# Adding new workloads

All workloads belonging to the teams are defined inside `modules/stacks/workload`
in the team specific sub module, e.g. Cloud Platform Team's modules are
defined in `modules/stacks/workload/cpt` sub module.

We support to main types of workloads:

- `web_app` - represents REST services, websites and full fledged web applications,
- `data_job` - KEDA jobs used in in the Data Pipeline.

For each type there is corresponding component that should be used when
adding new workload of given type. Components can be found in the `modules/components` directory.

## Step by step guide on adding new workload

The following describes how to add new workload of type `web_app` named `new_workload`.
Adding workloads for data jobs is virtually the same. In that case use `data_job` components
instead of `web_app`.

### Step 1 - create workload sub module

Inside team specific sub module create new sub module for the new workload:

```bash
mkdir -p modules/stacks/workload/<team>/<new_workload>
```

Each workload should have the following file structure:

- `main.tf` - definitions of all the resources,
- `variables.tf` - input variables definitions,
- `output.tf` - optional output variables.

### Step 2 - define common input variables

All workloads should accept some common variables, which are
used to pass values in the consistent way. Each workload
should define at least the following input variables:

```hcl
variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "team_configuration" {
  type = any
}

variable "kubernetes" {
  type = map(string)
}

variable "network" {
  type = any
}

variable "central_key_vault_id" {
  type = string
}

variable "persistence" {
  type = any
}

```

Types of those variables are generic on purpose. Actual types are defined at the top level.
You'll define them at later step.
Unless you have some specific use case, you won't have to customize this file.

To learn more about our approach to Terraform input variables see **Structuring Terraform input variables** in the `README.md`.

### Step 3 - basic main.tf setup

Each workload should have some common setup like provider definition, tags assigned for easier identification and kubernetes namespace. 
We're utilizing a tag hierarchy - sub modules should assign, possibly extended,
tags passed to them.

In the `main.tf` define `terraform` and `locals` block with `tags` variable and add team name:

```hcl
# REPLACE VALUES INSIDE <>

terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.<name_of_your_service>
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace = "<name_of_your_team>"
}
```

This variable will be passed wherever tags should be assigned.

### Step 4 - create resource group for the workload

Each workload requires some resources, that should be provisioned in the same
resource group. In the `main.tf` add it's definition:

```hcl
resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${var.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}
```

### Step 5 - instantiate `web_app` module for new workload

As mentioned at the beginning new workloads are added using modules.
In the `main.tf` add `web_app` module instance:

```hcl
# REPLACE VALUES INSIDE <>

module "<new_workload>_app" {
  source = "../../../../components/web_app"

  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  environment             = var.environment.name
  domain                  = var.environment.domain
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  argocd_notifications    = var.team_configuration.argocd_notifications
  resource_group_name     = azurerm_resource_group.resource_group.name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  env_dns_extension       = var.environment.dns_extension
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  tags                    = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART
  # is_private_webapp = true                   # true for VPN-only app on all envs
  # for the case of a publicly available app, check also if target environment is private
  # (all environments besides production are private)
  is_private_webapp = false || var.environment.is_private
  # use if `is_private_webapp = true`, see "Overriding Web Application Firewall rules"
  # for more detail about settings related to firewall policy
  firewall_policy = { mode = "Detection" }
  # required for public web apps
  private_routes_allowed_ips = [var.common.firewall_public_ip_address]

  # uncomment if you wish prometheus to scrap metrics from your service. Requires prometheus client
  # like https://github.com/trallnag/prometheus-fastapi-instrumentator
  # prometheus_monitor_enabled = true

  config = {}
  secret = {}
}
```


See `modules/components/web_app/variables.tf` for descriptions of the variables you're passing here.

For the most basic workloads that is enough, but most of them will require additional
configuration and/or resources to work.

### Step 6 - instantiate newly created workload module

Once sub module for the new workload is ready it can be instantiated in team's module.
Inside `modules/stacks/workload/<team>/main.tf` add module definition:

```hcl
# REPLACE VALUES INSIDE <>

module "<new_workload>" {
  source                 = "./<new_workload>"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on             = [kubernetes_namespace.team_ns]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
```

Each team sub module has some common variables like `common`, `environment`, `configuration`, `kubernetes` and
`central_key_vault_id`. Variables are already defined, so it's a case of just passing them to `new_workload` sub module.
Unless you have some specific use case, you won't have to customize this file.

### Step 7 - add top level input variables

Each workload's input variables are configured as a part of team configuration name
`team_configuration` in `modules/stacks/variables.tf`.
Variables for team specific workloads should have the following general structure:

```hcl
variable "team_configuration" {
  type = object({
    tags = map(string)
    argocd_notifications = map(string)

    <new-workload> = object({
      workload_name = string
      configuration = object({})
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })
  })
}
```

where:

- `workload_name` - is the name used for the workload - its resources, domains etc. -
  **can only use lowercase alphanumeric characters**,
- `configuration` - configuration values, that should be passed to the workload (excluding
  any value that comes from dependencies or is generated in the workload module),
  in case of workloads that don't have any such configurations this attributes can be omitted,
- `docker_image` - docker image configuration,
- `kubernetes` - Kubernetes deployment configuration - CPU/memory requests/limits,
- `tags` - team specific tags, that will be added to all workloads, unless you're adding
  first workload it should already be defined,
- `argocd_notifications` - Argo CD notification triggers and Teams channel names where notifications from Argo CD should be sent to. [More about Argo CD Notifications](https://gitlab.com/bluebrick/indiebi/infra/single-click-indiebi/-/blob/main/README.md?ref_type=heads#argo-cd-notifications). 

Let's say that `new_workload` belongs to DPT:

```hcl
variable "data_platform_team_configuration" {
  description = <<EOT
  # existing description

  New workload configuration:
  {
    configuration:
      some_config: "Some configuration for new workload"
  }
  EOT
  type = object({
    # other workloads
    new_workload = object({
      workload_name = string
      configuration = object({
        some_config  = number
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })
  })
}
```

Each workload should have its configuration attributes described. You can omit `workload_name`,
`docker_image` and `kubernetes` attributes from the description.

### Step 8 - add concrete configuration

Once defined new workload need to be configured for each environment. They are configured through
environment specific YAML file. In this example you'll configure `new_workload` in `dev` environment.
Configuration for this environment can be found in `deployments/dev/config.yaml`. Configuration
should look as follows:

```yaml
# other configuration

data_platform_team_configuration:
  # other CPT configurations


  # REPLACE VALUES INSIDE <>
  <new_workload>:
    workload_name: <new-workload>
    configuration:
      <some_config>: <some_value>
    docker_image:
      name: registry.azurecr.io/cpt/<new-workload>
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 150Mi
      limits:
        cpu: 200m
        memory: 300Mi

# more configs
```

### CONGRATS! 
At this point your basic config is ready!  
If you push and deploy your code the service should start working... and immediately fail since it is missing business specific configs, secrets, azure resources and permissions to those resources.  
So let's try to customize it so that it meets your specific needs.

### Step 9 - pass configuration values

Most workloads require additional configuration. It can be passed through `config` variable:

```hcl
module "new_workload_app" {
  source = "../../../../components/web_app"

  # same as before

  config = {
    ENV = var.environment.name
    HARDCODED_CONFIG = "hardcoded_value"
    CONFIG_PASSED_THROUGH_VARS = var.workload_configuration.configuration.some_config
  }

  tags = local.tags
}
```

As you can see config values can come from various sources:

- most commonly configuration values will come from `configuration` key of the `workload_configuration` variable,
- you're also free to pass values from other variables,
- some values can be hardcoded - if you know that it's unlikely to change,
- values can also come from outputs of provisioned resources - you'll see one such config at a later step.

### Step 10 - configure secrets

Some values should be kept secret and can't be passed directly. Workloads can request
secrets from the global Secret Store by name. The secrets on the cluster are synchronized with
Azure Key Vault automatically - all you need to do is make sure secret exists in Azure Key 
Vault and then pass the secret's name to `web_app` or `data_job` component.

There are several ways you might want to configure and pass the secret and you always
do it by instantiating the `key_vault_secret` component. Let's break down how can you
parametrize your new secret in that component:

 - **name** - name of the secret (on Azure and on the cluster)
 - **value** - secret value (can be skipped if it's an autogenerated secret or a placeholder value secret)
 - **content_type** - this can usually be skipped, only define if it's something
 other than plain text
 - **key_vault_id** - key_vault to store secret at, usually it should be central infra key_vault, contact CPT if you think it should be a separate one
 - **readers** - a list of Entra ID object IDs (usually teams but could be individual users,service principals or managed identities as well) that are able to read secret contents. In typical use case this should just be your own team, however sometimes other teams might want to have access to that secret as well.
 - **admins** - a list of Entra ID object IDs (usually teams but could be individual users,service principals or managed identities as well) that are able to manage (read/write etc) secret contents. In typical use case this should be the team leader of the team that owns the secret
 - **autogenerated** - if set to `true`, secret will be prefilled with autogenerated random value of length 128 and prefixed with `ibit-`
 - **placeholder** - if set to `true`, a placeholder value will be set that will never be updated by Terraform and should be set manually on Azure
 - **expiration_date** - secret's expiration date, as a general convention we use the same expiration date stored in the `var.common` object
 - **tags** - your team's tags


#### A. Secret is created and passed inside your workload file


```hcl
# your-workload.tf

locals {
  api_key_secret_name = "new-workload-api-key"
}

module "events_service_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  value           = "my-custom-selected-value"
  key_vault_id    = var.central_key_vault_id
  tags            = local.tags
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "app_using_events_service" {
  source = "../../../../components/web_app"

  # same as before

  secret = {
    YOUR_ENV_VAR_NAME = local.api_key_secret_name
  }

  tags = local.tags
}

```


#### B. Secret is autogenerated


```hcl
# your-workload.tf

locals {
  api_key_secret_name = "new-workload-api-key"
}

module "events_service_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  tags            = local.tags
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "app_using_events_service" {
  source = "../../../../components/web_app"

  # same as before

  secret = {
    YOUR_ENV_VAR_NAME = local.api_key_secret_name
  }

  tags = local.tags
}

```

#### C. Secret was created in another workload

```hcl
# your-workload.tf

locals {
  api_key_secret_name = "another-workload-secret-name"
}

module "app_using_events_service" {
  source = "../../../../components/web_app"

  # same as before

  secret = {
    YOUR_ENV_VAR_NAME = local.api_key_secret_name
  }

  tags = local.tags
}

```

#### D. Secret is created as placeholder that has to modified manually (since it comes from an external dependency not defined in Terraform)


```hcl
# your-workload.tf

locals {
  api_key_secret_name = "new-workload-api-key"
}

module "events_service_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  tags            = local.tags
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "app_using_events_service" {
  source = "../../../../components/web_app"

  # same as before

  secret = {
    YOUR_ENV_VAR_NAME = local.api_key_secret_name
  }

  tags = local.tags
}

```

### Step 11 - add additional resources

The example so far was mostly self-contained and didn't required any additional resources
(aside from Key Vault Secret). Most workloads requires some kind of storage or database.
This section shows how to add a database. The example below assumes that SQL Server is used
if you need to provision PostgreSQL resources the **Provisioning PostgreSQL users** section below.

All databases should be defined as part of the `persistence` stack. Adding new database requires
adding it the `config.yaml` of the environment:

```yaml
persistence_configuration:
  databases:
    - workload_name: <new_workload>
      sku_name: Basic
      max_size_gb: 5
```

This will provision new database named `db-new-workload`. Its information will be
available in `databases` output of the `persistence` module under `new_workload` key.

Next pass the database information to the `new_workload` module. To do this you'll need to define new variable in the `modules/stacks/workload/cpt/variables.tf`:

```hcl
variable "workload_database" {
  type = map(string)
}
```

Pass the value for this variable to the team's sub module in `modules/stacks/workload/cpt/main.tf`:

```hcl
module "cloud_platform_team" {
  source = "./workload/cpt"
  # other configurations
  workload_database = module.persistence.databases.new_workload
  # more_configurations
}
```

Next define new variables in `new_workload` module variables and pass it:

`modules/stacks/cpt/new_workload/variables.tf`

```hcl
variable "database" {
  type = string
}

# required for DB user provisioning
variable "sql_ad_admin" {
  type = map(string)
}
```

`modules/stacks/cpt/main.tf`

```hcl
module "new_workload" {
  source                 = "./new_workload"
  common                 = var.common
  environment            = var.environment
  workload_configuration = var.configuration.new_workload
  tags                   = var.configuration.tags
  kubernetes             = var.kubernetes
  central_key_vault_id   = var.central_key_vault_id
  database               = var.workload_database
  sql_ad_admin           = var.sql_ad_admin
  depends_on             = [kubernetes_namespace.team_ns]
}
```

Finally pass the configuration to the workload and provision SQL user:

```hcl
module "new_workload_app" {
  # rest stays the same

  config = {
    # other config values
    SQLALCHEMY_DATABASE_URL = "mssql+pyodbc:///?odbc_connect=Server=${var.database.host};Database=${var.database.name}"
  }

  # the rest of the config
}

module "database_provisioner" {
  source = "../../../../components/database_provisioner"
  database = var.database
  server_admin = {
    username = "${var.sql_ad_admin.app_id}@${var.common.tenant_id}"
    password = var.sql_ad_admin.password
  }
  admins = [
    {
      object_id = module.new_workload_app.identity.id
      name      = module.new_workload_app.identity.name
    },
    var.common.teams.team_name.team_leader
  ]
  writers = [var.common.teams.team_name.azure_group]
  readers = [var.common.service_principals.service]
}
```

### Step 11a - adding workload specific resources

In the above section you've added a database, which is defined in a different module
(stack even). Some workloads have dependencies that should be defined at workload
(or - in case of shared resources - team) module level.

#### Adding Storage Account

In this section you'll add an ephemeral storage account which is used only by `new_workload`.
In the `new_workload/main.tf`, create new storage account using `storage_account` components:

```hcl
module "workload_storage" {
  source               = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  name                 = "workload-storage"
  container_names      = ["workload-container"]
  is_public            = false
  environment          = var.environment.name
  private_dns_zone_ids = var.private_dns_zone_ids
  backup               = var.common.backup
  tags                 = local.tags
}
```

Add relevant config to the workload module:

```hcl
module "new_workload_app" {
  # everything stays the same as before
  config = {
    NEW_WORKLOAD_STORAGE_ACCOUNT = module.workload_storage.account_name
  }
}

New workload's pod will need access to the account. This can be achieved by adding
role assignment to the identity:

```hcl
resource "azurerm_role_assignment" "workload_storage_role_assignment" {
  scope                = module.workload_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.new_workload_app.identity.principal_id
}
```

This process is similar for other resources, also the ones that are not abstracted as components.

#### Adding custom DNS entries

In this section, you will create custom DNS and hostname for your workload. By default, DNS and hostname are created automatically and are based on workload name. So if you want to use those default DNS settings, skip this section. But if you want, you can create some custom DNS and hostname entries:

In `config.yaml` of the environment add all DNS entries you want to use for your workload:

```yaml
network:
  custom_dns_entries:
    - dns_name: new-workload
      hostname: new-workload.indiebi.dev
    - dns_name: new-workload-secondary
      hostname: new-workload-secondary.indiebi.dev
```

Next, add variables for your workload in `modules/stacks/variables.tf`:

```hcl
variable "team_configuration" {
  type = object({
    tags = map(string)
    argocd_notifications = map(string)
    <new-workload> = object({
    # everything stays the same as before
      network = object({
        custom_dns_entries = list(object({
          dns_name = string
          hostname = string
        }))
      })
```

And add `custom_dns_entries` parameter in your workload:

```hcl
module "new_workload_app" {
  # everything stays the same as before
custom_dns_entries = local.workload_configuration.network.custom_dns_entries
  }
```

## Full example

The following is a full example of new workload added in the previous steps:

`modules/stacks/cpt/new_workload/main.tf`

```hcl
locals {
  tags = merge(
    var.tags,
    {
      workload = var.workload_configuration.workload_name
    }
  )
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${var.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "random_password" "api_key" {
  length  = 123
  special = false

  keepers = {
    expiration_date = var.common.keys_secrets_expiration_date
  }
}

locals {
  api_key_secret_name = "new-workload-api-key"
}

resource "azurerm_key_vault_secret" "api_key" {
  name         = local.api_key_secret_name
  value        = "ibit-${random_password.api_key.result}"
  key_vault_id = var.central_key_vault_id
  content_type = "text/plain"
  tags         = local.tags
}

locals {
  placeholder_secret_value = "IM_A_PLACEHOLDER_CHANGE_ME"
  external_secret_name = "external-secret"
}

resource "azurerm_key_vault_secret" "external_workload_secret" {
  name         = local.external_secret_name
  value        = local.placeholder_secret_value
  key_vault_id = var.central_key_vault_id
  content_type = "text/plain"
  tags         = var.configuration.tags

  lifecycle {
    ignore_changes = [value]
  }
}

locals {
  dependency_secret_name = "dependency-secret"
}

module "workload_storage" {
  source               = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  name                 = "workload-storage"
  container_names      = ["workload-container"]
  is_public            = false
  environment          = var.environment.name
  backup               = var.common.backup
  private_dns_zone_ids = var.private_dns_zone_ids
  tags                 = local.tags
}

module "new_workload_app" {
  source = "../../../../components/web_app"

  name                    = var.workload_configuration.workload_name
  kubernetes_namespace    = "cpt"
  environment             = var.environment.name
  domain                  = var.environment.domain
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  argocd_notifications    = var.team_configuration.argocd_notifications
  resource_group_name     = azurerm_resource_group.resource_group.name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url

  docker_image = var.workload_configuration.docker_image
  kubernetes   = var.workload_configuration.kubernetes

  custom_dns_entries = local.workload_configuration.network.custom_dns_entries

  config = {
    ENV = var.environment.name
    HARDCODED_CONFIG             = "hardcoded_value"
    CONFIG_PASSED_THROUGH_VARS   = var.workload_configuration.configuration.some_config
    SQLALCHEMY_DATABASE_URL      = "mssql+pyodbc:///?odbc_connect=Server=${var.database.host};Database=${var.database.name}"
    NEW_WORKLOAD_STORAGE_ACCOUNT = module.workload_storage.account_name
  }

  secret = {
    API_KEY           = local.api_key_secret_name
    DEPENDENCY_SECRET = local.dependency_secret_name
    EXTERNAL_SECRET   = local.external_secret_name
  }

  tags = local.tags
}

module "database_provisioner" {
  source = "../../../../components/database_provisioner"
  database = var.database
  server_admin = {
    username = "${var.sql_ad_admin.app_id}@${var.common.tenant_id}"
    password = var.sql_ad_admin.password
  }
  admins = [
    {
      object_id = module.new_workload_app.identity.id
      name      = module.new_workload_app.identity.name
    },
    var.common.teams.team_name.team_leader
  ]
  writers = [var.common.teams.team_name.azure_group]
  readers = [var.common.service_principals.service]
}

resource "azurerm_role_assignment" "workload_storage_role_assignment" {
  scope                = module.workload_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.new_workload_app.identity.principal_id
}
```

`modules/stacks/cpt/new_workload/variables.tf`

```hcl
variable "common" {
  type = map(any)
}

variable "environment" {
  type = map(any)
}

variable "workload_configuration" {
  type = any
}

variable "kubernetes" {
  type = any
}

variable "tags" {
  type = map(string)
}

variable "central_key_vault_id" {
  type = string
}

variable "database" {
  type = string
}

# required for DB user provisioning
variable "sql_ad_admin" {
  type = map(string)
}
```

`modules/stacks/workload/cpt/main.tf`

```hcl
module "new_workload" {
  source                 = "./new_workload"
  common                 = var.common
  environment            = var.environment
  workload_configuration = var.configuration.new_workload
  tags                   = var.configuration.tags
  kubernetes             = var.kubernetes
  central_key_vault_id   = var.central_key_vault_id
  sql_ad_admin           = var.sql_ad_admin
  workload_database      = module.persistence.workload_db
  
  depends_on             = [kubernetes_namespace.team_ns]
}
```

`modules/stacks/persistence/main.tf`

```hcl
module "workload_db" {
  source                 = "../../components/sqldb"
  workload_name          = "workload-db-server"
  tenant_id              = var.common.tenant_id
  admin_contact_email    = var.common.admin_contact_email
  environment            = var.environment.name
  sql_ad_admin           = var.sql_ad_admin
  tags                   = var.tags
  key_vault_id           = azurerm_key_vault.keyvault.id

  depends_on = [azurerm_role_assignment.keyvault_user]
}
```

`modules/stacks/persistence/output.tf`

```hcl
output "workload_db" {
  value = {
    name           = module.workload_db.name
    host           = module.workload_db.host
    admin_password = module.workload_db.admin_password
  }
}
```

`modules/stacks/workload/cpt/variables.tf`

```hcl
variable "workload_database" {
  type = map(string)
}
```

`modules/stacks/main.tf`

```hcl
module "cloud_platform_team" {
  source = "./workload/cpt"
  # other configurations
  workload_database = module.persistence.workload_db
  # more_configurations
}
```

`modules/stacks/variables.tf`

```hcl
variable "cloud_platform_team_configuration" {
  description = <<EOT
  # existing description

  New workload configuration:
  {
    configuration:
      some_config: "Some configuration for new workload"
  }
  EOT
  type = object({
    # other workloads
    new_workload = object({
      workload_name = string
      configuration = object({
        some_config  = number
      })
      network = object({
        custom_dns_entries = list(object({
          dns_name = string
          hostname = string
        }))
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })
  })
}
```

`deployments/dev/config.yaml`

```yaml
# other configuration
argocd_configuration:
  # other Argo CD configuration
  argocd_notification_channels:
    cpt-argo-cd: "https://indiebisa.webhook.office.com/webhookb2/..."
    cpt-errors: "https://indiebisa.webhook.office.com/webhookb2/..."
# other configuration


cloud_platform_team_configuration:
  # other CPT configurations

  argocd_notifications:
    sync-succeeded.teams: argo-cd
    sync-running.teams: argo-cd
    sync-failed.teams: errors
    sync-status-unknown.teams: 
    created.teams: argo-cd
    deleted.teams: argo-cd
    deployed.teams: argo-cd
    health-degraded.teams: errors

  new_workload:
    workload_name: new-workload
    configuration:
      some_config: 42
    network:
      custom_dns_entries:
        - dns_name: new-workload
          hostname: new-workload.indiebi.dev
        - dns_name: new-workload-secondary
          hostname: new-workload-secondary.indiebi.dev
    docker_image:
      name: registry.azurecr.io/cpt/<new-workload>
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 150Mi
      limits:
        cpu: 200m
        memory: 300Mi

# more configs
```

## Provisioning PostgreSQL database users

To provision users in PostgreSQL database use `postgresql_provisioner`:

```hcl
module "database_provisioner" {
  source = "../../../../components/postgresql_provisioner"
  database = {
    name = local.database_name
    host = local.database_server_fqdn
  }
  server_admin = var.persistence.postgresql_servers.postgresql_server.server_admin
  entra_admin  = var.common.teams.cpt.runner_identity
  admins = [
    {
      object_id = module.new_workload_app.identity.id
      name      = module.new_workload_app.identity.name
    },
    var.common.teams.cpt.team_leader
  ]
  entra_users = [var.common.teams.cpt.azure_group, { object_id = var.backup_manager.principal_id, name = var.backup_manager.name }]
}
```

The most relevant variables are:
- `admins` variable is a list of Entra ID users/groups/service principals that will be granted ownership
over the schema used by application,
- `entra_users` is a list of Entra ID users that will be imported to the server, these users don't get
any grants, but need to be imported into PostgreSQL server, so web apps can grant them privileges on tables.

## Adding complex values to workload configuration

In most cases values passed to the workload configuration are simple,
e.g. strings, integers etc., but it some cases we need to pass more complex
values e.g. lists or objects that will be parsed as JSON by the application.
When passing such values it must be ensured that they're passed as properly
escaped strings.

When passing JSON lists all quotes need to be escaped, e.g.:

```yaml
some_config: '[{\"key\": \"value\", \"other_key\": 12}]'
```

## Overriding Web Application Firewall rules

Our web applications are configured with Web Application Firewall turned on.
Default firewall policy prevents suspicious requests being sent to the service.
In case of private applications, since they are hidden behind VPN, this policy
can be relaxed and set to `Detection`:

```hcl
module "new_workload_app" {
  source = "../../../../components/web_app"

  name              = var.workload_configuration.workload_name
  is_private_webapp = true
  firewall_policy   = { mode = "Detection" }

  # the rest of the configuration
}
```

For public applications this mode should stay set to `Prevention` (default). In some situations
firewall can block legitimate requests (gateway will return 403 status code).
In such cases offending rules can overridden and firewall will be instructed
to not block requests that trigger the rule. This can be achieved by passing the name(s)
of problematic rule group(s) and list of IDs to override, e.g.:

```hcl
module "new_workload_app" {
  source = "../../../../components/web_app"

  name              = var.workload_configuration.workload_name
  is_private_webapp = false
  firewall_policy   = { 
    overrides = [
      {
        rule_group_name = "REQUEST-931-APPLICATION-ATTACK-RFI"
        rule_ids = ["931130"]
      },
      {
        rule_group_name = "REQUEST-942-APPLICATION-ATTACK-SQLI"
        rule_ids = ["942200", "942370"]
      }
    ]
  }

  # the rest of the configuration
}
```

This feature should be used sparingly and overrides should be limited to absolute minimum.
It's always better to change the endpoint in a way that no legitimate
request should trip it.

## Turning off automatic configuration reloading

Automatic configuration reloading is turned on by default. If this is undesired behavior the mechanism can be disabled by setting `automatic_configuration_reload_enabled` to false:

```hcl
module "new_workload_app" {
  source = "../../../../components/web_app"

  name                                   = var.workload_configuration.workload_name
  automatic_configuration_reload_enabled = false

  # the rest of the configuration
}
```

## Configuring Horizontal Pod Autoscaler

**You don't need this. No really, fix you workload and/or set static number of replicas.**

Web apps support basic configuration for Horizontal Pod Autoscaler (HPA). It can be configured by passing `hpa` setting:

```hcl
module "new_workload_app" {
  source = "../../../../components/web_app"

  name = var.workload_configuration.workload_name

  hpa = {
    min_replicas = 1
    max_replicas = 2
    metrics = [{
      resource = "cpu"
      target   = 80
    }]
  }
  # the rest of the configuration
}
```

The configuration above will configure HPA to scale the workload to up to 2 replicas when CPU usage reaches 80% of configured CPU request.

Currently only `cpu` and `memory` resources are supported. You can define more that one metric.

Note that `hpa` setting takes precedence over `replicas` setting. If `hpa` is set `replicas` will be ignored.

For more information refer to [official documentation](https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/).

## Hiding selected endpoints behind VPN

**Additionally service's domain needs to be forced through VPN, this can be configured in `azure-core`**

To hide selected endpoints behind VPN pass `firewall_policy` in `Prevention` mode (the default), `private_routes` and `firewall_public_ip_address` parameters:

```hcl
module "new_workload_app" {
  source = "../../../../components/web_app"

  private_routes             = ["/hideme"]
  private_routes_allowed_ips = [var.common.firewall_public_ip_address]

  # the rest of the configuration
}
```

`private_routes` supports regular expressions, passing `/hideme` blocks `/hideme` and everything
under it, e.g. `/hideme/secret`. If this behaviour is undesirable pass `/hideme$` regex, which will
block only `/hideme`.

By default `/docs`, `/openapi.json` and `/health$` endpoints are hidden, they don't need to be provided.