## Argo CD FAQ

**Q: Does sync option causes pod to reload?**

A: Yes. Changing configuration values or secrets in key vault reloads the pod.

**Q: How log does it take Argo CD to pick up new version of the image?**

A: Up to 30 seconds.

**Q: How log does it take Argo CD to pick up changes in the application?**

A: Up to 3 minutes.

**Q: How long does it take to secret updated in key vault to be synced in cluster?**

A: Up to 5 minutes.

**Q: Can deployment be deleted?**

A: Technically it can, but it shouldn't be ever required. The one situation when
it might be necessary is when Kubernetes is unable to schedule new deployment.
In that situation old one can be deleted to fit new one. 

**Q: If we need new image SHA, can we reuse tag's previously deployed image?**

A: Yes, it just checks if there is any image tag's SHA changed.

**Q: What is version displayed in app view next to SYNC STATUS and LAST SYNC fields?**

A: This is version of Helm Chart and it is shared by all workloads.

**Q: Can we see history/logs of running jobs?**

A: Yes, but only when they are running, so usually it will be a short period of time.