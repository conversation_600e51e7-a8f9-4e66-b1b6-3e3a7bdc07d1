# Prometheus/Grafana provisioning


## Provisioning data sources

Data sources should be added in `modules/stacks/compute/k8s/prometheus/prometheus-overrides.yaml.tpl` file.

For details see the [official documentation](https://grafana.com/docs/grafana/latest/administration/provisioning/#data-sources).

## Provisioning dashboards

Dashboards are provisioned as a part of Prometheus and Grafana deployment and are stored in the 
`modules/stacks/compute/k8s/prometheus/dashboards` folder.
Dashboards are provisioned into Grafana folders based on the dashboard directory structure.
Each directory will have a corresponding Grafana folder created. Provisioning dashboards
to root folder is **unsupported**.

Every dashboard is stored in a separate JSON file. The easiest way to create these files is 
to create dashboard in Grafana using UI and then copy the JSON model into a file.

### Dashboards that use Azure Monitor data source

Extra care needs to be taken when provisioning dashboards that use Azure Monitor data source.
Since Azure resources depend on subscription ID, resource group and resource name, all of which
are environment dependent, it's important to pick resources using template variables.
Variables should be defined as query variables with optional regex filters (e.g. to narrow down 
resource group name), then during resource select in query use defined variables.

Example configuration would look like this:

1. Define `subscription`, `resource_group`, `namespace` and `resource` variables  in dashboard settings.
  Variables can be hidden, each variable should be defined in terms of previous variables,
  in case of `resource` variable:
  ![Template variable for Azure Monitor](images/grafana_azure_monitor_01.png "Template variable for Azure Monitor")

2. When selecting resource from data source use variables instead of hardcoded values:
  ![Choosing resource based on variables](images/grafana_azure_monitor_02.png "Choosing resource based on variables")

For more information see official [documentation](https://grafana.com/docs/grafana/latest/datasources/azure-monitor/template-variables/).

NOTE: when defining `subscription` variable for environment specific dashboards you should filter out `app-desktop` subscription
using the following regex `/^(?!5d37e76e-270f-4dc0-939f-d667a5fb1f57).*/`.

If you want your Dashboards to access subscriptions that are not related to Single Click (e.g. you want to display dashboards for 
desktop application resources that are stored in an `app-desktop` subscription), you have to manually assign a Monitoring Reader for
Grafana Managed Identity on that subscription. Automating it with terraform would require SCI runners to have Owner access on non SCI
subscriptions, which is a violation of Principle of Least Privilege.

### Adding provisioned dashboards

Adding provisioned dashboard consists of the following steps:

1. Create dashboard in the Grafana UI.
2. Create a folder under `modules/stacks/compute/k8s/prometheus/dashboards` (or choose existing one)
   and save JSON file.
3. We also recommend changing UID to UUID4 (this is optional).

### Updating provisioned dashboard

Updating provisioned dashboard consists of the following steps:

1. Make a copy of dashboard in Grafana - *Dashboard settings* -> *Save as*.
2. Modify the copy as you wish and save the changes.
3. Copy JSON - *Dashboard settings* -> *JSON Model* - to dashboard's file.
4. Change `id`, `uid` and `title` in the copied JSON to the original ones.
5. Plan and apply with Terragrunt.
6. Once you are satisfied with the result remove the copy.

**NOTE: Changing the name of/removing the folder will deprovision dashboards from the old folder. Deleting folder from the repository will not remove it from Grafana.** 

### Alerting

This repository is responsible for provisioning contact points and notification policy for Grafana alertmanager.
Alert rules and notification templates are meant to be added manually.

To add new contact point add new entry to the 
`prometheus_configuration.grafana.alerting.<team_name>.contact_points` 
map in `config.yaml` for each deployment.
Currently only Teams webhooks are supported.

To remove provisioned contact point add the following to Prometheus overrides under `grafana` key:

```yaml
alerting:
  contactpoints.yaml:
    apiVersion: 1
    deleteContactPoints:
    - orgId: 1
      uid: contact-point-uid
```

Similar to reset the policy:

```yaml
policies.yaml:
  apiVersion: 1
  resetPolicies:
    - 1
```