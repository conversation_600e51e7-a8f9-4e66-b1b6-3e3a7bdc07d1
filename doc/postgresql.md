# Postgresql


## Adding new postgresql server

All databases should be defined as part of the persistence stack. 
Adding new database requires adding it the config.yaml of the environment:

```
postgresql:
- server_name: "<server_name>"
    max_size_mb: 32768
    storage_tier: "P4"
    sku_name: "B_Standard_B1ms"
    databases:
    - workload_name: "<database_name>"
```

## Adding new postgresql database to an existing server

To add a new database simply extend list of databases:

```
postgresql:
- server_name: "some_server"
    max_size_mb: 32768
    storage_tier: "P4"
    sku_name: "B_Standard_B1ms"
    databases:
    - workload_name: "some_database"
    - workload_name: "new_database"
```

## Adding new postgresql database vs adding new postgresql server

All database within a psql server share the same resources. Deploying multiple 
databases on a single server will be cheaper but might lead to noisy neighbor.
In the first implementation we will try to deploy all databases on a single 
server.