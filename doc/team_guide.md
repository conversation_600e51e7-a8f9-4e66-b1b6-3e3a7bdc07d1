# Single-Click IndieBI: Team Guide

## Introduction

Each team is assigned their own directory inside `modules/stacks/workload`, referred to later as *team's directory*. 

## Basic rules

 - Your team owns and is responsible for changes related to workload configuration (what services are deployed, how are they configured, what additional resources do they need etc.)
 - CPT might periodically introduce maintenance changes like refactors, RBAC adjustments, helm chart updates etc., however typically not changes that would affect any clients / business logic of IndieBI
 - Only your team and CPT should change given team's configuration. If you feel the need to make changes outside of your team's directory, please consult CPT

## How to work with Terraform configs

Since as dev team you should not be forced to make changes to this configuration often, the preferred way of testing and introducing changes should be through Gitlab CI. If you need to do bigger work that would make going through CI tedious, please consult CPT first. The workflow is fairly standard:

1. Create a new branch.
2. Introduce changes.
3. Open a MR for CPT (you can leave it unassigned).
4. Run `validate`, `plan`, etc, all steps except `apply`.
5. Post MR on CPT team channel.
6. Once approved, `apply` TF changes on dev. 
7. Merge changes.
8. Run `apply` on main branch on production. 

Please be mindful whether any other pipeline (especially one triggered by another team) might still be in progress. Since we are using a single state for every env, no more than one Terraform job (`plan`, `apply`) can run at once, since the state will get locked. For the same reason we need to pay attention to what is proposed by the `plan` stage to avoid inadvertently dropping someone else's configuration that landed on the target env.  