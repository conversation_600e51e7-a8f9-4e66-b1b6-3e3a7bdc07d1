# Adding alerts in Grafana

The following document provides description of steps necessary for adding
new alert rules in Grafana.

## Adding alerts to panels

The easiest way to add alert is to add it to existing panel.
This way you’ll be able to see alert related annotations on the panel,
additionally default alert message will contain links to the dashboard
and panel.

1. Go to the dashboard of interest, select a panel from the select
*New alert rule* from panel's menu (three vertical dots in the top
right corner)

    ![App Gateway backend pool](images/grafana_add_alert_rule_01.png "Add alert rule")

2. Name your alert.

3. Configure query and alert condition. If your panel contains
multiple queries you might need to adjust the query by removing
or modifying the queries. Once done you can click *Preview* to evaluate
the condition.

4. Configure evaluation behavior - choose a folder to store a rule in
(in most cases you'll want to keep the default). Next create new or
choose existing evaluation group (evaluation groups are tied to 
folders, if you don't see the group that should exist try changing the folder).
Under *Configure no data and error handling* you can configure behavior
when there is no data or data source errors out, e.g. you might want to
ignore data source connection errors by choosing *OK* or *Keep Last State*
from *Alert state if execution error or timeout* drop down.

5. Configure labels and annotation, which will determine how your alert is routed.
Currently we have a simple notification policy configuration that routes the alerts
based on `team` label. Each team has a single contact point configured with
with notification policy that routes alerts based on short team name,
i.e. `cpt`, `dpt`, `saas`.
**You need to add correct `team` label for your alert to be routed.**
You're free to add any other labels.

    **NOTE**: you can preview routing by clicking *Preview routing*, but this feature
    seems to be glitched and works only if the value is enclosed in double quotes, e.g. `"cpt"`. Routing works fine without the quotes, you don't need to add quotes.

6. Finally you can add additional annotation and URL and save the rule with 
*Save rule and exit*.

## Adding alert without panels

Sometimes you might want to have an alert not related to any panels. You can add
such and alert by going from the main menu to *Alert rules* and clicking *+ New alert rule*.

![App Gateway backend pool](images/grafana_add_alert_rule_02.png "Add alert rule without panel")

The rest of the process is the same as described above.

## Mute timings

Mute intervals are provisioned from this repository. To add a mute interval go to
environment config (e.g. `deployments/prod/config.yaml`) and desired configuration
under `prometheus_configuration.grafana.alerting.<team_name>.mute_times` and
`prometheus_configuration.grafana.alerting.<team_name>.custom_policies`, e.g.:

```yaml
prometheus_configuration:
  grafana:
    alerting:
      cpt:
        mute_times:
          mute_intruder_requests:
            - times:
                - start_time: '01:00'
                  end_time: '04:00'
              location: 'Europe/Warsaw'
        custom_policies:
          - matchers:
              - intruder = true
            mute_time_intervals:
              - mute_intruder_requests
```

The above adds a mute time between 1AM and 4AM CEST.
Every alert labeled with `intruder = true` will be muted during that time.

## Links

[Official documentation on adding alert rules](https://grafana.com/docs/grafana/v10.4/alerting/alerting-rules/create-grafana-managed-rule/)
[Official documentation on mute timings](https://grafana.com/docs/grafana/v10.4/alerting/manage-notifications/mute-timings/)