# Single click IndieBI

Repository for recreating full IndieBI's azure + k8s resources with a "single click".

## I'm NOT from CPT and I just want to add new workload

### Adding new workloads

Guide on adding new workloads can be found in [Adding new workloads](doc/adding_workloads.md).

### Adding Grafana alerts

Guide on adding Grafana alerts can be found in [Adding alerts in Grafana](doc/adding_alerts_in_grafana.md).

### Intro guide for dev teams

If you are just starting to work with Single-Click IndieBI, please refer to [Team Guide](doc/team_guide.md).

## Prerequisites

### Service Principal and Managed Identity for SQL Server
Single click IndieBI require dedicated service principal and managed identity with read access to AAD (those are required to set up Azure sql server).
Most likely you DON'T want to set up those on your own. Instead ask your team members for credentials to already existing resources.
If you really have to create new service principal and new managed identity, here are the steps:    

Service principal:
1. Create new service principal
    ```
    AAD > Add > App registration
    ```
2. Assign the following permissions to that principal: `GroupMember.Read.All` - `User.Read.All`, `Application.Read.All` permissions (requires Admin permissions)
    ```
    AAD > find the sp > API permissions > Add a permission > Microsoft Graph > Application permissions > select all listed above
    ```
3. Grant admin consent (requires admin permissions)
    ```
    AAD > find the sp > API permissions > Grant admin consent for IndieBI
    ```
3. Generate secret
    ```
    AAD > find the sp > Certificates & secrets > New client secret
    ```

Managed identity:
1. Create if doesn't exist: resource group `rg-bootstrap`
2. Inside that resource group create a Managed Identity
3. Assign Directory Reader role to that managed identity (https://portal.azure.com/#view/Microsoft_AAD_IAM/RoleMenuBlade/~/RoleMembers/objectId/88d8e3e3-8f55-4a1e-953a-9b9898b8876b/roleName/Directory%20Readers/roleTemplateId/88d8e3e3-8f55-4a1e-953a-9b9898b8876b/adminUnitObjectId//customRole~/false/resourceScope/%2F)  
Note: Instead of assigning Directory Reader role we would rather assing more granular accesses (`GroupMember.Read.All` - `User.Read.All`, `Application.Read.All`). However, at the time of writing this doc, it's not possible to do it for managed identities through portal. Based on initial research it seems that it should be possible to do it via powershell, but we decided to postpone further investigations and focus on more crucial things.

Once created the `SQL_AD_ADMIN_PASSWORD` environment variable needs to be exported. Other
variables need to be configured through `config.yaml`.

### External dependencies configuration

Here is a list of secret values that are created as placeholders and need to be
manually overwritten for the workloads to work:

Landing page:							
 - `landing-google-tag-manager-id` - id for google tag manager
 - `landing-google-tag-manager-query-params` - additional query params (stringified json) necessary for dev environment in google tag manager

Electron API:
 - `PBI_DEFS_CONNECTION_STRING` - URL needed to connect with AAS and update cache
 - `ELECTRON_STORE_CONNECTION_STRING` - URL needed to connect with Electron Store 
   and update cache

Dataset Manager:
  - `PBI_AZURE_CLIENT_SECRET` - Service Principal used to access Power BI

Cloud Scraper:
  - `EPIC_ACCOUNT_ID` - ID of the EPIC account to scrape
  - `EPIC_DEVICE_ID` - ID of the EPIC device
  - `EPIC_OAUTH_TOKEN` - OAUTH token used for authentication
  - `EPIC_SECRET` - EPIC secret

### Dependencies

- [sqlcmd](https://github.com/microsoft/go-sqlcmd) for provisioning database users. Remember to use sqlcmd from that link and not one supplied with mssql-tools.
  It's also available from the [official Microsoft apt repository ](https://learn.microsoft.com/en-us/linux/packages). Assuming Ubuntu: `sudo apt install sqlcmd`.
- **terragrunt** wrapper for Terraform to manage multiple Terraform configs that we use. Install by running: 

    ```bash
    wget https://github.com/gruntwork-io/terragrunt/releases/download/v0.48.1/terragrunt_linux_amd64 -O terragrunt
    chmod u+x terragrunt
    sudo mv terragrunt /usr/local/bin/terragrunt
    ```

    You might have to switch binary to match your machine's architecture if you're running on something other than Ubuntu, you can look up releases for different platforms [here](https://github.com/gruntwork-io/terragrunt/releases).

## How to work with this repo

### Repository structure

Repository is divided in two directories:

- `modules` - contains Terraform definitions for the deployed infrastructure, it's further divided into:
  * `components` - generic components that can be used while building "stacks",
  * `stacks` - actual infrastructure definitions divided into three "stacks": `compute` (everything network and Kubernetes related), 
    `persistence` (databases, storage accounts etc.) and `workload` (team-specific workloads);
- `deployments` - contains environment specific configuration, each environment settings are stored in a separate directory in a `config.yaml`
  file that overrides global settings defined in `deployments/config.yaml`.

To create new environment:

1. Create a new directory inside deployments.
2. Inside create a minimal `terragrunt.hcl`:
   
    ```hcl
    include "root" {
      path = find_in_parent_folders("root.hcl")
    }
    ```

3. Customize settings using `config.yaml`.
4. Provide real values for all secrets that are created with placeholder `IM_A_PLACEHOLDER_CHANGE_ME` value.

### Structuring Terraform input variables

Terraform input variables can be defined at several levels:

* top level - variables passed directly through `config.yaml` files,
* sub module level - variables passed to/between sub modules that represent stacks or services, e.g: `k8s`, `aks`, `user-service`, etc.,
* component level - variables passed to reusable components, e.g.: `data_job`, `sqldb` etc..

#### General rules

Variables should have descriptive names. It should be obvious what each variable represents.

#### Top level input variables

Variables, wherever possible, should be passed as objects/maps that group related variables (objects are preferred since they contain type information, but sometimes it might be impractical). 

Variables that are common to all environments should be a part of common object.

Variables defined at the top level should always have a description that specifies its purpose in a clear way.

Variables at the top level should specify default values sparingly. Default values should only be used when there is low probability of it changing, but we still want to have an ability to modify it from time to time. Variables that change between environments should never have default values.

Variables should specify validation rules with descriptive error message. Not every variable needs validation. Validation should be used to guide user when the input value can be ambiguous, e.g. depending on the Azure resource “ID” can be in the form of UUID, name or a “long” ID in the form `/subscription/********-0000-0000-0000-********0000/.../********-0000-0000-0000-********0000`. Additionally values that have limitations imposed by Azure should be validated, e.g. some resource names can only use lowercase alphanumeric values.

#### Sub module input variables

Variables passed from higher levels can omit descriptions and validation as long as the name is not changed.

Variables, wherever possible, should be passed as objects/maps that group related variables (objects are preferred since they contain type information, but sometimes it might be impractical).

Object/map passed from higher level should be passed through generic map e.g. given top level variable `network_configuration` defined as:

```
variable "network_configuration" {
  description = "Network configuration"
  type = object({
    vnet_id             = string
    primary_subnet_id   = string
    secondary_subnet_id = string
  })
}
```

Sub module should define generic map as a type:

```
variable "network_configuration" {
  type = map(any)
}
```

In case of complex types that mix attributes of different types use generic type (not map), e.g.:

```
variable "complex_configuration" {
  type = object({
    string_value = string,
    object_value = object({
      first_value = string
      second_value = number
    })
    some_flag = bool
  })
}
```

Sub module should use `any`:

```
variable "complex_configuration" {
  type = any
}
```

#### Component level input variables

Components should prefer flat variable structure and use objects/maps only for truly related, i.e. they should not accept object like `common`, instead only specific attributes should be accepted. This keeps components abstract, so they can be used as building blocks outside of `single-click-indiebi`. This also avoids propagating changes from higher levels to the components.

Variables should specify validation rules with descriptive error message.

Variables should have a description that specifies its purpose in a clear way.

Nullable values should be disallowed, unless component knows how to handle it.

#### References

[Official input variables documentation](https://developer.hashicorp.com/terraform/language/values/variables)

### Local developemnt

**The preferred way to deploy is to go through Gitlab CI.**. In case you need to test something locally:


1. Make sure noone else is working on the same environment
1. Connect to VPN
1. Change to azure subscription which contains terraform state
    ```
    az login
    az account set --subscription app-dev
    ```
1. Set env vars
    ```
    export TERRAFORM_BACKEND_STORAGE=satfstateindiebidevndbi
    # for prod: satfstateindiebiprod
    export SQL_AD_ADMIN_PASSWORD=<same as gitlab sp secret>
    export GITLAB_ACCESS_TOKEN=<gitlab-personal-access-token>
    ```
1. Go to appropriate env config folder
    ```
    cd deployments/dev
    ```
1. Initialize terragrunt
    ```
    terragrunt init
    ```
1. Validate your config:
    ```
    terragrunt plan
    ```
1. Plan changes   

    ```bash
    terragrunt plan -out plan.tfplan
    ```
    If you just want to run `plan`, you can try to use plan with `-refresh=False`. This skips refreshing Terraform state before checking for configuration changes.
    You should **not** apply such plan though!
    ```bash
    terragrunt plan -refresh=false 
    ```
1. In rare cases, when you need to quickly iterate locally, run:

   ```bash
   terragrunt apply plan.tfplan
   ```
1. Make sure your changes comply to security policies (`--quiet` displays only failed checks).
   
   Execute in the root of the repository:

    ```bash
    docker run \
        --volume $(pwd):/tf \
        bridgecrew/checkov:3 \
            --directory /tf \
            --framework dockerfile kubernetes terraform \
            --quiet
    ```

## Deployment

Every deploy environment (dev/prod/whatever comes next) has a child pipeline configured that contains all Terraform jobs needed to run deployment. To configure a new environment, add a job spawning a new child pipeline for this environment in top level `.gitlab-ci.yml`:

```
new-env:
  extends: .env-pipeline
  variables:
    ENV: new-env2 # name of the directory and env used in Azure resources
    TF_AUTO_DEPLOY: "false" # flag making apply manual or automatic
    ARM_TENANT_ID: "$AZURE_TENANT_ID" # azure credentials for TF
    ARM_CLIENT_ID: "$AZURE_DEV_SUB_CLIENT_ID"
    ARM_CLIENT_SECRET: "$AZURE_DEV_SUB_CLIENT_SECRET"
    ARM_SUBSCRIPTION_ID: 94d8e615-83f8-4a81-9015-85747c64bc3a
  rules: !reference [.mr-or-main, rules]
```

As a last manual step, it is also necessary to add the public IP of the newly created AKS cluster's API server to `ips_forced_through_vpn` list in azure core's `indiebi.tfvars` to allow access through new VPN.

##  Argo CD RBAC

Only users that belong to groups assigned to Argo CD service principal can login.

### Roles

Roles are defined in the `policy/roles.csv` file in the `argocd_core`` module and can be divided into two types:

- global: 
  * `no-access` - default, deny access to everything,
  * `org-admin` - full access to everything,
  * `org-reader` - read-only access to all applications, projects and logs, permission to sync application;

- team-specific:
  * `team-admin` - full access to team’s workloads,
  * `team-reader` - read-only access to team’s workloads, also ability to manually sync;

where `team` is a team’s shorthand, e.g.: `cpt`, `dpt`, etc..

Team roles are designed to grant permissions at project level. All workloads belonging to a team are assigned to the same project. Projects are named after team’s shorthand name, which is also the case for the namespaces. For example: Cloud Platform Team’s projects are assigned to cpt project and allow deployments only to the cpt namespace. Additionally Argo CD projects have roles attribute, which can be used for access control, but it’s not utilized.

### Assignments

Roles are assigned to Microsoft Entra ID groups/users. Those assignments are environment-specific. By default (more restrictive) assignments for production environment are deployed.

Assignments are defined in the `policy/policy_{suffix}.csv` in the `argocd_core` module, where suffix is one of: `strict` or `relaxed`. Suffix can be configured through `argocd_rbac_policy` terraform variable, if not provided `strict` policy will be used.

Assigning role to user or group

Modify `policy_*.csv` files by adding relevant record, e.g.: `g, ********-1111-2222-3333-************`, `role:org-reader` will assign org-reader role to user/group with UUID `********-1111-2222-3333-************`.

Once the role is assigned, adding/removing access is a matter of assigning user to the appropriate group through the Azure portal.

Adding single user always requires changes in CSV files, hence it’s preferred to use groups.

## Argo CD Notifications

Argo CD has implemented notifications feature, which can send notifications about application deployment status or events to specified service (in our case - Teams). It uses triggers and templates system, where the first one indicates when notification should be sent and the second one what content notification should contain.

In Single Click IndieBI we allow teams to use any Argo CD built-in notification and send those notifications to any Teams channel.

To set this up, in `config.yaml` of the environment add:
```hcl
argocd_configuration:
  # everything stays the same as before
    <team_short_name>-<name>: "<your_channel_webhook_url>"
    
<your_team_configuration>:
  # other configuration
  argocd_notifications:
    <notification_trigger>: <your_channel_name>
```
`<name>` can be any string, but usually it will be team's channel name, which is prefixed by team's short name to avoid duplications with other team's channel names.
`<notification_trigger>` defines the condition when the notification should be sent.

Each trigger triggers specified notification template, and they are defined in `modules/stacks/compute/argocd_core/argocd-overrides.yaml.tpl`. More about triggers and templates in [documentation](https://argocd-notifications.readthedocs.io/en/stable/catalog/).

## Prometheus and Grafana

Prometheus and Grafana are deployed as a single stack. Grafana is available
under `https://grafana.${env}.${domain}`. Deployment supports only OAuth2 authentication.
We're using standard Grafana RBAC roles, i.e. `Admin`, `Editor` and `Viewer`.
Assignments can be configured through variables, see `prometheus` variable
for details.

Information about resource provisioning can be found in [Prometheus/Grafana provisioning](doc/prometheus_grafana_provisioning.md).

## Troubleshooting

### When adding new environment I cannot access any subdomain under env_name.indiebi.dev, e.g. argocd.env_name.indiebi.dev

This is a known issue with `cert-manager` and Azure DNS: https://github.com/cert-manager/cert-manager/issues/806 (or expected behavior: https://serverfault.com/a/1109867).

Check the created DNS zone `env_name.indiebi.dev` for hanging ACME challenges, if there are
any `cert-manager` might be stuck due to federated credentials not being propagated. 
In that case simple restart should suffice. It can be done with (or through OpenLens):

```bash
kubectl -n cert-manager rollout restart deployment cert-manager
```

After couple minutes the issue should resolve itself.

If this didn't help, remove `<service-name>-tls` secrets (in Lens: Config > Secret > pick namespace > delete) and restart again.

### When adding new environment Database provisioners fails to create DB Users  

We spotted that during **the first deploy**, problems with creating Database users can occur through prepared provisioners. To fix this issue you have two options:

1. Remove broken provisioners from state, and then rerun pipeline in the GitLab for given env, in order to do it:  
```bash
cd deployments/<env>

az login
az account set --subscription app-<env>

export TERRAFORM_BACKEND_STORAGE=<could be found in the .gitlab-ci.yaml for given env>
export SQL_AD_ADMIN_PASSWORD=foo # It can be fake for this purpose
export GITLAB_ACCESS_TOKEN=foo # It can be fake for this purpose

terragrunt init --reconfigure
terragrunt state list | grep null_resource # This lists all identity provisioner

terragrunt state rm <broken provisioner from the previous list>
```

2. We prepared mechanism to recreate all database provisioners. You need to bump the `retry` value in the given modules:  
```bash
modules/components/database_provisioner/main.tf
modules/stacks/workload/cpt/user_service/main.tf # Only if User Service provisioner needs to be fixed
modules/stacks/workload/dpt/dataset_manager/main.tf # Only if Dataset Manger provisioner needs to be fixed
```
```
resource "null_resource" "identity_provisioner" {
  triggers = {
    # In case of problem with creating DB users (the most likely on first deployment) We should bump this value to force creating users once again
    retry         = 0
    database_name = var.database.name
    identity_id   = var.identity.id
  }
  provisioner "local-exec" {
    command = "sqlcmd --authentication-method=ActiveDirectoryServicePrincipal -d ${var.database.name} -Q \"${
    templatefile("../components/database_provisioner/setup_db.sql.tpl", { identity = var.identity.name })}\""
    environment = {
      SQLCMDSERVER   = var.database.host
      SQLCMDUSER     = var.server_admin.username
      SQLCMDPASSWORD = var.server_admin.password
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}
```

### App Gateway points to wrong pod of a workload

Due to App Gateway synchronization failure, or any other failure that causes App Gateway to route to wrong pods, as a temporary measure it is possible to manually route it to the desired pods. To do so, select the App Gateway in question and navigate to *Backend pools* as shown below:

![App Gateway backend pool](doc/images/appgw_backend_pods.png "Access backend pools")

then select the backend pool (it should be named after the workload you are fixing). In the section *Backend targets*, update the pod IP(s) to new values:

![App Gateway backend pool](doc/images/edit_backend.png "Edit backend pool")

### Argo CD gets stuck during application delete through the UI

This might be caused by [known issue](https://github.com/argoproj/argo-cd/issues/12493). To fix this issue remove `finalizers` from Argo CD `Application`
resource. Before doing that though, manually remove all the managed resources (`ConfigMap`, `Secret`, `Service` etc.).

### Argo CD Application is created but none of the managed resources are created

This might be caused by outdated or incorrect credentials. Make sure container registry and Helm repository credentials are valid.
If that is the case manually restart `argocd-server`, `argocd-image-updater` and `argocd-application-controller`. First two can be restarted
through `Deployment`, last one through `StatefulSet`.

### When updating blob in storage I get error saying it needs to be imported into state

The full error:

```
Error: A resource with the ID "https://storage-account.blob.core.windows.net/container/blob-name" already exists - to be managed via Terraform this resource needs to be imported into the State. Please see the resource documentation for "azurerm_storage_blob" for more information.
```

This is caused by dependencies between modules and `create_before_destroy` lifecycle set to `true` on some resources.

From the [documentation](https://developer.hashicorp.com/terraform/language/meta-arguments/lifecycle):

> Note that Terraform propagates and applies the `create_before_destroy` meta-attribute behaviour to all resource dependencies. For example, if `create_before_destroy` is enabled on resource A but not on resource B, but resource A is dependent on resource B, then Terraform enables `create_before_destroy` for resource B implicitly by default and stores it to the state file. You cannot override `create_before_destroy` to `false` on resource B because that would imply dependency cycles in the graph.

"Quick" fix for this issue it to comment out **all** instances of `create_before_destroy` lifecycle and replanning and reapplying the changes.
Once the change is applied lifecycle definitions should be restored.

**In the long term blob management should moved outside of `single-click-indiebi`.**

### When creating storage account apply fails on creation of backup instance

This is caused by Azure Policy denying creation of storage accounts without container soft delete configured.
Even though we correctly configure it on the storage account, request that sets up the backup instance doesn't send the required config.
To fix this issue:

1. In Azure Portal or `azure-core` repository change "[INDIEBI] Azure Storage Blobs and Containers should have soft delete enabled" policy effect to `Audit`.
2. Go to backup instance and click "Fix protection error".
3. Import existing instance into terraform state.
4. Change back policy effect to `Deny`.

### Prometheus can't start due WAL failure

There are several symptoms:

- Pod gets OOM killed and keeps restarting - this might be caused by high cardinality metrics that cause the memory usage to increase over the limit.

- Logs contain:

  ```
  "Fatal error" err="opening storage failed: repair corrupted WAL: cannot handle error: open WAL segment: 0: open /prometheus/wal/********: no such file or directory"
  ```

- All WAL segments gets loaded (logs contain: `WAL replay completed` message), but it takes too long and Pod gets restarted by startup probe.

This prevents Prometheus-based Grafana dashboards to load and might affect other workloads deployed on pod hosting the Prometheus.

To resolve this issue:

**Since the procedure below is destructive, inform other teams about metrics data loss on Issues channel.**

1. Scale down stateful set to 0, this is tricky, since it won't allow scaling down if there's a failing pod, so you need to kill it first and
immediately scale down to 0. Since this is a race condition situation the best bet is to execute the following commands (you might need to do it more than once):

    ```bash
    kubectl delete pod --grace-period=0 --force -n prometheus prometheus-prometheus-kube-prometheus-prometheus-0; kubectl scale statefulset prometheus-prometheus-kube-prometheus-prometheus -n prometheus --replicas=0
    ```

2. Once scaled down run debugger pod with persistent volume mounted:

    ```bash
    cat <<EOF | kubectl apply -n prometheus -f -
    apiVersion: v1
    kind: Pod
    metadata:
      name: prometheus-pvc-debugger
    spec:
      containers:
      - name: debugger
        image: alpine:latest
        command: ["sleep", "3600"]
        volumeMounts:
        - mountPath: /data
          name: prometheus-data
        securityContext:
            runAsNonRoot: true
            runAsUser: 1000
      volumes:
      - name: prometheus-data
        persistentVolumeClaim:
          # validate if claimName is correct
          claimName: prometheus-prometheus-kube-prometheus-prometheus-db-prometheus-prometheus-kube-prometheus-prometheus-0
    EOF
    ```

3. Run shell in the debugger pod:

    ```bash
    kubectl exec -it -n prometheus prometheus-pvc-debugger -- /bin/sh
    ```

4. Find the `wal` directory under `/prometheus-db` (or `/data/prometheus-db`, depending how it was mounted)
and remove its contents `rm -rf wal/*`.

5. Remove debugger pod, once it's stopped scale the stateful set back up:

    ```bash
    kubectl delete pod -n prometheus prometheus-pvc-debugger
    kubectl scale statefulset -n prometheus prometheus-prometheus-kube-prometheus-prometheus --replicas=1
    ``` 

## FAQ

**Q: Is it possible to destroy infrastructure through CI/CD?**

A: For safety reasons, destroying infrastructure using Gitlab GUI has been disabled. We decided it is too risky to do it so easily and the cases should be rare enough that the local env flow described [here](#how-to-work-with-this-repo) will be sufficient while preventing accidental destruction. 

**Q: Can I disable auto-sync for web-app/data-job?**

A: Yes. Organization administrators can do it through Argo CD UI. Everyone can do it by setting workload's `auto_sync_enabled` parameter to `false`.

### Things that should be moved to core

SQL AD Admin - this resource needs to be created manually and values need to be
shared thorough shared vault created in core.

Git repo, Helm repo and Docker registry credentials for Argo CD - those values
are generated in GitLab and are provided externally.  In the perfect world
each set of credentials would be different (right now we're using the same everywhere).