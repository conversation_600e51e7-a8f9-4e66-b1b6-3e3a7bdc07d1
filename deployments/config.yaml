# for up-to-date list use:
# az ad group list --query "[].{name:displayName, id:id}"
# All Developers: 54988583-edda-4bc2-b6ce-1e30da28b67a
# CTO: bfaa9ba4-f9f4-4062-bfbd-01fc2a14278d
# Cloud Platform Team: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103
# CPT Tech Lead: 138569dc-1ed7-4645-a47d-4c9a942bf18d
# Data Platform Team: a82022b2-cc7f-4570-b342-13f799c579c7
# DPT Tech Lead: 1e4e49e9-80a9-4b20-8ff9-62e58d27fc8b
# SaaS Team: 7264ab0e-c6c0-4305-a4a0-8027df6f024f
# SAAS Tech Lead: 87acc001-2bd1-4c5a-ad53-a6ae873c594e

common:
  firewall_public_ip_address: *************
  vpn_address_space: "**********/16"
  tenant_id: bb7887c7-c8cd-4f3d-b602-2e270710fdb9
  location: westeurope
  admin_contact_email: <EMAIL>
  secret_store_name: indiebi-secret-store
  keys_secrets_expiration_date: "2026-01-15T00:00:00Z"
  connectivity_subscription_id: d9a1fbdd-f213-4389-804c-d7e244846f5a
  management_subscription_id: c0f751ef-b0e7-4ca1-9cec-4aae0501583b
  acr_resource_id: /subscriptions/c0f751ef-b0e7-4ca1-9cec-4aae0501583b/resourceGroups/rg-container-registry/providers/Microsoft.ContainerRegistry/registries/crindiebimain
  service_principals:
    dbw_indiebi:
      object_id: a1f0490f-2aba-4a85-bc28-a0af6a7a3b10
      name: sp-gitlab-dbw-indiebi
  teams:
    cpt:
      name: cpt
      azure_group:
        object_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103
        name: "[Azure] Cloud Platform Team"
      team_leader:
        object_id: 138569dc-1ed7-4645-a47d-4c9a942bf18d
        name: "<EMAIL>"
      runner_identity:
        object_id: 205a050e-c595-4116-9269-c7d272c63031
        name: id-gitlab-runner-cpt-indiebi
    saas:
      name: saas
      azure_group:
        object_id: 7264ab0e-c6c0-4305-a4a0-8027df6f024f
        name: "[Azure] SaaS Team"
      team_leader:
        object_id: 87acc001-2bd1-4c5a-ad53-a6ae873c594e
        name: "<EMAIL>"
      runner_identity:
        object_id: 7ddc0d56-3afb-4a2c-a7fb-a079220eb55e
        name: id-gitlab-runner-saas-indiebi
    dpt:
      name: dpt
      azure_group:
        object_id: a82022b2-cc7f-4570-b342-13f799c579c7
        name: "[Azure] Data Platform Team"
      team_leader:
        object_id: 1e4e49e9-80a9-4b20-8ff9-62e58d27fc8b
        name: "<EMAIL>"
      runner_identity:
        object_id: 2364184b-1933-4df1-bb64-c9a1a5eea2f0
        name: id-gitlab-runner-dpt-indiebi
    ppt:
      name: ppt
      azure_group:
        object_id: 0320fd79-0136-4561-a23a-4916c725e803
        name: "[Azure] Partner Program Team"
      team_leader:
        object_id: 68920d10-00e8-4fda-aa05-8f4014886a29
        name: "<EMAIL>"
      runner_identity:
        object_id: 8c4c2ab6-6e71-45c1-b6a3-5aade64258d7
        name: id-gitlab-runner-ppt-indiebi
  backup:
    vault_id: /subscriptions/c0f751ef-b0e7-4ca1-9cec-4aae0501583b/resourceGroups/rg-backup-vault/providers/Microsoft.DataProtection/backupVaults/bvault-indiebi
    policy_id: /subscriptions/c0f751ef-b0e7-4ca1-9cec-4aae0501583b/resourceGroups/rg-backup-vault/providers/Microsoft.DataProtection/backupVaults/bvault-indiebi/backupPolicies/bkpol-storage-backup-indiebi

gitlab_username: single-click-access

private_dns_zone_ids:
  redis: /subscriptions/d9a1fbdd-f213-4389-804c-d7e244846f5a/resourceGroups/indiebi-dns/providers/Microsoft.Network/privateDnsZones/privatelink.redis.cache.windows.net
  sql_server: /subscriptions/d9a1fbdd-f213-4389-804c-d7e244846f5a/resourceGroups/indiebi-dns/providers/Microsoft.Network/privateDnsZones/privatelink.database.windows.net
  vault: /subscriptions/d9a1fbdd-f213-4389-804c-d7e244846f5a/resourceGroups/indiebi-dns/providers/Microsoft.Network/privateDnsZones/privatelink.vaultcore.azure.net
  blob: /subscriptions/d9a1fbdd-f213-4389-804c-d7e244846f5a/resourceGroups/indiebi-dns/providers/Microsoft.Network/privateDnsZones/privatelink.blob.core.windows.net
  table: /subscriptions/d9a1fbdd-f213-4389-804c-d7e244846f5a/resourceGroups/indiebi-dns/providers/Microsoft.Network/privateDnsZones/privatelink.table.core.windows.net
  dls: /subscriptions/d9a1fbdd-f213-4389-804c-d7e244846f5a/resourceGroups/indiebi-dns/providers/Microsoft.Network/privateDnsZones/privatelink.dfs.core.windows.net
  postgres: /subscriptions/d9a1fbdd-f213-4389-804c-d7e244846f5a/resourceGroups/indiebi-dns/providers/Microsoft.Network/privateDnsZones/privatelink.postgres.database.azure.com
