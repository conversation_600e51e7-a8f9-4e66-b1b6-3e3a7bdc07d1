locals {
  state_name                 = "single_click_${replace(path_relative_to_include(), "/", "-")}"
  root_deployments_dir       = get_parent_terragrunt_dir()
  relative_deployment_path   = path_relative_to_include()
  deployment_path_components = compact(split("/", local.relative_deployment_path))

  possible_config_dirs = [
    for i in range(0, length(local.deployment_path_components) + 1) :
    join("/", concat(
      [local.root_deployments_dir],
      slice(local.deployment_path_components, 0, i)
    ))
  ]

  possible_config_paths = flatten([
    for dir in local.possible_config_dirs : [
      "${dir}/config.yml",
      "${dir}/config.yaml"
    ]
  ])

  file_configs = [
    for path in local.possible_config_paths :
    yamldecode(file(path)) if fileexists(path)
  ]

  config_from_environment = {
    sql_ad_admin_password = get_env("SQL_AD_ADMIN_PASSWORD")

    # TODO: this probably should be a different credentials for docker, git and helm repos
    gitlab_password = get_env("GITLAB_ACCESS_TOKEN")
  }

  merged_config = merge(local.config_from_environment, local.file_configs...)
}

inputs = local.merged_config

remote_state {
  backend = "azurerm"
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
  config = {
    subscription_id      = get_env("TERRAFORM_BACKEND_SUBSCRIPTION_ID", "1fd032f3-ad04-46de-8364-6133cff9460b")
    resource_group_name  = get_env("TERRAFORM_BACKEND_RG", "rg-terraform")
    storage_account_name = get_env("TERRAFORM_BACKEND_STORAGE", "stterraformstate7a")
    container_name       = get_env("TERRAFORM_BACKEND_CONTAINER", "cpt")
    key                  = "${get_env("TERRAFORM_BACKEND_KEY", "single_click_dev")}.tfstate"
  }
}

generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
provider "azurerm" {
  features {
    key_vault {
      purge_soft_delete_on_destroy               = true
      purge_soft_deleted_keys_on_destroy         = true
      purge_soft_deleted_secrets_on_destroy      = true
      purge_soft_deleted_certificates_on_destroy = true
    }
  }
  tenant_id       = var.common.tenant_id
  subscription_id = var.environment.subscription_id
}

provider "azurerm" {
  alias           = "connectivity"
  subscription_id = var.common.connectivity_subscription_id
  features {}
}

provider "azurerm" {
  alias           = "management"
  subscription_id = var.common.management_subscription_id
  features {}
}

provider "azuread" {
  tenant_id       = var.common.tenant_id
}

provider "helm" {
  kubernetes {
    host                   = module.compute.kubernetes.host
    client_certificate     = base64decode(module.compute.kubernetes.client_certificate)
    client_key             = base64decode(module.compute.kubernetes.client_key)
    cluster_ca_certificate = base64decode(module.compute.kubernetes.cluster_ca_certificate)
  }
}

provider "kubernetes" {
  host                   = module.compute.kubernetes.host
  client_certificate     = base64decode(module.compute.kubernetes.client_certificate)
  client_key             = base64decode(module.compute.kubernetes.client_key)
  cluster_ca_certificate = base64decode(module.compute.kubernetes.cluster_ca_certificate)
}

EOF
}

terraform {
  # "//" is intended:  https://developer.hashicorp.com/terraform/language/modules/sources#modules-in-package-sub-directories
  source = "${local.root_deployments_dir}/..//modules/stacks"

  extra_arguments "compact_warnings" {
    commands = get_terraform_commands_that_need_input()

    arguments = [
      # "-compact-warnings"
    ]
  }

  extra_arguments "auto_approve" {
    commands = ["apply"]
    arguments = [
      "-auto-approve",
      "-compact-warnings"
    ]
  }

  extra_arguments "retry_lock" {
    commands = get_terraform_commands_that_need_locking()

    arguments = [
      "-lock-timeout=20m"
    ]
  }
}
