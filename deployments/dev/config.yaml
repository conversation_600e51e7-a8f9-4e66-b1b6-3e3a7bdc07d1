environment:
  name: dev
  domain: indiebi.dev
  subscription_id: 974be8a6-83ee-4087-b47d-7cd8424ba8e5
  is_private: true

top_dns_zone:
  name: "indiebi.dev"
  resource_group_name: "indiebi-dns"

aks_configuration:
  default_node_pool:
    vm_size: standard_ds2_v2
    min_count: 2
    max_count: 3

  data_jobs_node_pool:
    vm_size: standard_e4as_v5
    min_count: 1
    max_count: 2
    max_pods: 50

  web_apps_node_pool:
    vm_size: standard_ds2_v2
    min_count: 3
    max_count: 4

  namespace_admins:
    - namespace: cpt
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    # required for local dev of BO by teams other than CPT
    - namespace: cpt
      principal_id: 54988583-edda-4bc2-b6ce-1e30da28b67a # All Developers
    - namespace: saas
      principal_id: 7264ab0e-c6c0-4305-a4a0-8027df6f024f # SAAS
    - namespace: saas
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: dpt
      principal_id: a82022b2-cc7f-4570-b342-13f799c579c7 # DPT
    - namespace: dpt
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT

infra_admins:
  - c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
  - 6876615a-8d1a-42ac-bb8f-fc1d69e758da # sp-gitlab-indiebi-dev enterprise app object id

infra_readers:
  - a82022b2-cc7f-4570-b342-13f799c579c7 # DPT
  - 7264ab0e-c6c0-4305-a4a0-8027df6f024f # SAAS

client_credentials_vault_admins:
  - c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT

argocd_configuration:
  rbac_policy: relaxed
  allowed_login_group: 54988583-edda-4bc2-b6ce-1e30da28b67a # All Developers
  argocd_notification_channels:
    cpt-argo-cd: "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/268a2fff845243ec9a730eb157f44db5/4827773e-80a9-4979-bfea-98319be2785f/V2Vz6X8xZJxrcQqrMNhoQLj52cENFsinWFzRTWEa_9hsA1"
    cpt-errors: "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/b3a8dad416e14bd7bdb513d520cefc38/4827773e-80a9-4979-bfea-98319be2785f/V2NpNNbMRcVlry1dq1qIxFVVAl4vaSYnAUTYFYIt-0E3M1"
    dpt-notifications: "https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/da32f9aac999437f96756d7c3f6843e5/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2aZx3V2cCsEIWubkj10ddn8M6q5bMm1IbjYbXIXUgjYs1"
    saas-argo-cd: "https://indiebisa.webhook.office.com/webhookb2/10b5e8b6-658b-4993-82bc-1e5e763575f1@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/8b42f8fb208d447db70d5fe91c513290/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2eh9oDFbVVYqGV9KrL0k9F8nFyEJ16QnZA6CPSpzIADk1"

prometheus_configuration:
  storage_size: 16Gi
  retention: 14d
  retention_size: 14GiB
  grafana:
    image:
      tag: 11.6.4
    admins:
      - c8809c7d-1e66-4ccc-b807-2ad3f2cf2103
    editors:
      - 54988583-edda-4bc2-b6ce-1e30da28b67a
    alerting:
      cpt:
        contact_point: "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/fc9094d0c1ee43d1ae37683b78c5dd82/4827773e-80a9-4979-bfea-98319be2785f/V2qrqehg-x9OIGWGaFRhymb0AvBaiGEqOPyoo5X9unoys1"
        mute_times:
          mute_intruder_requests:
            - times:
                - start_time: "01:00"
                  end_time: "04:00"
              location: "Europe/Warsaw"
        custom_policies:
          - matchers:
              - intruder = true
            mute_time_intervals:
              - mute_intruder_requests
      dpt:
        contact_point: "https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/da32f9aac999437f96756d7c3f6843e5/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2aZx3V2cCsEIWubkj10ddn8M6q5bMm1IbjYbXIXUgjYs1"
      saas:
        contact_point: "https://indiebisa.webhook.office.com/webhookb2/10b5e8b6-658b-4993-82bc-1e5e763575f1@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/02ec4fc5e6d249ea9054b7f8faec06b7/87acc001-2bd1-4c5a-ad53-a6ae873c594e/V2VoASukyt0-CreRcaUu77j3tQlkih9aEHTvj9xrb2tkY1"

vnet:
  name: vnet-indiebi-dev
  rg_name: rg-platform
  # ******** - default subnet (******** - ***********)
  workload_subnet_address_space: *********/20
  function_app_subnet_address_space: **********/24
  app_gateway_subnet_address_space: **********/24
  postgresql_subnet_address_space: **********/24
  app_gateway_private_ip: ************
  firewall_private_ip: ********
  default_route_table: "rt-default"
  app_gateway_capacity_units: 1
  app_gateway_response_buffering_enabled: false

sql_ad_admin:
  login_name: sp-gitlab-app-dev
  app_id: 8984d01d-7d62-4932-97c7-067c91f1049e
  server_identity_id: /subscriptions/974be8a6-83ee-4087-b47d-7cd8424ba8e5/resourceGroups/rg-bootstrap/providers/Microsoft.ManagedIdentity/userAssignedIdentities/id-gitlab-indiebi-dev

persistence_configuration:
  databases:
    - workload_name: pipeline_manager
      sku_name: S2
      max_size_gb: 5

    - workload_name: report_service
      sku_name: S1
      max_size_gb: 2

    - workload_name: dataset_manager
      sku_name: Basic
      max_size_gb: 2

    - workload_name: events_service
      sku_name: Basic
      max_size_gb: 2

    - workload_name: user_service_v2
      sku_name: S0
      max_size_gb: 2

  postgresql:
    - server_name: "indiebi-postgresql-server"
      max_size_mb: 32768
      storage_tier: "P4"
      sku_name: "B_Standard_B1ms"
      databases:
        - workload_name: scraper_service
    - server_name: "pdc-postgresql-server"
      max_size_mb: 32768
      # TODO: consider reducing sku and storage_tier
      storage_tier: "P10"
      sku_name: "B_Standard_B2s"
      databases:
        - workload_name: public_data_crawler
    - server_name: "dms-postgresql-server"
      max_size_mb: 32768
      storage_tier: "P4"
      sku_name: "B_Standard_B1ms"
      databases:
        - workload_name: discount_management_system

  module_federation_storage_account:
    cors_rules:
      - allowed_origins:
          - "http://localhost:5173"
          - "https://app.indiebi.dev"
        allowed_methods:
          - GET
          - OPTIONS
        max_age_in_seconds: 7200
        exposed_headers:
          - "*"
        allowed_headers:
          - "*"

cloud_platform_team_configuration:
  tags:
    team: cpt
  argocd_notifications:
    sync-succeeded: cpt-argo-cd
    sync-running: cpt-argo-cd
    sync-failed: cpt-errors
    deleted: cpt-argo-cd
    deployed: cpt-argo-cd
    health-degraded: cpt-errors

  pipeline_manager:
    workload_name: pipeline-manager
    configuration:
      backoffice_url: "https://backoffice.indiebi.dev"
      service_bus_message_ttl: P14D
    docker_image:
      name: crindiebimain.azurecr.io/cpt/pipeline-manager
      tag: dev
    kubernetes:
      requests:
        cpu: 250m
        memory: 420Mi
      limits:
        cpu: 500m
        memory: 500Mi

  report_service:
    workload_name: report-service
    docker_image:
      name: crindiebimain.azurecr.io/cpt/report-service
      tag: dev
    kubernetes:
      requests:
        cpu: 200m
        memory: 220Mi
      limits:
        cpu: 2000m
        memory: 1.5Gi

  user_service_v2:
    workload_name: user-service-v2
    configuration:
      allow_impersonate_access_token_expiry_override: true
    docker_image:
      name: crindiebimain.azurecr.io/cpt/user-service-v2
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 290Mi
      limits:
        cpu: 200m
        memory: 400Mi

  backoffice:
    workload_name: backoffice
    configuration:
      user_editor_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      user_viewer_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      user_access_manager_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      report_editor_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      report_viewer_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      soft_reupload_editor_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      data_sharing_viewer_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      data_sharing_editor_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      sku_viewer_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      sku_editor_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      feature_viewer_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      feature_editor_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      processing_manager_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      dashboard_manager_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      key_value_editor_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      partner_portal_user_groups: ["3f05c0bd-79ae-4a62-bef6-7df8eb46e918"] # All users
      msteams_webhook: "https://indiebisa.webhook.office.com/webhookb2/74172533-2be3-42f6-bb65-ce797774b540@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/9bc75593f4344931beab2be6b15edafa/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2WASCzzLLciAx57WS4v7HbJPVDyVZBco43XXBYwlnQXY1"
      synapse_database_url: ""
      partner_portal_url: "https://partner-portal.indiebi.dev"
    docker_image:
      name: crindiebimain.azurecr.io/cpt/backoffice
      tag: dev
    kubernetes:
      requests:
        cpu: 200m
        memory: 100Mi
      limits:
        cpu: 400m
        memory: 400Mi

  reprocessing_trigger:
    workload_name: reprocessing-trigger
    docker_image:
      name: crindiebimain.azurecr.io/cpt/reprocessing-trigger
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 100Mi
      limits:
        cpu: 100m
        memory: 100Mi

  sql_backup:
    workload_name: sql-backup
    docker_image:
      name: crindiebimain.azurecr.io/cpt/sql-backup
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 2Gi
      limits:
        cpu: 100m
        memory: 2Gi

  parser_runner:
    workload_name: parser-runner
    docker_image:
      name: crindiebimain.azurecr.io/cpt/parser-runner
      tag: dev
    kubernetes:
      requests:
        cpu: 1500m
        memory: 16Gi
      limits:
        cpu: 1500m
        memory: 16Gi

  public_data_crawler:
    workload_name: public-data-crawler
    configuration:
      apm_enabled: "False"
    docker_image:
      name: crindiebimain.azurecr.io/cpt/public-data-crawler
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 100Mi
      limits:
        cpu: 1000m
        memory: 2Gi
    replicas: 1

  partner_portal:
    workload_name: partner-portal
    docker_image:
      name: crindiebimain.azurecr.io/cpt/discount-setter
      tag: dev
    configuration:
      bitwarden_client_id: user.c4c059e0-8ab4-41f9-8724-b2f8009d6857
      bitwarden_base_collection: PPT/Automation-Dev
      raw_discounts_files:
        - name: dev_master_file
          folder_url: https://graph.microsoft.com/v1.0/sites/indiebisa.sharepoint.com,1633609d-75ea-483f-96f0-546a840d3bc8,a8f295a7-6638-4835-bf22-0fec445affa9/drive/items
          identifier: D25433BC-82F4-46A6-B2FD-F9000442A130
        - name: "SAAS DISCOUNTING MASTER FILE"
          folder_url: https://graph.microsoft.com/v1.0/sites/indiebisa.sharepoint.com,6601026f-1785-4138-b60e-fc8b45a81fd9,3aaa1bf3-e636-4fa1-8d8c-91dce7513178/drive/items
          identifier: FF1707D8-8776-49B3-B0C0-D3FF058AF0BF
      # CPT test channel
      playstation_events_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/44f620a99a40463c861a6b63a2c91378/4827773e-80a9-4979-bfea-98319be2785f/V2_7dKM6IrjDB75orFrR8po-b3H2gQAcorDGgvqu6oGYk1",
        ]
      user_action_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/44f620a99a40463c861a6b63a2c91378/4827773e-80a9-4979-bfea-98319be2785f/V2_7dKM6IrjDB75orFrR8po-b3H2gQAcorDGgvqu6oGYk1",
        ]
      errors_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/44f620a99a40463c861a6b63a2c91378/4827773e-80a9-4979-bfea-98319be2785f/V2_7dKM6IrjDB75orFrR8po-b3H2gQAcorDGgvqu6oGYk1",
        ]
      playstation_events_monitor_enabled: False
      organization_override: o-EGQfNf
    kubernetes:
      requests:
        cpu: 300m
        memory: 400Mi
      limits:
        cpu: 2000m
        memory: 2.5Gi
    replicas: 1

  partner_tasks_executor:
    workload_name: partner-tasks-executor
    docker_image:
      name: crindiebimain.azurecr.io/cpt/partner-tasks-executor
      tag: dev
    configuration:
      bitwarden_client_id: user.c4c059e0-8ab4-41f9-8724-b2f8009d6857
      bitwarden_base_collection: PPT/Automation-Dev
      # CPT test channel
      playstation_events_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/44f620a99a40463c861a6b63a2c91378/4827773e-80a9-4979-bfea-98319be2785f/V2_7dKM6IrjDB75orFrR8po-b3H2gQAcorDGgvqu6oGYk1",
        ]
      unassigned_skus_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/44f620a99a40463c861a6b63a2c91378/4827773e-80a9-4979-bfea-98319be2785f/V2_7dKM6IrjDB75orFrR8po-b3H2gQAcorDGgvqu6oGYk1",
        ]
      errors_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/44f620a99a40463c861a6b63a2c91378/4827773e-80a9-4979-bfea-98319be2785f/V2_7dKM6IrjDB75orFrR8po-b3H2gQAcorDGgvqu6oGYk1",
        ]
      organization_override: o-EGQfNf
      backoffice_url: "https://backoffice.indiebi.dev"
    kubernetes:
      requests:
        cpu: 1000m
        memory: 2.5Gi
      limits:
        cpu: 1000m
        memory: 2.5Gi

data_platform_team_configuration:
  tags:
    team: dpt
  argocd_notifications:
    sync-failed: dpt-notifications
    health-degraded: dpt-notifications
    deployed: dpt-notifications

  dataset_manager:
    workload_name: dataset-manager
    configuration:
      azure_client_id: "33289804-21c1-4fcf-a345-645aabb7e59d"
      user_profiles_service_principal_id: "f8cba1ea-c8a9-44b9-92a3-8a014e07a40f"
      pbi_sub_id: "03db22d5-c3b5-4c7c-87c1-81da04c6283f"
      pbi_resource_group: rg-pbi-embedded-dev
      service_principal_name: dataset-manager-dev
    docker_image:
      name: crindiebimain.azurecr.io/dpt/dataset-manager
      tag: dev
    kubernetes:
      requests:
        cpu: 150m
        memory: 275Mi
      limits:
        cpu: 500m
        memory: 1Gi

  update_shared:
    workload_name: update-shared
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/update-shared
      tag: dev
    kubernetes:
      requests:
        cpu: 250m
        memory: 500Mi
      limits:
        cpu: 250m
        memory: 500Mi

  find_shards:
    workload_name: find-shards
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/find-shards
      tag: dev
    kubernetes:
      requests:
        cpu: 150m
        memory: 100Mi
      limits:
        cpu: 150m
        memory: 100Mi

  pbi_refresh:
    workload_name: pbi-refresh
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/pbi-refresh
      tag: dev
    kubernetes:
      requests:
        cpu: 250m
        memory: 200Mi
      limits:
        cpu: 250m
        memory: 200Mi

  core_silver:
    workload_name: core-silver
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/core-silver
      tag: dev
    kubernetes:
      requests:
        cpu: 1500m
        memory: 8Gi
      limits:
        cpu: 1500m
        memory: 8Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 3000m
          memory: 25Gi
        limits:
          cpu: 3000m
          memory: 25Gi

  processing_notification:
    workload_name: processing-notification
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/processing-notification
      tag: dev
    kubernetes:
      requests:
        cpu: 150m
        memory: 100Mi
      limits:
        cpu: 150m
        memory: 100Mi

  scraper_api:
    workload_name: scraper-api
    configuration:
      lse_uri: https://stscraperbinstorag01db.blob.core.windows.net/dev-scraper-client/lse_version.json
      auth_email_forwarding_prefix: mfa-dev-
      redis_sku_name: Standard
    docker_image:
      name: crindiebimain.azurecr.io/dpt/scraper-api
      tag: dev
    side_deployment_slot:
      workload_name: scraper-api-staging
      tag: pre-dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 115Mi
      limits:
        cpu: 250m
        memory: 300Mi

  cloud_reports:
    workload_name: cloud-reports
    docker_image:
      name: crindiebimain.azurecr.io/dpt/cloud-reports
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 300Mi
      limits:
        cpu: 250m
        memory: 300Mi

  events_service:
    workload_name: events-service
    docker_image:
      name: crindiebimain.azurecr.io/dpt/events-service
      tag: dev
    kubernetes:
      requests:
        cpu: 200m
        memory: 1.5Gi
      limits:
        cpu: 2000m
        memory: 3Gi
    replicas: 1

  events_service_gold:
    workload_name: events-service-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/events-service-gold
      tag: dev
    kubernetes:
      requests:
        cpu: 300m
        memory: 8Gi
      limits:
        cpu: 1500m
        memory: 8Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 3000m
          memory: 25Gi
        limits:
          cpu: 3000m
          memory: 25Gi

  dataoffice:
    workload_name: dataoffice
    docker_image:
      name: crindiebimain.azurecr.io/dpt/dataoffice
      tag: dev
    kubernetes:
      requests:
        cpu: 300m
        memory: 200Mi
      limits:
        cpu: 500m
        memory: 400Mi

  currency_exchange_rates:
    workload_name: currency-exchange-rates
    docker_image:
      name: crindiebimain.azurecr.io/dpt/public-data-crawlers/currency-exchange-rates
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 400Mi
      limits:
        cpu: 200m
        memory: 400Mi

  scraper_service:
    configuration:
      teams_webhook_url: ""
    workload_name: scraper-service
    docker_image:
      name: crindiebimain.azurecr.io/dpt/s2
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 200Mi
      limits:
        cpu: 200m
        memory: 600Mi
    replicas: 1

saas_team_configuration:
  tags:
    team: saas
  argocd_notifications:
    sync-failed: saas-argo-cd
    health-degraded: saas-argo-cd
    deployed: saas-argo-cd

  electron_api:
    workload_name: electron-api
    configuration:
      env: dev
    legacy_network_config:
      dns_name: electron-api-integration
      hostname: electron-api-integration.indiebi.dev
    docker_image:
      name: crindiebimain.azurecr.io/saas/electron-api
      tag: dev
    side_deployment_slot:
      workload_name: electron-api-staging
      tag: pre-dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 180Mi
      limits:
        cpu: 250m
        memory: 250Mi

  discounts_service:
    workload_name: discounts-service
    configuration:
      env: dev
      discounts_master_file_path: "Events Planner/[DEV] SAAS DISCOUNTING MASTER FILE.xlsx"
    docker_image:
      name: crindiebimain.azurecr.io/saas/discounts-service
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 110Mi
      limits:
        cpu: 1000m
        memory: 200Mi

  saas_monitoring:
    workload_name: saas-monitoring
    docker_image:
      name: crindiebimain.azurecr.io/saas/saas-monitoring
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 100Mi
      limits:
        cpu: 250m
        memory: 200Mi

  events_planner:
    workload_name: events-planner
    configuration:
      env: dev
    docker_image:
      name: crindiebimain.azurecr.io/saas/events-planner
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 20Mi
      limits:
        cpu: 250m
        memory: 200Mi

  desktop_app_web:
    workload_name: desktop-app-web
    configuration:
      env: dev
    network:
      custom_dns_entries:
        - dns_name: app
          hostname: app.indiebi.dev
        - dns_name: www.app
          hostname: www.app.indiebi.dev
    docker_image:
      name: crindiebimain.azurecr.io/saas/desktop-app-web
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 160Mi
      limits:
        cpu: 500m
        memory: 500Mi

  landing:
    workload_name: landing
    configuration:
      env: dev
      nuxt_public_site_env: dev
      external_urls:
        user_guide: https://indiebi.notion.site/IndieBI-User-Guide-d68a4d1cb92643098085e62cb652c33a
        discord: https://discord.com/invite/WJRMe2D6Xt
    network:
      custom_dns_entries:
        - dns_name: "@"
          hostname: indiebi.dev
        - dns_name: www
          hostname: www.indiebi.dev
    docker_image:
      name: crindiebimain.azurecr.io/saas/indiebi-landing
      tag: dev
    kubernetes:
      requests:
        cpu: 100m
        memory: 160Mi
      limits:
        cpu: 500m
        memory: 500Mi

  mobile:
    workload_name: mobile
    configuration:
      # Standard is required for basic auth support
      sku: Standard

  saas_gold:
    workload_name: saas-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/saas-gold
      tag: dev
    kubernetes:
      requests:
        cpu: 300m
        memory: 17Gi
      limits:
        cpu: 1500m
        memory: 17Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 3000m
          memory: 25Gi
        limits:
          cpu: 3000m
          memory: 25Gi

  direct_data_access_gold:
    workload_name: direct-data-access-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/direct-data-access-gold
      tag: dev
    kubernetes:
      requests:
        cpu: 300m
        memory: 1Gi
      limits:
        cpu: 300m
        memory: 1Gi

partner_program_team_configuration:
  tags:
    team: ppt
  argocd_notifications: # TODO: change to ppt when it will be prepared
    sync-failed: dpt-notifications
    health-degraded: dpt-notifications
    deployed: dpt-notifications

  ppt_gold:
    workload_name: ppt-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/ppt-gold
      tag: dev
    kubernetes:
      requests:
        cpu: 300m
        memory: 8Gi
      limits:
        cpu: 1500m
        memory: 8Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 3000m
          memory: 25Gi
        limits:
          cpu: 3000m
          memory: 25Gi
