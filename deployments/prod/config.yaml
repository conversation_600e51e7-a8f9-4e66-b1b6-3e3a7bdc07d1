environment:
  name: prod
  domain: indiebi.com
  subscription_id: c4d9d53d-61a4-4362-be32-4d242ba17160
  is_private: false

top_dns_zone:
  name: "indiebi.com"
  resource_group_name: "indiebi-dns"

aks_configuration:
  sku_tier: Standard
  default_node_pool:
    vm_size: standard_ds2_v2
    min_count: 2
    max_count: 3

  data_jobs_node_pool:
    vm_size: standard_e16as_v5
    min_count: 1
    max_count: 12
    max_pods: 50

  web_apps_node_pool:
    vm_size: standard_ds2_v2
    min_count: 3
    max_count: 5

  namespace_admins:
    - namespace: cpt
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: saas
      principal_id: 7264ab0e-c6c0-4305-a4a0-8027df6f024f # SAAS
    - namespace: saas
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: dpt
      principal_id: a82022b2-cc7f-4570-b342-13f799c579c7 # DPT
    - namespace: dpt
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: argocd
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: cert-manager
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: external-secrets
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: keda
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: gatekeeper-system
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: kube-node-lease
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: kube-public
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: kube-system
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: prometheus
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
    - namespace: reloader
      principal_id: c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT

infra_admins:
  - 160a0fa7-6afc-46db-8f8c-e7ee821b7241 # sp-gitlab-app-prod enterprise app object id
  - 138569dc-1ed7-4645-a47d-4c9a942bf18d # Patryk

infra_readers:
  - c8809c7d-1e66-4ccc-b807-2ad3f2cf2103 # CPT
  - a82022b2-cc7f-4570-b342-13f799c579c7 # DPT
  - 7264ab0e-c6c0-4305-a4a0-8027df6f024f # SAAS

client_credentials_vault_admins:
  - 138569dc-1ed7-4645-a47d-4c9a942bf18d # Patryk
  - 0320fd79-0136-4561-a23a-4916c725e803 # PPT

argocd_configuration:
  rbac_policy: strict
  allowed_login_group: 54988583-edda-4bc2-b6ce-1e30da28b67a # All Developers
  argocd_notification_channels:
    cpt-argo-cd: "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/268a2fff845243ec9a730eb157f44db5/4827773e-80a9-4979-bfea-98319be2785f/V2Vz6X8xZJxrcQqrMNhoQLj52cENFsinWFzRTWEa_9hsA1"
    cpt-errors: "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/b3a8dad416e14bd7bdb513d520cefc38/4827773e-80a9-4979-bfea-98319be2785f/V2NpNNbMRcVlry1dq1qIxFVVAl4vaSYnAUTYFYIt-0E3M1"
    dpt-notifications: "https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/da32f9aac999437f96756d7c3f6843e5/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2aZx3V2cCsEIWubkj10ddn8M6q5bMm1IbjYbXIXUgjYs1"
    saas-argo-cd: "https://indiebisa.webhook.office.com/webhookb2/10b5e8b6-658b-4993-82bc-1e5e763575f1@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/8b42f8fb208d447db70d5fe91c513290/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2eh9oDFbVVYqGV9KrL0k9F8nFyEJ16QnZA6CPSpzIADk1"

prometheus_configuration:
  storage_size: 32Gi
  retention: 21d
  retention_size: 30GiB
  grafana:
    image:
      tag: 11.6.4
    admins:
      - c8809c7d-1e66-4ccc-b807-2ad3f2cf2103
    editors:
      - 54988583-edda-4bc2-b6ce-1e30da28b67a
    alerting:
      cpt:
        contact_point: "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/fc9094d0c1ee43d1ae37683b78c5dd82/4827773e-80a9-4979-bfea-98319be2785f/V2qrqehg-x9OIGWGaFRhymb0AvBaiGEqOPyoo5X9unoys1"
        mute_times:
          mute_intruder_requests:
            - times:
                - start_time: "01:00"
                  end_time: "04:00"
              location: "Europe/Warsaw"
        custom_policies:
          - matchers:
              - intruder = true
            mute_time_intervals:
              - mute_intruder_requests
      dpt:
        contact_point: "https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/da32f9aac999437f96756d7c3f6843e5/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2aZx3V2cCsEIWubkj10ddn8M6q5bMm1IbjYbXIXUgjYs1"
      saas:
        contact_point: "https://indiebisa.webhook.office.com/webhookb2/10b5e8b6-658b-4993-82bc-1e5e763575f1@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/02ec4fc5e6d249ea9054b7f8faec06b7/87acc001-2bd1-4c5a-ad53-a6ae873c594e/V2VoASukyt0-CreRcaUu77j3tQlkih9aEHTvj9xrb2tkY1"

vnet:
  name: vnet-indiebi-prod
  rg_name: rg-platform
  # ******** - default subnet (******** - ***********)
  workload_subnet_address_space: *********/20
  function_app_subnet_address_space: **********/24
  app_gateway_subnet_address_space: **********/24
  postgresql_subnet_address_space: **********/24
  app_gateway_private_ip: ************
  firewall_private_ip: ********
  default_route_table: "rt-default"
  app_gateway_capacity_units: 2
  app_gateway_response_buffering_enabled: false

sql_ad_admin:
  login_name: sp-gitlab-app-prod
  app_id: b59cfecb-0a13-**********************
  server_identity_id: /subscriptions/c4d9d53d-61a4-4362-be32-4d242ba17160/resourceGroups/rg-bootstrap/providers/Microsoft.ManagedIdentity/userAssignedIdentities/id-gitlab-indiebi-prod

persistence_configuration:
  databases:
    - workload_name: pipeline_manager
      sku_name: S4
      max_size_gb: 20

    - workload_name: report_service
      sku_name: S3
      max_size_gb: 20

    - workload_name: dataset_manager
      sku_name: S0
      max_size_gb: 2

    - workload_name: events_service
      sku_name: Basic
      max_size_gb: 2

    - workload_name: user_service_v2
      sku_name: S0
      max_size_gb: 2

  postgresql:
    - server_name: "indiebi-postgresql-server"
      max_size_mb: 32768
      storage_tier: "P4"
      sku_name: "B_Standard_B1ms"
      databases:
        - workload_name: scraper_service
    - server_name: "pdc-postgresql-server"
      max_size_mb: 32768
      # TODO: consider reducing sku and storage_tier
      storage_tier: "P10"
      sku_name: "B_Standard_B2s"
      databases:
        - workload_name: public_data_crawler
    - server_name: "dms-postgresql-server"
      max_size_mb: 32768
      storage_tier: "P4"
      sku_name: "B_Standard_B1ms"
      databases:
        - workload_name: discount_management_system

  module_federation_storage_account:
    cors_rules:
      - allowed_origins:
          - "https://desktop.indiebi.com"
          - "https://app.indiebi.com"
        allowed_methods:
          - GET
          - OPTIONS
        max_age_in_seconds: 7200
        exposed_headers:
          - "*"
        allowed_headers:
          - "*"

cloud_platform_team_configuration:
  tags:
    team: cpt
  argocd_notifications:
    sync-succeeded: cpt-argo-cd
    sync-running: cpt-argo-cd
    sync-failed: cpt-errors
    deleted: cpt-argo-cd
    deployed: cpt-argo-cd
    health-degraded: cpt-errors

  pipeline_manager:
    workload_name: pipeline-manager
    configuration:
      service_bus_message_ttl: P14D
      backoffice_url: "https://backoffice.indiebi.com"
      error_notification_msteams_webhooks: '{"DPT":["https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/7b5bf21eea6d4c52950f1d5b6b833106/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2_3QuDVIdVplCgJB5RpunYF9dvbFaZeeHrxSi0frzfUs1"],"CPT":["https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/c47bc3f1e73943deb0d6581cc80be5af/4827773e-80a9-4979-bfea-98319be2785f/V2SuKt1GBhl0fBzYnu0XMyFPF5raPhf7RKRsU0chdPGwA1"],"SAAS":["https://indiebisa.webhook.office.com/webhookb2/10b5e8b6-658b-4993-82bc-1e5e763575f1@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/8b42f8fb208d447db70d5fe91c513290/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2eh9oDFbVVYqGV9KrL0k9F8nFyEJ16QnZA6CPSpzIADk1"],"PPT":["https://indiebisa.webhook.office.com/webhookb2/2921a668-8714-4603-832f-802a6026d724@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/bc91fa85e9fe42dd92a0c47e9cc7b2a4/9f84c475-0da9-4a3f-aaf1-7613b1531bab/V2ZQlcEtVkqu-u4KLuqqvkRvU55AfdmESkOVbEO58xXD81"]}'
      infrastructure_error_notification_msteams_webhooks: >-
        [\"https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/c47bc3f1e73943deb0d6581cc80be5af/4827773e-80a9-4979-bfea-98319be2785f/V2SuKt1GBhl0fBzYnu0XMyFPF5raPhf7RKRsU0chdPGwA1\"]
    docker_image:
      name: crindiebimain.azurecr.io/cpt/pipeline-manager
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 800Mi
      limits:
        cpu: 2000m
        memory: 2Gi

  report_service:
    workload_name: report-service
    docker_image:
      name: crindiebimain.azurecr.io/cpt/report-service
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 450Mi
      limits:
        cpu: 1000m
        memory: 1.5Gi

  user_service_v2:
    workload_name: user-service-v2
    configuration: {}
    docker_image:
      name: crindiebimain.azurecr.io/cpt/user-service-v2
      tag: prod
    kubernetes:
      requests:
        cpu: 200m
        memory: 300Mi
      limits:
        cpu: 2000m
        memory: 800Mi

  backoffice:
    workload_name: backoffice
    configuration:
      user_editor_groups: [
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # PPT, QA
      user_viewer_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
          "b4a89766-6926-4f01-8f97-5671e281fbfb",
          "622d77cc-ddc3-467a-8502-65c4bf4de546",
        ] # CPT, SAAS, DPT, PPT, QA, Finance, HR
      user_access_manager_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, SAAS, DPT, PPT, QA
      report_editor_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, DPT, PPT, QA
      report_viewer_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, SAAS, DPT, PPT, QA
      soft_reupload_editor_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, SAAS, DPT, PPT, QA
      data_sharing_viewer_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, SAAS, DPT, PPT, QA
      data_sharing_editor_groups: [
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # PPT, QA
      sku_viewer_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, SAAS, DPT, PPT, QA
      sku_editor_groups: [
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # DPT, PPT, QA
      feature_viewer_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
          "622d77cc-ddc3-467a-8502-65c4bf4de546",
        ] # CPT, SAAS, DPT, PPT, QA, HR
      feature_editor_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
          "622d77cc-ddc3-467a-8502-65c4bf4de546",
        ] # CPT, SAAS, DPT, PPT, QA, HR
      processing_manager_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, DPT, PPT, QA
      dashboard_manager_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, DPT, SAAS, QA
      key_value_editor_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "7264ab0e-c6c0-4305-a4a0-8027df6f024f",
          "a82022b2-cc7f-4570-b342-13f799c579c7",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
          "622d77cc-ddc3-467a-8502-65c4bf4de546",
        ] # CPT, SAAS, DPT, PPT, QA, HR
      partner_portal_user_groups: [
          "c8809c7d-1e66-4ccc-b807-2ad3f2cf2103",
          "0320fd79-0136-4561-a23a-4916c725e803",
          "1cded212-00e3-4674-b85f-cf4e07931a33",
        ] # CPT, PPT, QA
      msteams_webhook: "https://indiebisa.webhook.office.com/webhookb2/74172533-2be3-42f6-bb65-ce797774b540@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/9bc75593f4344931beab2be6b15edafa/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2WASCzzLLciAx57WS4v7HbJPVDyVZBco43XXBYwlnQXY1"
      synapse_database_url: "mssql+pyodbc:///?odbc_connect=Server=asa-prod-ondemand.sql.azuresynapse.net;Database=DeltaDB;Driver={ODBC Driver 18 for SQL Server};"
      partner_portal_url: "https://partner-portal.indiebi.com"
    docker_image:
      name: crindiebimain.azurecr.io/cpt/backoffice
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 200Mi
      limits:
        cpu: 350m
        memory: 1Gi # triggering full reprocessing causes OOM

  reprocessing_trigger:
    workload_name: reprocessing-trigger
    docker_image:
      name: crindiebimain.azurecr.io/cpt/reprocessing-trigger
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 100Mi
      limits:
        cpu: 100m
        memory: 100Mi

  sql_backup:
    workload_name: sql-backup
    docker_image:
      name: crindiebimain.azurecr.io/cpt/sql-backup
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 2Gi
      limits:
        cpu: 100m
        memory: 2Gi

  parser_runner:
    workload_name: parser-runner
    docker_image:
      name: crindiebimain.azurecr.io/cpt/parser-runner
      tag: prod
    kubernetes:
      requests:
        cpu: 1500m
        memory: 16Gi
      limits:
        cpu: 1500m
        memory: 16Gi

  public_data_crawler:
    workload_name: public-data-crawler
    configuration:
      apm_enabled: "True"
    docker_image:
      name: crindiebimain.azurecr.io/cpt/public-data-crawler
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 500Mi
      limits:
        cpu: 1000m
        memory: 2Gi
    replicas: 1

  partner_portal:
    workload_name: partner-portal
    docker_image:
      name: crindiebimain.azurecr.io/cpt/discount-setter
      tag: prod
    configuration:
      bitwarden_client_id: "user.0980a6c4-858f-4075-bce3-b2b90096d28f"
      bitwarden_base_collection: PPT/Automation
      raw_discounts_files:
        - name: "PPT DISCOUNTING MASTER FILE"
          folder_url: https://graph.microsoft.com/v1.0/sites/indiebisa.sharepoint.com,ce175bac-8de5-44da-903a-30941dfcd5b2,e0ed6e78-df4f-4c74-b5c3-c0a45f3f730f/drive/items
          identifier: 72707515-DC31-4544-AEBA-ED69C37E802A
        - name: "SAAS DISCOUNTING MASTER FILE"
          folder_url: https://graph.microsoft.com/v1.0/sites/indiebisa.sharepoint.com,6601026f-1785-4138-b60e-fc8b45a81fd9,3aaa1bf3-e636-4fa1-8d8c-91dce7513178/drive/items
          identifier: 4BACE671-1788-471B-AC1F-EBBB5E9E3957
      # PPT - PlayStation Discounts Alerts
      playstation_events_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/2921a668-8714-4603-832f-802a6026d724@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/5483d1ea95594cc28aebcb1b57a5358b/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2qqjzMfm1Qd5QmLjgaRWrwyYb7josa8h8jAmqLLiLpkA1",
        ]
      # CPT discount setter log channel
      user_action_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/74172533-2be3-42f6-bb65-ce797774b540@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/9bc75593f4344931beab2be6b15edafa/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2WASCzzLLciAx57WS4v7HbJPVDyVZBco43XXBYwlnQXY1",
        ]
      # CPT error channel
      errors_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/a54482b014114874819c3ee4e18d8863/2e732828-6798-41d3-963b-1707a5e49a5d/V2dg2SDga_vQZ43sZN7Hhk0rm8wnVqVeQnnWOgOK_74IU1",
        ]
      # until we fully migrate from App Service
      playstation_events_monitor_enabled: False
    kubernetes:
      requests:
        cpu: 300m
        memory: 400Mi
      limits:
        cpu: 2000m
        memory: 2.5Gi
    replicas: 1

  partner_tasks_executor:
    workload_name: partner-tasks-executor
    docker_image:
      name: crindiebimain.azurecr.io/cpt/partner-tasks-executor
      tag: prod
    configuration:
      bitwarden_client_id: "user.0980a6c4-858f-4075-bce3-b2b90096d28f"
      bitwarden_base_collection: PPT/Automation
      # PPT - PlayStation Discounts Alerts
      playstation_events_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/2921a668-8714-4603-832f-802a6026d724@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/5483d1ea95594cc28aebcb1b57a5358b/e02b1470-4290-4f77-8c08-485f8ba90eb9/V2qqjzMfm1Qd5QmLjgaRWrwyYb7josa8h8jAmqLLiLpkA1",
        ]
      # PPT - Unassigned SKUs Alerts
      unassigned_skus_teams_notification_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/2921a668-8714-4603-832f-802a6026d724@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/a60e91b63a484042bd48891b6a7bdbb7/6fb6d949-5483-4fa3-a596-4552cee30f05/V2iShYzJr5nwKs_FF6QvyOGMeorJg8iEhEB43Zd8UFFJI1",
        ]
      # CPT error channel
      errors_webhooks:
        [
          "https://indiebisa.webhook.office.com/webhookb2/96767769-1562-4d63-8512-b948d325e421@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/a54482b014114874819c3ee4e18d8863/2e732828-6798-41d3-963b-1707a5e49a5d/V2dg2SDga_vQZ43sZN7Hhk0rm8wnVqVeQnnWOgOK_74IU1",
        ]
      backoffice_url: "https://backoffice.indiebi.com"
    kubernetes:
      requests:
        cpu: 1000m
        memory: 2.5Gi
      limits:
        cpu: 1000m
        memory: 2.5Gi

data_platform_team_configuration:
  tags:
    team: dpt
  argocd_notifications:
    sync-failed: dpt-notifications
    health-degraded: dpt-notifications
    deployed: dpt-notifications

  dataset_manager:
    workload_name: dataset-manager
    configuration:
      azure_client_id: "335eac8e-6c60-4387-b987-c5278f7d1b60"
      user_profiles_service_principal_id: "0fc74812-2aca-450c-8668-c3e9f61adaf5"
      pbi_sub_id: "03db22d5-c3b5-4c7c-87c1-81da04c6283f"
      pbi_resource_group: rg-pbi-embedded-prod
      service_principal_name: dataset-manager-prod
    docker_image:
      name: crindiebimain.azurecr.io/dpt/dataset-manager
      tag: prod
    kubernetes:
      requests:
        cpu: 150m
        memory: 430Mi
      limits:
        cpu: 2000m
        memory: 2Gi

  update_shared:
    workload_name: update-shared
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/update-shared
      tag: prod
    kubernetes:
      requests:
        cpu: 250m
        memory: 1Gi
      limits:
        cpu: 250m
        memory: 1Gi

  find_shards:
    workload_name: find-shards
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/find-shards
      tag: prod
    kubernetes:
      requests:
        cpu: 150m
        memory: 100Mi
      limits:
        cpu: 150m
        memory: 100Mi

  pbi_refresh:
    workload_name: pbi-refresh
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/pbi-refresh
      tag: prod
    kubernetes:
      requests:
        cpu: 250m
        memory: 200Mi
      limits:
        cpu: 250m
        memory: 200Mi

  core_silver:
    workload_name: core-silver
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/core-silver
      tag: prod
    kubernetes:
      requests:
        cpu: 1500m
        memory: 28Gi
      limits:
        cpu: 2500m
        memory: 28Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 12800m
          memory: 112Gi
        limits:
          cpu: 12800m
          memory: 112Gi

  processing_notification:
    workload_name: processing-notification
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/processing-notification
      tag: prod
    kubernetes:
      requests:
        cpu: 150m
        memory: 100Mi
      limits:
        cpu: 150m
        memory: 100Mi

  scraper_api:
    workload_name: scraper-api
    configuration:
      lse_uri: https://stscraperbinstorag01db.blob.core.windows.net/prod-scraper-client/lse_version.json
      auth_email_forwarding_prefix: mfa-
      redis_sku_name: Standard
    docker_image:
      name: crindiebimain.azurecr.io/dpt/scraper-api
      tag: prod
    side_deployment_slot:
      workload_name: scraper-api-staging
      tag: pre-prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 200Mi
      limits:
        cpu: 250m
        memory: 400Mi

  events_service:
    workload_name: events-service
    docker_image:
      name: crindiebimain.azurecr.io/dpt/events-service
      tag: prod
    kubernetes:
      requests:
        cpu: 300m
        memory: 550Mi
      limits:
        cpu: 2000m
        memory: 5Gi
    replicas: 3

  events_service_gold:
    workload_name: events-service-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/events-service-gold
      tag: prod
    kubernetes:
      requests:
        cpu: 300m
        memory: 32Gi
      limits:
        cpu: 1500m
        memory: 32Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 12800m
          memory: 112Gi
        limits:
          cpu: 12800m
          memory: 112Gi

  cloud_reports:
    workload_name: cloud-reports
    docker_image:
      name: crindiebimain.azurecr.io/dpt/cloud-reports
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 300Mi
      limits:
        cpu: 250m
        memory: 300Mi

  dataoffice:
    workload_name: dataoffice
    docker_image:
      name: crindiebimain.azurecr.io/dpt/dataoffice
      tag: prod
    kubernetes:
      requests:
        cpu: 400m
        memory: 300Mi
      limits:
        cpu: 1000m
        memory: 500Mi

  currency_exchange_rates:
    workload_name: currency-exchange-rates
    docker_image:
      name: crindiebimain.azurecr.io/dpt/public-data-crawlers/currency-exchange-rates
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 400Mi
      limits:
        cpu: 200m
        memory: 400Mi

  scraper_service:
    configuration:
      teams_webhook_url: "https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/238f7edb7ad447e281eb403a8dc53afe/8c942920-d413-4a59-967c-8b469596af99/V2kI_IdBnWHdg66PLcZgg9yqQkcg2wC-kP5DsWpmBSrYU1"
    workload_name: scraper-service
    docker_image:
      name: crindiebimain.azurecr.io/dpt/s2
      tag: prod
    kubernetes:
      requests:
        cpu: 500m
        memory: 200Mi
      limits:
        cpu: 1000m
        memory: 600Mi
    replicas: 1

saas_team_configuration:
  tags:
    team: saas
  argocd_notifications:
    sync-failed: saas-argo-cd
    health-degraded: saas-argo-cd
    deployed: saas-argo-cd

  electron_api:
    workload_name: electron-api
    configuration:
      env: production
    legacy_network_config:
      dns_name: electron
      hostname: electron.indiebi.com
    docker_image:
      name: crindiebimain.azurecr.io/saas/electron-api
      tag: prod
    side_deployment_slot:
      workload_name: electron-api-staging
      tag: pre-prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 200Mi
      limits:
        cpu: 1000m
        memory: 800Mi

  discounts_service:
    workload_name: discounts-service
    configuration:
      env: production
      discounts_master_file_path: "Events Planner/SAAS DISCOUNTING MASTER FILE.xlsx"
    docker_image:
      name: crindiebimain.azurecr.io/saas/discounts-service
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 200Mi
      limits:
        cpu: 2000m
        memory: 1000Mi

  events_planner:
    workload_name: events-planner
    configuration:
      env: production
    docker_image:
      name: crindiebimain.azurecr.io/saas/events-planner
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 15Mi
      limits:
        cpu: 250m
        memory: 200Mi

  desktop_app_web:
    workload_name: desktop-app-web
    configuration:
      env: prod
    network:
      custom_dns_entries:
        - dns_name: app
          hostname: app.indiebi.com
        - dns_name: www.app
          hostname: www.app.indiebi.com
    docker_image:
      name: crindiebimain.azurecr.io/saas/desktop-app-web
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 190Mi
      limits:
        cpu: 500m
        memory: 500Mi

  landing:
    workload_name: landing
    configuration:
      env: production
      nuxt_public_site_env: production
      external_urls:
        user_guide: https://indiebi.notion.site/IndieBI-User-Guide-d68a4d1cb92643098085e62cb652c33a
        discord: https://discord.com/invite/WJRMe2D6Xt
    network:
      custom_dns_entries:
        - dns_name: "@"
          hostname: indiebi.com
        - dns_name: www
          hostname: www.indiebi.com
    docker_image:
      name: crindiebimain.azurecr.io/saas/indiebi-landing
      tag: prod
    kubernetes:
      requests:
        cpu: 100m
        memory: 190Mi
      limits:
        cpu: 500m
        memory: 500Mi

  mobile:
    workload_name: mobile
    configuration:
      sku: Free

  saas_gold:
    workload_name: saas-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/saas-gold
      tag: prod
    kubernetes:
      requests:
        cpu: 300m
        memory: 45Gi
      limits:
        cpu: 1500m
        memory: 45Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 12800m
          memory: 112Gi
        limits:
          cpu: 12800m
          memory: 112Gi

  direct_data_access_gold:
    workload_name: direct-data-access-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/direct-data-access-gold
      tag: prod
    kubernetes:
      requests:
        cpu: 300m
        memory: 1Gi
      limits:
        cpu: 300m
        memory: 1Gi

partner_program_team_configuration:
  tags:
    team: ppt
  argocd_notifications: # TODO: change to ppt when it will be prepared
    sync-failed: dpt-notifications
    health-degraded: dpt-notifications
    deployed: dpt-notifications

  ppt_gold:
    workload_name: ppt-gold
    docker_image:
      name: crindiebimain.azurecr.io/dpt/data-jobs/ppt-gold
      tag: prod
    kubernetes:
      requests:
        cpu: 300m
        memory: 32Gi
      limits:
        cpu: 1500m
        memory: 32Gi
    heavy_slot:
      kubernetes:
        requests:
          cpu: 12800m
          memory: 112Gi
        limits:
          cpu: 12800m
          memory: 112Gi
