import argparse
import time
import tomllib
from contextlib import contextmanager

import pyodbc


@contextmanager
def get_cursor(connection: pyodbc.Connection):
    cursor = connection.cursor()
    try:
        yield cursor
    finally:
        cursor.close()


def select(connection: pyodbc.Connection, table_name: str) -> list[pyodbc.Row]:
    stmt = f"SELECT * FROM {table_name}"
    with get_cursor(connection) as cursor:
        cursor.execute(stmt)
        rows = cursor.fetchall()
    return rows


def insert(connection: pyodbc.Connection, table_name: str, rows: list[pyodbc.Row]):
    columns = ",".join(c[0] for c in rows[0].cursor_description)
    placeholder = ",".join(["?"] * len(rows[0].cursor_description))
    stmt = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholder})"
    with get_cursor(connection) as cursor:
        cursor.fast_executemany = True
        try:
            cursor.executemany(stmt, rows)
            connection.commit()
        except (pyodbc.ProgrammingError, pyodbc.IntegrityError):
            connection.rollback()
            raise


def count(connection: pyodbc.Connection, table_name: str) -> int:
    stmt = f"SELECT COUNT(*) FROM {table_name}"
    with get_cursor(connection) as cursor:
        cursor.execute(stmt)
        count = cursor.fetchval()
    return int(count)


def toggle_identity_insert(connection: pyodbc.Connection, table_name: str, on: bool):
    stmt = f"SET IDENTITY_INSERT {table_name} {'ON' if on else 'OFF'}"
    with get_cursor(connection) as cursor:
        try:
            cursor.execute(stmt)
            connection.commit()
        except pyodbc.ProgrammingError as error:
            if error.args[0] == "42000":
                pass
            else:
                connection.rollback()
                raise

def check_contraints(connection: pyodbc.Connection, table_name: str, on: bool):
    stmt = f"ALTER TABLE {table_name} {'CHECK' if on else 'NOCHECK'} CONSTRAINT all"
    with get_cursor(connection) as cursor:
        try:
            cursor.execute(stmt)
            connection.commit()
        except pyodbc.ProgrammingError as error:
            if error.args[0] == "42000":
                pass
            else:
                connection.rollback()
                raise


def delete(connection: pyodbc.Connection, table_name: str):
    stmt = f"DELETE FROM {table_name}"
    with get_cursor(connection) as cursor:
        cursor.execute(stmt)
        try:
            connection.commit()
        except (pyodbc.ProgrammingError, pyodbc.IntegrityError):
            connection.rollback()
            raise


def clear_table(configuration: dict, table_name: str):
    destination_connection = pyodbc.connect(
        configuration["destination_connection_string"], autocommit=False
    )
    table_name_with_schema = f"[{configuration['schema']}].[{table_name}]"
    print(
        f"Clearing table '{table_name}', "
        f"{count(destination_connection, table_name_with_schema)} rows"
    )
    if count(destination_connection, table_name_with_schema) > 0:
        delete(destination_connection, table_name_with_schema)

    destination_connection.close()


def migrate_table(configuration: dict, table_name: str):
    source_connection = pyodbc.connect(
        configuration["source_connection_string"], readonly=True
    )
    destination_connection = pyodbc.connect(
        configuration["destination_connection_string"], autocommit=False
    )
    table_name_with_schema = f"[{configuration['schema']}].[{table_name}]"
    print(
        f"Migrating table '{table_name}', "
        f"{count(source_connection, table_name_with_schema)} rows"
    )
    toggle_identity_insert(destination_connection, table_name_with_schema, True)
    check_contraints(destination_connection, table_name_with_schema, False)
    insert(
        destination_connection,
        table_name_with_schema,
        select(source_connection, table_name_with_schema),
    )
    toggle_identity_insert(destination_connection, table_name_with_schema, False)
    check_contraints(destination_connection, table_name_with_schema, True)
    source_connection.close()
    destination_connection.close()


def main():
    parser = argparse.ArgumentParser(description="Database migration")
    parser.add_argument(
        "config_file", type=str, help="path to the configuration file in TOML format"
    )

    args = parser.parse_args()

    with open(args.config_file, "rb") as config_file:
        configuration = tomllib.load(config_file)

    start_time = time.perf_counter()

    for table in configuration["clear_tables"] + configuration["tables"][::-1]:
        clear_table(configuration, table)

    for table in configuration["tables"]:
        migrate_table(configuration, table)

    print(f"Total time: {time.perf_counter() - start_time} seconds")


if __name__ == "__main__":
    main()
