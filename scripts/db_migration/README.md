# Database migration script

Simple script for migrating data between databases. Copies specified tables between SQL Servers.
Assumes schema has been migrated on the destination host beforehand.

If any data already exists in the specified tables it will be removed.

Doesn't account for foreign keys, meaning list of tables needs to be provided
in the order that will ensure data is created according to the relationships.

## Dependency installation

Install `unixODBC`: https://learn.microsoft.com/en-us/sql/connect/odbc/linux-mac/installing-the-microsoft-odbc-driver-for-sql-server?view=sql-server-ver16&tabs=alpine18-install%2Cubuntu17-install%2Cdebian8-install%2Credhat7-13-install%2Crhel7-offline#17

Script uses poetry, all Python dependencies can be installed with

```bash
poetry install
```

## Configuration

Script is configured through TOML file with the following fields:

- `source_connection_string` - connection string for the source database in the form:
  `"Driver={ODBC Driver 17 for SQL Server};Server=tcp:{HOST},{PORT};Database=Report;Uid={USER};Pwd={PASSWORD};Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;"`,
- `destination_connection_string` - connection string for the destination database,
- `schema` - name of the table schema,
- `tables` - list of tables to migrate.

For example:

```toml
source_connection_string = "Driver={ODBC Driver 17 for SQL Server};Server=tcp:srt-host.database.windows.net,1433;Database=Report;Uid=src_db_user;Pwd=src_db_pass;Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;"
destination_connection_string = "Driver={ODBC Driver 17 for SQL Server};Server=tcp:dest-host.database.windows.net,1433;Database=Report;Uid=dest_db_user;Pwd=dest_db_pass;Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;"
schema = "SchemaName"
tables = ["table1", "table2", "table3"]
clear_tables = ["clear_table1", "clear_table2", "clear_table3"]
```

## Running

To start the migration execute:

```bash
poetry run python -m db_migration.cli config.toml
```

To run additional (custom) migration step - Checking missing entries in the Shared table:

```bash
poetry run python -m db_migration.check_shared config.toml
```