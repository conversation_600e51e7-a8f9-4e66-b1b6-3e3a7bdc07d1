image:
  name: crindie<PERSON><PERSON>in.azurecr.io/cpt/terragrunt-1.12.1:master
variables:
  TF_ROOT_DIR: deployments/$ENV
  TF_ROOT: $CI_PROJECT_DIR/$TF_ROOT_DIR
  TERRAFORM_BACKEND_KEY: "single_click_$ENV"


default:
  tags:
    - cpt-azure-infra

stages:
  - prepare
  - plan
  - apply

# install ssh agent in container and add ssh key
# (needed for jobs using Docker executor)
.inject_ssh_config:
  before_script:
    - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - chmod 400 "$TF_LIB_SSH_PRIVATE_KEY"
    - ssh-add "$TF_LIB_SSH_PRIVATE_KEY"
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -t rsa gitlab.com >> ~/.ssh/known_hosts

fmt:
  stage: prepare
  script:
    - cd ${CI_PROJECT_DIR}/modules
    - terraform fmt -recursive -check
    - cd ${TF_ROOT}
    - terragrunt hclfmt --terragrunt-check
  rules:
    -  when: always

validate:
  stage: prepare
  extends: .inject_ssh_config
  script:
    - cd ${TF_ROOT}
    - terragrunt validate
  rules:
    -  when: always

plan:
  stage: plan
  extends: .inject_ssh_config
  script:
    - cd ${TF_ROOT}
    - terragrunt plan -out=plan.tfplan
  artifacts:
    paths:
      - ${TF_ROOT}/**/plan.tfplan
  rules:
    -  when: always

apply:
  stage: apply
  extends: .inject_ssh_config
  script:
    - cd ${TF_ROOT}
    - terragrunt apply --terragrunt-non-interactive plan.tfplan
  rules:
    - when: manual
  # this is to allow merging the MR if the manual action has not been executed
  allow_failure: true
