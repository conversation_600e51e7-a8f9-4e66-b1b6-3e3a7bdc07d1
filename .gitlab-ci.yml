default:
  tags:
    - cpt-azure-infra

stages:
  - security
  - deploy-to-env

include:
  - "gitlab-ci/pipeline-triggerer.yml"

security-checkov:
  stage: security
  image:
    name: bridgecrew/checkov:3
    entrypoint:
      - "/usr/bin/env"
      - "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
  before_script:
    - apt-get update && apt-get install -y gettext-base
  script:
    # the skip check for CKV_TF_1 is because we want to refer to tags rather than commit hashes required by checkov
    - checkov -d ${CI_PROJECT_DIR} --quiet -o cli -o junitxml -o json --framework kubernetes terraform --download-external-modules true --skip-check CKV_TF_1 --output-file-path console,checkov.test.xml,checkov.test.json
  rules: !reference [.mr-or-main, rules]
  artifacts:
    reports:
      junit: "checkov.test.xml"
      sast: "checkov.test.json"
    paths:
      - "checkov.test.xml"
      - "checkov.test.json"
      
dev:
  extends: .env-pipeline
  variables:
    ENV: dev
    GITLAB_ACCESS_TOKEN: "$GITLAB_PROJECT_ACCESS_TOKEN"
    ARM_TENANT_ID: bb7887c7-c8cd-4f3d-b602-2e270710fdb9
    ARM_SUBSCRIPTION_ID: 974be8a6-83ee-4087-b47d-7cd8424ba8e5
    ARM_USE_MSI: true
    SQL_AD_ADMIN_PASSWORD: "$GITLAB_SP_DEV_SECRET"
  rules: !reference [.mr-or-main, rules]

prod:
  extends: .env-pipeline
  variables:
    ENV: prod
    GITLAB_ACCESS_TOKEN: "$GITLAB_PROJECT_ACCESS_TOKEN"
    ARM_TENANT_ID: bb7887c7-c8cd-4f3d-b602-2e270710fdb9
    ARM_USE_MSI: true
    ARM_SUBSCRIPTION_ID: c4d9d53d-61a4-4362-be32-4d242ba17160
    SQL_AD_ADMIN_PASSWORD: "$GITLAB_SP_PROD_SECRET"
  rules: !reference [.main, rules]

