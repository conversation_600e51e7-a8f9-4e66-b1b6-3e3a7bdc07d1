variable "common" {
  description = <<EOT
  Common configuration

  {
    tenant_id: "ID of the Azure tenat"
    location: "Location where resources will be provisioned"
    firewall_public_ip_address: "Firewall/VPN VM public IP address"
    vpn_address_space: "Address space reserved for VPN-connected devices"
    admin_contact_email: "Admin email address used for various notifications and alerts"
    secret_store_name: "Name for the External Secrets global secret store name"
    keys_secrets_expiration_date: "Expiration date set for secrets created in the Key Vault"
    connectivity_subscription_id: "ID of the indieBI enterprise-scale connectivity subscription"
    management_subscription_id: "ID of the indieBI enterprise-scale management subscription"
    acr_resource_id: "ID of Azure Container Registry from which AKS would fetch images"
    service_principals: "A dictionary of Service principals that should have some permissions assigned with their name and Entra object ID"
    teams: "A dictionary of teams configurations containing objects with their name and Entra object ID"
    backup: "Configuration for storage backup"
  }
  EOT
  type = object({
    tenant_id                    = string
    location                     = string
    firewall_public_ip_address   = string
    vpn_address_space            = string
    admin_contact_email          = string
    secret_store_name            = string
    keys_secrets_expiration_date = string
    connectivity_subscription_id = string
    management_subscription_id   = string
    acr_resource_id              = string
    service_principals = object({
      dbw_indiebi = object({
        name      = string
        object_id = string
      })
    })
    teams = object({
      cpt = object({
        name = string
        azure_group = object({
          name      = string
          object_id = string
        })
        team_leader = object({
          name      = string
          object_id = string
        })
        runner_identity = object({
          name      = string
          object_id = string
        })
      })
      dpt = object({
        name = string
        azure_group = object({
          name      = string
          object_id = string
        })
        team_leader = object({
          name      = string
          object_id = string
        })
        runner_identity = object({
          name      = string
          object_id = string
        })
      })
      saas = object({
        name = string
        azure_group = object({
          name      = string
          object_id = string
        })
        team_leader = object({
          name      = string
          object_id = string
        })
        runner_identity = object({
          name      = string
          object_id = string
        })
      })
      ppt = object({
        name = string
        azure_group = object({
          name      = string
          object_id = string
        })
        team_leader = object({
          name      = string
          object_id = string
        })
        runner_identity = object({
          name      = string
          object_id = string
        })
      })
    })
    backup = object({
      vault_id  = string
      policy_id = string
    })
  })

  validation {
    condition     = can(regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}/\\d{1,2}$", var.common.vpn_address_space))
    error_message = "VPN subnet address space is not in CIDR format"
  }

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.common.teams.cpt.azure_group.object_id))
    error_message = "CPT object ID must be a valid UUID4"
  }

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.common.teams.dpt.azure_group.object_id))
    error_message = "DPT object ID must be a valid UUID4"
  }

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.common.teams.saas.azure_group.object_id))
    error_message = "SAAS object ID must be a valid UUID4"
  }

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.common.teams.cpt.team_leader.object_id))
    error_message = "CPT team leader object ID must be a valid UUID4"
  }

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.common.teams.dpt.team_leader.object_id))
    error_message = "DPT team leader object ID must be a valid UUID4"
  }

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.common.teams.saas.team_leader.object_id))
    error_message = "SAAS team leader object ID must be a valid UUID4"
  }
}

variable "environment" {
  description = <<EOT
  Environment configuration

  {
    name: "Name of the environment"
    domain: "Domain used by services deployed in the environment"
    dns_extension: "Custom postfix added to DNS records to distinguish envs other than dev/prod
    subscription_id: "Environment specific Azure subscription ID"
    is_private: "Flag deciding if env is available to the outside world or requires VPN for all services"
  }
  EOT
  type = object({
    name            = string
    domain          = string
    dns_extension   = optional(string)
    subscription_id = string
    is_private      = bool
  })
}

variable "aks_configuration" {
  description = <<EOT
  Configuration for Azure Kubernetes Service
  {
    sku_tier: "SKU SLA tier"
    default_node_pool:
      vm_size: "VM size of default node pool"
      min_count: "Min count of default node pool"
      max_count: "Max count of default node pool"
    data_jobs_node_pool:
      vm_size: "VM size of data jobs dedicated node pool"
      min_count: "Min count of data jobs dedicated node pool"
      max_count: "Max count of data jobs dedicated node pool"
      max_pods: "Maximum number of pods on each node"
    web_apps_node_pool:
      vm_size: "VM size of web apps dedicated node pool"
      min_count: "Min count of web apps dedicated node pool"
      max_count: "Max count of web apps dedicated node pool"
    namespace_admins:
      namespace: "Name of the Kubernetes namespace"
      principal_id: "Admin object ID (can represent either group or single user)"
  }
  EOT
  type = object({
    sku_tier = optional(string, "Free")
    default_node_pool = object({
      vm_size   = string
      min_count = number
      max_count = number
    })
    data_jobs_node_pool = object({
      vm_size   = string
      min_count = number
      max_count = number
      max_pods  = number # CKV_AZURE_168 suggests setting it to at least 50
    })
    web_apps_node_pool = object({
      vm_size   = string
      min_count = number
      max_count = number
    })
    namespace_admins = list(object({
      namespace    = string
      principal_id = string
    }))
  })
}

variable "argocd_configuration" {
  description = <<EOT
  Configuration for Argo CD.

  {
    ad_app_owners: "Active Directory object IDs for owners of the created Enterprise Application, Needed only for envs not fired through CI (using local credentials)"
    allowed_login_group: "Object ID of AD group that is allowed to login into Argo CD"
    helm_repository: "URL to IndeBI's Helm repository"
    rbac_policy: "RBAC policy file suffix, this file can be found in argocd_core sub module"
    argocd_notification_channels: "Argo CD channel names and their webhook URLs for sending Argo CD notifications"
  }
  EOT
  type = object({
    ad_app_owners                = optional(list(string), [])
    allowed_login_group          = string
    helm_repository              = optional(string, "https://gitlab.com/api/v4/projects/44953895/packages/helm/stable")
    rbac_policy                  = string
    argocd_notification_channels = map(string)
  })

  validation {
    condition     = var.argocd_configuration.rbac_policy == "strict" || var.argocd_configuration.rbac_policy == "relaxed"
    error_message = "Only 'strict' or 'relaxed' suffixes are accepted"
  }
  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.argocd_configuration.allowed_login_group))
    error_message = "Login group must be a valid UUID4"
  }
}

variable "prometheus_configuration" {
  description = <<EOT
  Prometheus configuration values.

  {
    storage_size: "Size (with unit) of the persistent volume for metrics"
    grafana:
      image:
        tag: "Tag of Grafana release"
      admins: "List of objects IDs (users/groups) that will have Admin role assigned"
      editors: "List of objects IDs (users/groups) that will have Editor role assigned"
      viewers: "List of objects IDs (users/groups) that will have Viewer role assigned"
      alerting:
        team_name:
          contact_point: "MS Teams webhook"
          custom_policies: "List of objects representing custom policies"
          mute_times: "Map of objects representing muted times"
  }
  EOT
  type = object({
    storage_size   = string
    retention      = string
    retention_size = string
    grafana = object({
      image = object({
        tag = string
      })
      admins  = set(string)
      editors = optional(set(string), [])
      viewers = optional(set(string), [])
      alerting = object({
        cpt = object({
          contact_point = string
          mute_times = optional(map(list(object({
            times = list(object({
              start_time = string
              end_time   = string
            }))
            location = optional(string, "UTC")
          }))), {})
          custom_policies = optional(list(object({
            matchers            = set(string)
            mute_time_intervals = set(string)
          })), [])
        })
        dpt = object({
          contact_point = string
          mute_times = optional(map(list(object({
            times = list(object({
              start_time = string
              end_time   = string
            }))
            location = optional(string, "UTC")
          }))), {})
          custom_policies = optional(list(object({
            matchers            = set(string)
            mute_time_intervals = set(string)
          })), [])
        })
        saas = object({
          contact_point = string
          mute_times = optional(map(list(object({
            times = list(object({
              start_time = string
              end_time   = string
            }))
            location = optional(string, "UTC")
          }))), {})
          custom_policies = optional(list(object({
            matchers            = set(string)
            mute_time_intervals = set(string)
          })), [])
        })
      })
    })
  })
}

variable "infra_admins" {
  description = "List of objects ID of users/groups that are admins of the whole infrastructure"
  type        = list(string)
}

variable "infra_readers" {
  description = "List of objects ID of users/groups that have read access to the infrastructure"
  type        = list(string)
}

variable "client_credentials_vault_admins" {
  description = "List of objects ID of users/groups that are admins of the client credential vault"
  type        = list(string)
}

variable "gitlab_username" {
  description = <<EOT
  GitLab access token name for Helm repository.

  This value is merged with `gitlab_password` into `registry_configuration` and is passed to the sub modules
  EOT
  type        = string
  nullable    = false
}

variable "gitlab_password" {
  description = <<EOT
  GitLab access token value for Helm repository. Should be populated through environment variable."

  This value is merged with `gitlab_username` into `registry_configuration` and is passed to the sub modules

  Should be populated through environment variable.
  EOT
  type        = string
  nullable    = false
  sensitive   = true
}

variable "top_dns_zone" {
  description = <<EOT
  Information about top DNS zone (defined in connectivity subscription)

  {
    name: "DNS zone name"
    resource_group_name: "Name of the resource group with DNS zone"
  }
  EOT
  type = object({
    name                = string
    resource_group_name = string
  })
}

variable "vnet" {
  description = <<EOT
  Environment's virtual network configuration (virtual network, address space reserved for subnet)

  {
    name: "Name of the virtual network"
    rg_name: "Resource group name in which vnet is deployed"
    workload_subnet_address_space: "Address space used for the workload subnet"
    function_app_subnet_address_space: "Address space used for Function App integrated subnet"
    app_gateway_subnet_address_space: "Address space used for the application gateway subnet"
    app_gateway_private_ip: "Static private IP used for internal services"
    firewall_private_ip: "Private IP of the Firewall"
    default_route_table: "Default routing table name"
    app_gateway_capacity_units: "Number of capacity units provisioned in Application Gateway"
    app_gateway_response_buffering_enabled: "Enable or disable response buffering in Application Gateway"
  }

  EOT
  type = object({
    name                                   = string
    rg_name                                = string
    workload_subnet_address_space          = string
    function_app_subnet_address_space      = string
    app_gateway_subnet_address_space       = string
    postgresql_subnet_address_space        = string
    app_gateway_private_ip                 = string
    firewall_private_ip                    = string
    default_route_table                    = string
    app_gateway_capacity_units             = number
    app_gateway_response_buffering_enabled = bool
  })

  validation {
    condition     = can(regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}/\\d{1,2}$", var.vnet.workload_subnet_address_space))
    error_message = "Workload subnet address space is not in CIDR format"
  }

  validation {
    condition     = can(regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}/\\d{1,2}$", var.vnet.function_app_subnet_address_space))
    error_message = "Function App subnet address space is not in CIDR format"
  }

  validation {
    condition     = can(regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}/\\d{1,2}$", var.vnet.app_gateway_subnet_address_space))
    error_message = "App Gateway subnet address space is not in CIDR format"
  }

  validation {
    condition     = can(regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}/\\d{1,2}$", var.vnet.postgresql_subnet_address_space))
    error_message = "Postgresql subnet address space is not in CIDR format"
  }

  validation {
    condition     = can(regex("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$", var.vnet.firewall_private_ip))
    error_message = "Invalid firewall IP format"
  }
}

variable "sql_ad_admin" {
  description = <<EOT
  Azure Active Directory Service Principal used as admin for Azure SQL databases

  {
    login_name: "Name of the Service Principal"
    app_id: "Service Principal Application ID"
    server_identity_id: "ID of the User Assigned Managed Identity that will be assigned to SQL servers"
  }

  This value is merged with `sql_ad_admin_password` into `sql_ad_admin_with_password` and is passed to the sub modules

  EOT
  type = object({
    login_name         = string
    app_id             = string
    server_identity_id = string
  })
  nullable = false

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.sql_ad_admin.app_id))
    error_message = "Application ID must be a valid UUID4"
  }

  validation {
    condition     = length(split("/", var.sql_ad_admin.server_identity_id)) == 9
    error_message = "Server identity ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.ManagedIdentity/userAssignedIdentities/<identity_name>"
  }
}

variable "sql_ad_admin_password" {
  description = <<EOT
  Client secret for Azure Active Directory Service Principal used as admin for Azure SQL databases
  This value is merged with `sql_ad_admin` into `sql_ad_admin_with_password` and is passed to the sub modules

  Should be populated through environment variable.
  EOT
  type        = string
  sensitive   = true
}

variable "persistence_configuration" {
  description = <<EOT
  Configuration for resources provisioned as a part of persistence stack

  {
    databases: "List of sql server databases that will be provisioned"
    postgresql: "Postgresql server configuraiton"
  }
  Each sql server database is configured through:
  {
    workload_name: "Name of the workload which will use the database"
    sku_name: "SKU of the Azure SQL Database. To find the list of available tiers run `az sql db list-editions -l <location> -o table`"
    max_size_gb: "Maximum size of the database"
  }
  Postgresql server is configured through:
  {
    "max_size_mb": "Max storage allowed for a server."
    "storage_tier": "The name of storage performance tier for IOPS of the PostgreSQL Flexible Server."
    "sku_name": "Specifies the SKU Name for this PostgreSQL Server. The name of the SKU, follows the tier + family + cores pattern (e.g. B_Gen4_1, GP_Gen5_8)."
    "databases": "List containing configuration of individual databases deployed in that server. "
  }
  Postgresql database is configured through:
  {
    workload_name: "Name of the workload which will use the database"
  }
  Module federation storage account is configured through:
  {
    cors_rules: List of CORS rule objects 
  }
  EOT
  type = object({
    databases = list(
      object({
        workload_name = string
        sku_name      = string
        max_size_gb   = number
        }
    ))
    postgresql = list(
      object({
        server_name  = string
        max_size_mb  = number
        storage_tier = string
        sku_name     = string
        databases    = list(object({ workload_name = string }))
        }
    ))
    module_federation_storage_account = object({
      cors_rules = list(object({
        allowed_origins    = list(string)
        allowed_methods    = list(string)
        max_age_in_seconds = number
        exposed_headers    = list(string)
        allowed_headers    = list(string)
      }))
    })

  })
}

variable "cloud_platform_team_configuration" {
  description = <<EOT
  Cloud Platform Team's configuration

  Backoffice configuration:
  {
    backoffice:
      configuration:
        feature_manager_groups: "List of groups (object IDs) that can manage features"
        processing_manager_groups: "List of groups (object IDs) that can manage processing"
        report_manager_groups: "List of groups (object IDs) that can manage reports"
        user_manager_groups: "List of groups (object IDs) that can manage users"
        msteams_webhook: "MS Teams webhook for maintanence channel"
  }

  Pipeline Manager configuration:
  {
    pipeline_manager:
      configuration:
        service_bus_message_ttl: "Service Bus Message time to live"
  }

  User Service configuration:
  {
    user_service_v2:
      configuration:
        allow_impersonate_access_token_expiry_override: "Allow overriding of access token expiry"
  }
  Partner Portal configuration:
  {
    partner_portal:
      configuration:
        bitwarden_client_id: "Bitwarden client ID"
        bitwarden_base_collection: "Collection in Bitwarden from which the client credentials are synchronized"
        raw_discounts_files: "List of objects representing discount files"
          name: "Discount file name"
          folder_url: "URL pointing to the folder with discount master file of the form: https://graph.microsoft.com/v1.0/sites/indiebisa.sharepoint.com,<site-id>,<web-id>/drive/items"
          identifier: "File ID of the discount master file"
        user_action_teams_notification_webhooks: "List of Teams webhooks for user actions notifications"
        organization_override: "Organization ID that will override ones defined in Bitwarden, for dev purposes only"
  }
  Partner Task Executor configuration:
  {
    partner_tasks_executor:
      configuration:
        bitwarden_client_id: "Bitwarden client ID"
        bitwarden_base_collection: "Collection in Bitwarden from which the client credentials are synchronized"
        playstation_events_teams_notification_webhooks: "List of Teams webhooks for PlayStation events notifications"
        unassigned_skus_teams_notification_webhooks: "List of Teams webhooks for unassigned SKUs notifications"
        errors_webhooks: "List of Teams webhooks for error notifications"
        organization_override: "Organization ID that will override ones defined in Bitwarden, for dev purposes only"
        backoffice_url: "URL to Backoffice"
  }
  EOT
  type = object({
    tags                 = map(string)
    argocd_notifications = map(string)

    backoffice = object({
      workload_name = string
      configuration = object({
        user_editor_groups          = list(string)
        user_viewer_groups          = list(string)
        user_access_manager_groups  = list(string)
        report_editor_groups        = list(string)
        report_viewer_groups        = list(string)
        soft_reupload_editor_groups = list(string)
        data_sharing_viewer_groups  = list(string)
        data_sharing_editor_groups  = list(string)
        sku_viewer_groups           = list(string)
        sku_editor_groups           = list(string)
        feature_viewer_groups       = list(string)
        feature_editor_groups       = list(string)
        processing_manager_groups   = list(string)
        dashboard_manager_groups    = list(string)
        key_value_editor_groups     = list(string)
        partner_portal_user_groups  = list(string)
        msteams_webhook             = string
        synapse_database_url        = string
        partner_portal_url          = string
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    pipeline_manager = object({
      workload_name = string
      configuration = object({
        service_bus_message_ttl                            = string
        backoffice_url                                     = string
        error_notification_msteams_webhooks                = optional(string, "")
        infrastructure_error_notification_msteams_webhooks = optional(string, "")
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    report_service = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    user_service_v2 = object({
      workload_name = string
      configuration = object({
        allow_impersonate_access_token_expiry_override = optional(bool, false)
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    reprocessing_trigger = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    partner_tasks_executor = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      configuration = object({
        bitwarden_client_id                            = string
        bitwarden_base_collection                      = string
        playstation_events_teams_notification_webhooks = list(string)
        unassigned_skus_teams_notification_webhooks    = list(string)
        errors_webhooks                                = list(string)
        organization_override                          = optional(string, "")
        backoffice_url                                 = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    sql_backup = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    parser_runner = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    public_data_crawler = object({
      workload_name = string
      configuration = object({
        apm_enabled = string
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      replicas = number
    })

    partner_portal = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      configuration = object({
        bitwarden_client_id       = string
        bitwarden_base_collection = string
        raw_discounts_files = list(object({
          name       = string
          folder_url = string
          identifier = string
        }))
        user_action_teams_notification_webhooks = list(string)
        organization_override                   = optional(string, "")
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      replicas = number
    })
  })
}

variable "data_platform_team_configuration" {
  description = <<EOT
  Data Platform Team's configuration

  Dataset Manager configuration:
  {
    dataset_manager:
      configuration:
        azure_client_id: "Power BI service princlinal client ID"
        pbi_sub_id: "Power BI specific subscription ID"
        pbi_resource_group: "Name of the resource group where Power BI capacities are deployed"
        service_principal_name: "Service principal name"
        user_profiles_service_principal_id: "Service principal object ID"
  }
  Scraper API configuration:
  {
    scraper_api:
      configuration:
        lse_uri: "URI pointing to JSON file with latest stable edition of scraper client"
        auth_email_forwarding_prefix: "Prefix uses in user forward emails"
        redis_sku_name: "Tier type of Redis cache"
  }
  EOT
  type = object({
    tags                 = map(string)
    argocd_notifications = map(string)

    dataset_manager = object({
      workload_name = string
      configuration = object({
        azure_client_id                    = string
        pbi_sub_id                         = string
        pbi_resource_group                 = string
        service_principal_name             = string
        user_profiles_service_principal_id = string
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    update_shared = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    find_shards = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    pbi_refresh = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    core_silver = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      heavy_slot = object({
        kubernetes = object({
          requests = object({
            cpu    = string
            memory = string
          })
          limits = object({
            cpu    = string
            memory = string
          })
        })
      })
    })

    events_service_gold = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      heavy_slot = object({
        kubernetes = object({
          requests = object({
            cpu    = string
            memory = string
          })
          limits = object({
            cpu    = string
            memory = string
          })
        })
      })
    })

    processing_notification = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    scraper_api = object({
      workload_name = string
      configuration = object({
        lse_uri                      = string
        auth_email_forwarding_prefix = string
        redis_sku_name               = string
      })
      docker_image = object({
        name = string
        tag  = string
      })
      side_deployment_slot = object({
        workload_name = string
        tag           = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    cloud_reports = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    events_service = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      replicas = number
    })

    dataoffice = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    currency_exchange_rates = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    scraper_service = object({
      configuration = object({
        teams_webhook_url = string
      })
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      replicas = number
    })
  })
}

variable "saas_team_configuration" {
  description = <<EOT
  SaaS Team's configuration

  Landing page configuration:
  {
    landing:
      configuration:
        external_urls:
          user_guide: "URL pointing to IndieBI User Guide"
          discord: "URL with invite to IndieBI Discord server"
        nuxt_public_site_env: "production | xxx -> when set to production, enables seo module to generate robots.txt file without deny"
        network:
          custom_dns_entries:
            dns_name: "DNS name to be used in DNS Zone instead of default name and env_dns_extension combinantion"
            hostname: "Custom hostname to use for landing e.g. indiebi.dev"
    mobile:
      configuration:
        sku: "SKU size of the provisioned Static Web App Service"
  }

  EOT
  type = object({
    tags                 = map(string)
    argocd_notifications = map(string)

    electron_api = object({
      workload_name = string
      configuration = object({
        env = string
      })
      legacy_network_config = object({
        dns_name = optional(string)
        hostname = optional(string)
      })
      docker_image = object({
        name = string
        tag  = string
      })
      side_deployment_slot = object({
        workload_name = string
        tag           = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })


    discounts_service = object({
      workload_name = string
      configuration = object({
        env                        = string
        discounts_master_file_path = string
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    saas_monitoring = optional(object({
      workload_name = string

      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    }))

    landing = object({
      workload_name = string

      configuration = object({
        env = string
        external_urls = object({
          user_guide = string
          discord    = string
        })
        nuxt_public_site_env = string
      })
      network = object({
        custom_dns_entries = list(object({
          dns_name = string
          hostname = string
        }))
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    events_planner = object({
      workload_name = string

      configuration = object({
        env = string
      })
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    desktop_app_web = object({
      workload_name = string

      configuration = object({
        env = string
      })
      docker_image = object({
        name = string
        tag  = string
      })
      network = object({
        custom_dns_entries = list(object({
          dns_name = string
          hostname = string
        }))
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })

    mobile = optional(object({
      workload_name = string
      configuration = object({
        sku = string
      })
    }))

    saas_gold = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      heavy_slot = object({
        kubernetes = object({
          requests = object({
            cpu    = string
            memory = string
          })
          limits = object({
            cpu    = string
            memory = string
          })
        })
      })
    })
    direct_data_access_gold = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })
  })
}

variable "partner_program_team_configuration" {
  description = <<EOT
  PPT Team's configuration
  EOT
  type = object({
    tags                 = map(string)
    argocd_notifications = map(string)

    ppt_gold = object({
      workload_name = string
      docker_image = object({
        name = string
        tag  = string
      })
      kubernetes = object({
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      heavy_slot = object({
        kubernetes = object({
          requests = object({
            cpu    = string
            memory = string
          })
          limits = object({
            cpu    = string
            memory = string
          })
        })
      })
    })
  })
}

variable "private_dns_zone_ids" {
  description = <<EOT
    IDs of private DNS zones

    {
      redis: "Redis Cache private DNS zone ID"
      sql_server: "SQL server databases private DNS zone ID"
      vault: "Key vault private link DNS zone ID"
      blob: "Blob storage account private link DNS zone ID"
      table: "Table storage account private link DNS zone ID"
      dls: "Data lake storage account private link DNS zone ID"
    }
  EOT

  type = object({
    redis      = string
    sql_server = string
    vault      = string
    blob       = string
    table      = string
    dls        = string
    postgres   = string
  })

  validation {
    condition     = length(split("/", var.private_dns_zone_ids["redis"])) == 9
    error_message = "Redis Cache private DNS zone ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.Network/privateDnsZones/<zone_name>"
  }

  validation {
    condition     = length(split("/", var.private_dns_zone_ids["sql_server"])) == 9
    error_message = "SQL server private DNS zone ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.Network/privateDnsZones/<zone_name>"
  }

  validation {
    condition     = length(split("/", var.private_dns_zone_ids["vault"])) == 9
    error_message = "Key vault private DNS zone ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.Network/privateDnsZones/<zone_name>"
  }

  validation {
    condition     = length(split("/", var.private_dns_zone_ids["blob"])) == 9
    error_message = "Storage Blob private DNS zone ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.Network/privateDnsZones/<zone_name>"
  }

  validation {
    condition     = length(split("/", var.private_dns_zone_ids["table"])) == 9
    error_message = "Table storage private DNS zone ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.Network/privateDnsZones/<zone_name>"
  }

  validation {
    condition     = length(split("/", var.private_dns_zone_ids["dls"])) == 9
    error_message = "Data Lake Storage private DNS zone ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.Network/privateDnsZones/<zone_name>"
  }

  validation {
    condition     = length(split("/", var.private_dns_zone_ids["postgres"])) == 9
    error_message = "Postgre private DNS zone ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource_group_name>/providers/Microsoft.Network/privateDnsZones/<zone_name>"
  }
}
