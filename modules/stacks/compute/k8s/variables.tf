variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "aks_configuration" {
  type = any
}

variable "infra_admins" {
  type = list(string)
}

variable "argocd_configuration" {
  type = any
}

variable "prometheus_configuration" {
  type = any
}

variable "registry_configuration" {
  type = any
}

variable "network_resource_group" {
  type = map(any)
}

variable "dns_zone_id" {
  type = string
}

variable "cert_issuer_name" {
  type    = string
  default = "letsencrypt-issuer"
}

variable "tags" {
  type = map(string)
}

variable "application_gateway" {
  type = map(string)
}

variable "top_dns_zone" {
  type = map(any)
}

variable "subnet_config" {
  type = object({
    id                        = string
    network_security_group_id = string
  })
}

variable "central_key_vault" {
  type = object({
    id                  = string
    name                = string
    uri                 = string
    resource_group_name = string
  })
}

