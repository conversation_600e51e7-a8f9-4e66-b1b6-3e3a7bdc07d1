terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

resource "azurerm_resource_group" "rg_k8s" {
  name     = "rg-k8s-${var.environment.name}"
  location = var.common.location
  tags     = var.tags
}

module "aks" {
  source                    = "./aks"
  resource_group            = azurerm_resource_group.rg_k8s.name
  common                    = var.common
  network_resource_group_id = var.network_resource_group.id
  configuration             = var.aks_configuration
  infra_admins              = var.infra_admins
  environment               = var.environment
  application_gateway       = var.application_gateway
  subnet_config             = var.subnet_config
  tags                      = var.tags
}

module "argocd_core" {
  source                  = "./argocd_core"
  resource_group          = azurerm_resource_group.rg_k8s.name
  registry_configuration  = var.registry_configuration
  cert_issuer_name        = var.cert_issuer_name
  configuration           = var.argocd_configuration
  common                  = var.common
  environment             = var.environment
  top_dns_zone            = var.top_dns_zone
  ip_address              = var.application_gateway.private_ip
  cluster_oidc_issuer_url = module.aks.oidc_issuer_url
  tags                    = var.tags
  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  depends_on = [module.aks]
}

module "keda_core" {
  source                  = "./keda_core"
  common                  = var.common
  environment             = var.environment
  tags                    = var.tags
  resource_group          = azurerm_resource_group.rg_k8s.name
  cluster_oidc_issuer_url = module.aks.oidc_issuer_url
}

module "eso_core" {
  source = "./eso_core"

  depends_on = [module.aks]
}

locals {
  secret_store_name = "indiebi-secret-store"
}

module "indiebi_eso" {
  source                  = "../../../components/external_secrets"
  tenant_id               = var.common.tenant_id
  environment             = var.environment.name
  location                = var.common.location
  cluster_oidc_issuer_url = module.aks.oidc_issuer_url
  kubernetes_namespace    = "external-secrets"
  key_vault               = var.central_key_vault
  secret_store_name       = local.secret_store_name

  tags       = var.tags
  depends_on = [module.eso_core]
}


module "cert_manager" {
  source                  = "./cert_manager"
  top_dns_zone            = var.top_dns_zone
  dns_zone_id             = var.dns_zone_id
  common                  = var.common
  environment             = var.environment
  cert_issuer_name        = var.cert_issuer_name
  tags                    = var.tags
  cluster_oidc_issuer_url = module.aks.oidc_issuer_url
  resource_group          = azurerm_resource_group.rg_k8s.name
}

module "prometheus" {
  source                  = "./prometheus"
  cluster_oidc_issuer_url = module.aks.oidc_issuer_url
  resource_group          = azurerm_resource_group.rg_k8s.name
  common                  = var.common
  environment             = var.environment
  configuration           = var.prometheus_configuration
  top_dns_zone            = var.top_dns_zone
  ip_address              = var.application_gateway.private_ip
  tags                    = var.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "reloader" {
  source = "./reloader"

  depends_on = [module.aks]
}

module "elastic" {
  source = "./elastic"

  common            = var.common
  key_vault_id      = var.central_key_vault.id
  infra_admins      = var.infra_admins
  secret_store_name = local.secret_store_name

  depends_on = [module.aks]
}
