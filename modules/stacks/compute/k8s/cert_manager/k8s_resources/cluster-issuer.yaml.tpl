apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: ${cert_issuer_name}
spec:
  acme:
    server: ${acme_server}
    email: ${expiration_contact_email}
    privateKeySecretRef:
      name: ${cert_issuer_name}
    solvers:
    - dns01:
        azureDNS:
          resourceGroupName: ${domain_rg}
          subscriptionID: ${subscription_id}
          hostedZoneName: "${domain_name}"
          environment: AzurePublicCloud
          managedIdentity:
            clientID: ${uaid_client_id}