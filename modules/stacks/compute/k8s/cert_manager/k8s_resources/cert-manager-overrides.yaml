podLabels:
  azure.workload.identity/use: "true"
serviceAccount:
  labels:
    azure.workload.identity/use: "true"
prometheus:
  enabled: true
  servicemonitor:
    enabled: true

resources:
  requests:
    memory: 70Mi
    cpu: 10m
  limits:
    memory: 256Mi

webhook:
  resources:
    requests:
      memory: 16Mi
      cpu: 10m
    limits:
      memory: 32Mi

cainjector:
  resources:
    requests:
      memory: 96Mi
      cpu: 10m
    limits:
      memory: 256Mi