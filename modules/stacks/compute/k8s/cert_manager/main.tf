resource "helm_release" "cert_manager" {
  name = "cert-manager"

  repository       = "https://charts.jetstack.io"
  chart            = "cert-manager"
  namespace        = "cert-manager"
  create_namespace = true
  version          = "1.12.6"

  set {
    name  = "installCRDs"
    value = "true"
  }

  # this is required since we are using private DNS zones and cert-manager makes validation queries for TXT records (from within the cluster
  # so using private DNS zone where they don't exist) before it lets ACME send challenge from outside. so we force cert-manager to use external DNS instead
  # https://stackoverflow.com/a/66042994/2209329
  set {
    name  = "extraArgs"
    value = "{--logging-format=json,--dns01-recursive-nameservers-only,--dns01-recursive-nameservers=8.8.8.8:53\\,1.1.1.1:53}"
  }

  set {
    name  = "webhook.extraArgs"
    value = "{--logging-format=json}"
  }

  set {
    name  = "cainjector.extraArgs"
    value = "{--logging-format=json}"
  }

  values = [
    "${file("./compute/k8s/cert_manager/k8s_resources/cert-manager-overrides.yaml")}"
  ]

}

resource "azurerm_user_assigned_identity" "cert_manager_identity" {
  resource_group_name = var.resource_group
  location            = var.common.location
  name                = "id-cert-manager-${var.environment.name}"
  tags                = var.tags
}

resource "azurerm_role_assignment" "cert_manager_role_assignment_dns_zone" {
  scope                = var.dns_zone_id
  role_definition_name = "DNS Zone Contributor"
  principal_id         = azurerm_user_assigned_identity.cert_manager_identity.principal_id
}

resource "azurerm_federated_identity_credential" "federated_identity_credential" {
  name                = "fic-cert-manager-${var.environment.name}"
  resource_group_name = var.resource_group
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.cert_manager_identity.id
  subject             = "system:serviceaccount:cert-manager:cert-manager"
}

resource "helm_release" "cluster_issuer" {
  name      = "${var.cert_issuer_name}-cluster-issuer"
  chart     = "../components/kubernetes-resource"
  namespace = helm_release.cert_manager.namespace

  values = [
    <<-EOF
configurations:
  - ${indent(
    4,
    templatefile("./compute/k8s/cert_manager/k8s_resources/cluster-issuer.yaml.tpl",
      {
        domain_name              = var.top_dns_zone.name,
        domain_rg                = var.top_dns_zone.resource_group_name,
        environment              = var.environment.name,
        subscription_id          = var.common.connectivity_subscription_id,
        uaid_client_id           = azurerm_user_assigned_identity.cert_manager_identity.client_id
        expiration_contact_email = var.common.admin_contact_email
        acme_server              = "https://acme-v02.api.letsencrypt.org/directory"
        cert_issuer_name         = var.cert_issuer_name
      }
    )
)}
  EOF
]

depends_on = [helm_release.cert_manager]
}
