locals {
  kubernetes_namespace = "external-secrets"
}

resource "helm_release" "eso" {
  name = "external-secrets-operator"

  repository       = "https://charts.external-secrets.io"
  chart            = "external-secrets"
  namespace        = local.kubernetes_namespace
  create_namespace = true
  version          = "0.10.3"

  set {
    name  = "serviceMonitor.enabled"
    value = "true"
  }

  set {
    name  = "priorityClassName"
    value = "high-priority"
  }

  set {
    name  = "resources.requests.memory"
    value = "64Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "10m"
  }

  set {
    name  = "resources.limits.memory"
    value = "64Mi"
  }

  set {
    name  = "webhook.resources.requests.memory"
    value = "32Mi"
  }

  set {
    name  = "webhook.resources.requests.cpu"
    value = "10m"
  }

  set {
    name  = "webhook.resources.limits.memory"
    value = "40Mi"
  }
  set {
    name  = "certController.resources.requests.memory"
    value = "80Mi"
  }

  set {
    name  = "certController.resources.requests.cpu"
    value = "10m"
  }

  set {
    name  = "certController.resources.limits.memory"
    value = "100Mi"
  }
}
