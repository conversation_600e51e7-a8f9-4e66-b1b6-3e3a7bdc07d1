
resource "kubernetes_namespace" "keda_ns" {
  metadata {
    name = "keda"
  }
}

resource "azurerm_user_assigned_identity" "keda_identity" {
  name                = "id-keda-${var.environment.name}"
  resource_group_name = var.resource_group
  location            = var.common.location
  tags                = var.tags
}

resource "helm_release" "keda" {
  name       = "keda"
  repository = "https://kedacore.github.io/charts"
  chart      = "keda"
  namespace  = "keda"
  version    = "2.14.3"

  set {
    name  = "podIdentity.azureWorkload.enabled"
    value = "true"
  }

  set {
    name  = "podIdentity.azureWorkload.clientId"
    value = azurerm_user_assigned_identity.keda_identity.client_id
  }

  set {
    name  = "podIdentity.azureWorkload.tenantId"
    value = var.common.tenant_id
  }

  set {
    name  = "resources.operator.requests.memory"
    value = "125Mi"
  }

  set {
    name  = "resources.operator.requests.cpu"
    value = "100m"
  }

  set {
    name  = "resources.operator.limits.memory"
    value = "1Gi"
  }
  set {
    name  = "resources.operator.limits.cpu"
    value = "1000m"
  }

  set {
    name  = "prometheus.operator.enabled"
    value = "true"
  }

  set {
    name  = "prometheus.operator.serviceMonitor.enabled"
    value = "true"
  }

  set {
    name  = "prometheus.operator.podMonitor.enabled"
    value = "true"
  }

  depends_on = [
    kubernetes_namespace.keda_ns
  ]
}

resource "azurerm_federated_identity_credential" "federated_identity_credential" {
  name                = "fic-keda-${var.environment.name}"
  resource_group_name = var.resource_group
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.keda_identity.id
  subject             = "system:serviceaccount:keda:keda-operator"
}
