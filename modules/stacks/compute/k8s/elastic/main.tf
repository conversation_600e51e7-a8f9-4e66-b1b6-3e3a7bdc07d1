
locals {
  elastic_agent_secrets_name = "elastic-agent-secrets"
}

module "fleet_enrollment_token" {
  source = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"

  name            = "elastic-fleet-enrollment-token"
  placeholder     = true
  key_vault_id    = var.key_vault_id
  admins          = var.infra_admins
  expiration_date = var.common.keys_secrets_expiration_date
}


resource "kubernetes_manifest" "elastic_agent_external_secret" {
  manifest = {
    apiVersion = "external-secrets.io/v1beta1"
    kind       = "ExternalSecret"
    metadata = {
      name      = local.elastic_agent_secrets_name
      namespace = "kube-system"
    }
    spec = {
      refreshInterval = "5m"
      secretStoreRef = {
        kind = "ClusterSecretStore"
        name = var.secret_store_name
      }
      target = {
        name = local.elastic_agent_secrets_name
      }
      data = [
        {
          secretKey = "FLEET_ENROLLMENT_TOKEN"
          remoteRef = {
            key = module.fleet_enrollment_token.secret_name
          }
        }
      ]
    }
  }
  lifecycle {
    create_before_destroy = true
  }
}

resource "kubernetes_daemonset" "elastic_agent" {
  metadata {
    name      = "elastic-agent"
    namespace = "kube-system"
    labels = {
      app = "elastic-agent"
    }
  }

  spec {
    selector {
      match_labels = {
        app = "elastic-agent"
      }
    }

    template {
      metadata {
        labels = {
          app = "elastic-agent"
        }

        annotations = {
          "container.apparmor.security.beta.kubernetes.io/elastic-agent" = "unconfined"
        }
      }

      spec {
        toleration {
          key    = "node-role.kubernetes.io/control-plane"
          effect = "NoSchedule"
        }

        toleration {
          key    = "node-role.kubernetes.io/master"
          effect = "NoSchedule"
        }

        toleration {
          key      = "dedicated"
          operator = "Equal"
          value    = "datajobs"
          effect   = "NoSchedule"
        }

        toleration {
          key      = "dedicated"
          operator = "Equal"
          value    = "webapps"
          effect   = "NoSchedule"
        }

        service_account_name = "elastic-agent"
        host_network         = true
        host_pid             = true
        dns_policy           = "ClusterFirstWithHostNet"

        container {
          image = "docker.elastic.co/beats/elastic-agent:8.18.4"
          name  = "elastic-agent"

          env {
            # Set to 1 for enrollment into Fleet server. If not set, Elastic Agent is run in standalone mode
            name  = "FLEET_ENROLL"
            value = "1"
          }

          env {
            # Set to true to communicate with Fleet with either insecure HTTP or unverified HTTPS
            name  = "FLEET_INSECURE"
            value = "false"
          }

          env {
            # Fleet Server URL to enroll the Elastic Agent into
            # FLEET_URL can be found in Kibana, go to Management > Fleet > Settings
            name  = "FLEET_URL"
            value = "https://b0453653f3098e2b235ecebf71b76250.fleet.westeurope.azure.elastic-cloud.com:443"
          }

          env {
            # Elasticsearch API key used to enroll Elastic Agents in Fleet (https://www.elastic.co/guide/en/fleet/current/fleet-enrollment-tokens.html#fleet-enrollment-tokens)
            name = "FLEET_ENROLLMENT_TOKEN"
            value_from {
              secret_key_ref {
                name = local.elastic_agent_secrets_name
                key  = "FLEET_ENROLLMENT_TOKEN"
              }
            }
          }

          env {
            name = "NODE_NAME"
            value_from {
              field_ref {
                field_path = "spec.nodeName"
              }
            }
          }

          env {
            name = "POD_NAME"
            value_from {
              field_ref {
                field_path = "metadata.name"
              }
            }
          }

          env {
            name  = "ELASTIC_NETINFO"
            value = "false"
          }

          port {
            host_port      = 9201
            container_port = 9201
          }

          security_context {
            run_as_user = 0
            capabilities {
              add = ["SYS_PTRACE"]
            }
          }

          resources {
            limits = {
              memory = "1Gi"
            }
            requests = {
              cpu    = "100m"
              memory = "400Mi"
            }
          }

          volume_mount {
            name       = "proc"
            mount_path = "/hostfs/proc"
            read_only  = true
          }

          volume_mount {
            name       = "cgroup"
            mount_path = "/hostfs/sys/fs/cgroup"
            read_only  = true
          }

          volume_mount {
            name       = "varlibdockercontainers"
            mount_path = "/var/lib/docker/containers"
            read_only  = true
          }

          volume_mount {
            name       = "varlog"
            mount_path = "/var/log"
            read_only  = true
          }

          volume_mount {
            name       = "etc-full"
            mount_path = "/hostfs/etc"
            read_only  = true
          }

          volume_mount {
            name       = "var-lib"
            mount_path = "/hostfs/var/lib"
            read_only  = true
          }

          volume_mount {
            name       = "etc-mid"
            mount_path = "/etc/machine-id"
            read_only  = true
          }

          volume_mount {
            name       = "sys-kernel-debug"
            mount_path = "/sys/kernel/debug"
          }

          volume_mount {
            name       = "elastic-agent-state"
            mount_path = "/usr/share/elastic-agent/state"
          }
        }

        volume {
          name = "proc"
          host_path {
            path = "/proc"
          }
        }

        volume {
          name = "cgroup"
          host_path {
            path = "/sys/fs/cgroup"
          }
        }

        volume {
          name = "varlibdockercontainers"
          host_path {
            path = "/var/lib/docker/containers"
          }
        }

        volume {
          name = "varlog"
          host_path {
            path = "/var/log"
          }
        }

        volume {
          name = "etc-full"
          host_path {
            path = "/etc"
          }
        }

        volume {
          name = "var-lib"
          host_path {
            path = "/var/lib"
          }
        }

        volume {
          name = "etc-mid"
          host_path {
            path = "/etc/machine-id"
            type = "File"
          }
        }

        volume {
          name = "sys-kernel-debug"
          host_path {
            path = "/sys/kernel/debug"
          }
        }

        volume {
          name = "elastic-agent-state"
          host_path {
            path = "/var/lib/elastic-agent-managed/kube-system/state"
            type = "DirectoryOrCreate"
          }
        }
      }
    }
  }
}

resource "kubernetes_cluster_role_binding" "elastic_agent_cluster_role_binding" {
  metadata {
    name = "elastic-agent"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "elastic-agent"
  }

  subject {
    kind      = "ServiceAccount"
    name      = "elastic-agent"
    namespace = "kube-system"
  }
}

resource "kubernetes_role_binding" "elastic_agent_role_binding" {
  metadata {
    name      = "elastic-agent"
    namespace = "kube-system"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = "elastic-agent"
  }

  subject {
    kind      = "ServiceAccount"
    name      = "elastic-agent"
    namespace = "kube-system"
  }
}

resource "kubernetes_role_binding" "elastic_agent_kubeadm_config_role_binding" {
  metadata {
    name      = "elastic-agent-kubeadm-config"
    namespace = "kube-system"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = "elastic-agent-kubeadm-config"
  }

  subject {
    kind      = "ServiceAccount"
    name      = "elastic-agent"
    namespace = "kube-system"
  }
}

resource "kubernetes_cluster_role" "elastic_agent_cluster_role" {
  metadata {
    name = "elastic-agent"
    labels = {
      k8s-app = "elastic-agent"
    }
  }

  rule {
    api_groups = [""]
    resources  = ["nodes", "namespaces", "events", "pods", "services", "configmaps", "serviceaccounts", "persistentvolumes", "persistentvolumeclaims"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups = ["extensions"]
    resources  = ["replicasets"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups = ["apps"]
    resources  = ["statefulsets", "deployments", "replicasets", "daemonsets"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups = [""]
    resources  = ["nodes/stats"]
    verbs      = ["get"]
  }

  rule {
    api_groups = ["batch"]
    resources  = ["jobs", "cronjobs"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups = ["rbac.authorization.k8s.io"]
    resources  = ["clusterrolebindings", "clusterroles", "rolebindings", "roles"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups = ["policy"]
    resources  = ["podsecuritypolicies"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups = ["storage.k8s.io"]
    resources  = ["storageclasses"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    non_resource_urls = ["/metrics"]
    verbs             = ["get"]
  }
}

resource "kubernetes_role" "elastic_agent_role" {
  metadata {
    name      = "elastic-agent"
    namespace = "kube-system"
    labels = {
      k8s-app = "elastic-agent"
    }
  }

  rule {
    api_groups = ["coordination.k8s.io"]
    resources  = ["leases"]
    verbs      = ["get", "create", "update"]
  }
}

resource "kubernetes_role" "elastic_agent_kubeadm_config_role" {
  metadata {
    name      = "elastic-agent-kubeadm-config"
    namespace = "kube-system"
    labels = {
      k8s-app = "elastic-agent"
    }
  }

  rule {
    api_groups     = [""]
    resources      = ["configmaps"]
    resource_names = ["kubeadm-config"]
    verbs          = ["get"]
  }
}

resource "kubernetes_service_account" "elastic_agent_service_account" {
  metadata {
    name      = "elastic-agent"
    namespace = "kube-system"
    labels = {
      k8s-app = "elastic-agent"
    }
  }
}


resource "kubernetes_service" "elastic_agent_service" {
  metadata {
    name      = "elastic-agent"
    namespace = "kube-system"
    labels = {
      k8s-app = "elastic-agent"
    }
  }
  spec {
    port {
      port        = 9201
      protocol    = "TCP"
      target_port = 9201
    }

    selector = { app = "elastic-agent" }

    session_affinity = null
    type             = "ClusterIP"
  }
}
# TODO requires more investigation
# resource "helm_release" "elastic-universal-profiling" {
#   name = "universal-profiling-agent"

#   repository       = "https://helm.elastic.co"
#   chart            = "pf-host-agent"
#   namespace        = "elastic-universal-profiling"
#   version          = "8.12.0"
#   create_namespace = true

#   set_sensitive {
#     name  = "secretToken"
#     value = "GceIp0DVHA9tnFN5"
#   }

#   set {
#     name  = "projectID"
#     value = "1"
#   }

#   set {
#     name  = "collectionAgentHostPort"
#     value = "7e725dbd5e8445bda555225be2ebd343.profiling.westeurope.azure.elastic-cloud.com:443"
#   }

#   set {
#     name  = "version"
#     value = "8.12.0"
#   }
# }
