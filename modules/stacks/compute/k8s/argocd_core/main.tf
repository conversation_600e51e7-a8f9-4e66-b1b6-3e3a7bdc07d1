terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  service_account_name = "service-account-argocd-${var.environment.name}"
  kubernetes_namespace = "argocd"
}

resource "kubernetes_namespace" "argocd_ns" {
  metadata {
    name = local.kubernetes_namespace
  }
}

resource "azuread_application" "argocd_app_registration" {
  display_name            = "argocd-${var.environment.name}"
  owners                  = var.configuration.ad_app_owners
  group_membership_claims = ["All"]

  feature_tags {
    enterprise            = true
    gallery               = false
    custom_single_sign_on = true
  }

  optional_claims {
    access_token {
      name      = "groups"
      essential = true
    }

    id_token {
      name      = "groups"
      essential = true
    }
  }

  web {
    redirect_uris = ["https://argocd.${var.environment.domain}/auth/callback"]
  }

  required_resource_access {
    resource_app_id = "********-0000-0000-c000-************" # Microsoft Graph

    resource_access {
      id   = "e1fe6dd8-ba31-4d61-89e7-88639da4683d" # User.Read
      type = "Scope"
    }
  }
}

resource "azuread_service_principal" "argocd" {
  client_id                     = azuread_application.argocd_app_registration.client_id
  owners                        = var.configuration.ad_app_owners
  preferred_single_sign_on_mode = "oidc"
  login_url                     = "https://argocd.${var.environment.domain}/auth/login"
  app_role_assignment_required  = true
  feature_tags {
    custom_single_sign_on = true
    # set to true so tf doesn't try to set it to null with every plan/apply 
    enterprise = true
  }
}

resource "azuread_app_role_assignment" "allowed_group" {
  app_role_id         = "00000000-0000-0000-0000-************" # default app role
  principal_object_id = var.configuration.allowed_login_group
  resource_object_id  = azuread_service_principal.argocd.object_id

  lifecycle {
    create_before_destroy = true
  }
}

resource "azuread_application_password" "argocd_app_password" {
  application_id = azuread_application.argocd_app_registration.id
  display_name   = "ArgoCD SSO"
  end_date       = var.common.keys_secrets_expiration_date
}

resource "helm_release" "argocd" {
  name = "argocd"

  repository = "https://argoproj.github.io/argo-helm"
  chart      = "argo-cd"
  namespace  = local.kubernetes_namespace
  # https://artifacthub.io/packages/helm/argo/argo-cd
  version = "7.7.23"

  set_sensitive {
    name  = "configs.secret.extra.oidc\\.azure\\.clientSecret"
    value = azuread_application_password.argocd_app_password.value
  }

  values = [
    templatefile("./compute/k8s/argocd_core/argocd-overrides.yaml.tpl", {
      url            = "argocd.${var.environment.domain}"
      tenant_id      = var.common.tenant_id
      oidc_client_id = azuread_application.argocd_app_registration.client_id
      rbac_policy = indent(
        6,
        join("\n", [
          file("./compute/k8s/argocd_core/policy/roles.csv"),
          file("./compute/k8s/argocd_core/policy/policy_${var.configuration.rbac_policy}.csv")
        ])
      )
      argocd_notification_channels = var.configuration.argocd_notification_channels
    })
  ]

  depends_on = [
    kubernetes_namespace.argocd_ns
  ]

  lifecycle {
    # needed so secrets don't get leaked in the metadata
    ignore_changes = [metadata]
  }
}

resource "kubernetes_secret" "argocd_private_repo_creds" {
  metadata {
    name      = "argocd-private-repo-creds"
    namespace = local.kubernetes_namespace
    labels = {
      "argocd.argoproj.io/secret-type" = "repo-creds"
    }
  }

  data = {
    type     = "git"
    url      = "https://gitlab.com/bluebrick/indiebi/infra/mono-workload"
    username = var.registry_configuration.gitlab.username
    password = var.registry_configuration.gitlab.password
  }

  depends_on = [
    kubernetes_namespace.argocd_ns
  ]
}

resource "kubernetes_secret" "argocd_helm_repository" {
  metadata {
    name      = "argocd-helm-repository"
    namespace = local.kubernetes_namespace
    labels = {
      "argocd.argoproj.io/secret-type" = "repository"
    }
  }

  data = {
    type     = "helm"
    url      = var.configuration.helm_repository
    username = var.registry_configuration.gitlab.username
    password = var.registry_configuration.gitlab.password
  }

  depends_on = [
    kubernetes_namespace.argocd_ns
  ]
}

# The reason we need this secret is to store the DigiCert certificate that is used do sign login.microsoftonline.com
# ArgoCD does not keep any default trust store so it does not automatically trust even respected certificate authorities like DigiCert. 
resource "kubernetes_secret" "argocd_tls_ca" {
  metadata {
    name      = "argocd-tls-ca"
    namespace = local.kubernetes_namespace
    labels = {
      "argocd.argoproj.io/secret-type" = "repository"
    }
  }

  data = {
    "ca.crt" = file("./compute/k8s/argocd_core/digicert-root-ca.crt")
  }

  depends_on = [
    kubernetes_namespace.argocd_ns
  ]
}

resource "azurerm_web_application_firewall_policy" "argocd_waf_policy" {
  name                = "waf-policy-argocd-${var.environment.name}"
  resource_group_name = var.resource_group
  location            = var.common.location
  tags                = var.tags

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = true
    file_upload_limit_in_mb     = 50
    max_request_body_size_in_kb = 1024
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"

      dynamic "rule_group_override" {
        for_each = local.owasp_overrides
        content {
          rule_group_name = rule_group_override.value["rule_group_name"]

          dynamic "rule" {
            for_each = rule_group_override.value["rules"]
            content {
              id      = rule.value["id"]
              action  = rule.value["action"]
              enabled = rule.value["enabled"]
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_ingress_v1" "ingress" {
  wait_for_load_balancer = true
  metadata {
    name      = "argocd-ingress"
    namespace = local.kubernetes_namespace
    annotations = {
      "kubernetes.io/ingress.class"                     = "azure/application-gateway"
      "cert-manager.io/cluster-issuer"                  = var.cert_issuer_name
      "appgw.ingress.kubernetes.io/backend-protocol"    = "HTTPS"
      "appgw.ingress.kubernetes.io/waf-policy-for-path" = azurerm_web_application_firewall_policy.argocd_waf_policy.id
      "appgw.ingress.kubernetes.io/rewrite-rule-set"    = "add-response-headers"
      "appgw.ingress.kubernetes.io/use-private-ip"      = true
    }
  }
  spec {
    rule {
      host = "argocd.${var.environment.domain}"
      http {
        path {
          path = "/"
          backend {
            service {
              name = "argocd-server"
              port {
                number = 80
              }
            }
          }
        }
      }
    }

    tls {
      secret_name = "argocd-server-tls"
      hosts       = ["argocd.${var.environment.domain}"]
    }
  }

  depends_on = [
    kubernetes_namespace.argocd_ns
  ]
}

resource "helm_release" "acr_generator" {
  name      = "acr-credentials-generator"
  chart     = "../components/kubernetes-resource"
  namespace = local.kubernetes_namespace

  values = [
    <<-EOF
    configurations:
      - ${indent(4, templatefile("${path.module}/acr-generator.yaml.tpl", { tenant_id = var.common.tenant_id, namespace = local.kubernetes_namespace, service_account = local.service_account_name }))}
      - ${indent(4, templatefile("${path.module}/acr-secret.yaml.tpl", { namespace = local.kubernetes_namespace }))}
    EOF
  ]
}

resource "helm_release" "argocd_image_updater" {
  name = "argocd-image-updater"

  repository = "https://argoproj.github.io/argo-helm"
  chart      = "argocd-image-updater"
  namespace  = "argocd"
  version    = "0.11.0"

  values = [
    file("${path.module}/argocd-image-updater-overrides.yaml")
  ]

  depends_on = [
    kubernetes_namespace.argocd_ns
  ]
}

module "dns_entry" {
  source                       = "../../../../components/dns_entry"
  name                         = "argocd${var.environment.dns_extension != null ? var.environment.dns_extension : ""}"
  dns_zone_resource_group_name = var.top_dns_zone.resource_group_name
  dns_zone_name                = var.top_dns_zone.name
  ip                           = var.ip_address
  is_private                   = false

  providers = {
    azurerm.connectivity = azurerm.connectivity
  }
}

resource "azurerm_user_assigned_identity" "argocd_identity" {
  name                = "id-argocd-${var.environment.name}"
  resource_group_name = var.resource_group
  location            = var.common.location
  tags                = var.tags
}


resource "kubernetes_service_account" "service_account" {
  metadata {
    name      = local.service_account_name
    namespace = local.kubernetes_namespace
    annotations = {
      "azure.workload.identity/client-id" = azurerm_user_assigned_identity.argocd_identity.client_id,
      "azure.workload.identity/tenant-id" = var.common.tenant_id
    }
    labels = {
      "azure.workload.identity/use" = "true"
    }
  }
}

resource "azurerm_federated_identity_credential" "federated_identity_credential" {
  name                = "fic-argocd-${var.environment.name}"
  resource_group_name = var.resource_group
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.argocd_identity.id
  subject             = "system:serviceaccount:${local.kubernetes_namespace}:${local.service_account_name}"
}

resource "azurerm_role_assignment" "argocd_acr_pull" {
  role_definition_name = "AcrPull"
  scope                = var.common.acr_resource_id
  principal_id         = azurerm_user_assigned_identity.argocd_identity.principal_id

  lifecycle {
    create_before_destroy = true
  }
}
