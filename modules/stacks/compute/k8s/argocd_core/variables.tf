variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "registry_configuration" {
  type = any
}

variable "cert_issuer_name" {
  type = string
}

variable "configuration" {
  type = any
}

variable "resource_group" {
  type = string
}

variable "top_dns_zone" {
  type = map(any)
}

variable "ip_address" {
  type = string
}

variable "cluster_oidc_issuer_url" {
  type = string
}

variable "tags" {
  type = map(string)
}
