p, role:no-access, applications, *, */*, deny
p, role:no-access, certificates, *, *, deny
p, role:no-access, clusters, *, *, deny
p, role:no-access, repositories, *, *, deny
p, role:no-access, projects, *, *, deny
p, role:no-access, accounts, *, *, deny
p, role:no-access, gpgkeys, *, *, deny
p, role:no-access, exec, create, */*, deny 
p, role:no-access, logs, *, */*, deny
p, role:no-access, extensions, *, *, deny

p, role:org-admin, applications, *, */*, allow
p, role:org-admin, certificates, *, *, allow
p, role:org-admin, clusters, *, *, allow
p, role:org-admin, repositories, *, *, allow
p, role:org-admin, projects, *, *, allow
p, role:org-admin, accounts, *, *, allow
p, role:org-admin, gpgkeys, *, *, allow
p, role:org-admin, exec, create, */*, allow 
p, role:org-admin, logs, *, *, allow
p, role:org-admin, extensions, *, *, allow

p, role:org-reader, applications, get, */*, allow
p, role:org-reader, applications, sync, */*, allow
p, role:org-reader, applications, action/apps/Deployment/restart, */*, allow
p, role:org-reader, projects, get, *, allow
p, role:org-reader, logs, get, */*, allow

p, role:cpt-admin, applications, *, cpt/*, allow
p, role:cpt-admin, logs, *, cpt/*, allow
p, role:cpt-admin, exec, create, cpt/*, allow

p, role:cpt-reader, applications, get, cpt/*, allow
p, role:cpt-reader, applications, sync, cpt/*, allow
p, role:cpt-reader, applications, action/apps/Deployment/restart, cpt/*, allow
p, role:cpt-reader, logs, get, cpt/*, allow

p, role:dpt-admin, applications, *, dpt/*, allow
p, role:dpt-admin, logs, *, dpt/*, allow
p, role:dpt-admin, exec, create, dpt/*, allow

p, role:dpt-reader, applications, get, dpt/*, allow
p, role:dpt-reader, applications, sync, dpt/*, allow
p, role:dpt-reader, applications, action/apps/Deployment/restart, dpt/*, allow
p, role:dpt-reader, logs, get, dpt/*, allow

p, role:saas-admin, applications, *, saas/*, allow
p, role:saas-admin, logs, *, saas/*, allow
p, role:saas-admin, exec, create, saas/*, allow

p, role:saas-reader, applications, get, saas/*, allow
p, role:saas-reader, applications, sync, saas/*, allow
p, role:saas-reader, applications, action/apps/Deployment/restart, saas/*, allow
p, role:saas-reader, logs, get, saas/*, allow