global:
  priorityClassName: high-priority

configs:
  cm:
    url: https://${url}
    oidc.config: |
      name: Azure
      issuer: https://login.microsoftonline.com/${tenant_id}/v2.0
      clientID: ${oidc_client_id}
      clientSecret: $oidc.azure.clientSecret
      requestedIDTokenClaims:
        groups:
          essential: true
      requestedScopes:
        - openid
        - profile
        - email
    admin.enabled: false
  rbac:
    policy.default: role:no-access
    policy.csv: |
      ${rbac_policy}
    scopes: '[groups, email]'
server:
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
  volumeMounts:
    - name: certificate
      mountPath: /etc/ssl/certs/ca.crt
      subPath: ca.crt 
  volumes:
    - name: certificate
      secret:
        secretName: argocd-tls-ca
        defaultMode: 420
  resources:
    requests:
      memory: 80Mi
      cpu: 50m
    limits:
      memory: 150Mi
controller:
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
  resources:
    requests:
      memory: 500Mi
      cpu: 100m
    limits:
      memory: 1.5Gi
applicationSet:
  enabled: false
repoServer:
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
  resources:
    requests:
      memory: 80Mi
      cpu: 50m
    limits:
      memory: 200Mi
redis:
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
  serviceAccount:
    create: true
  resources:
    requests:
      memory: 10Mi
      cpu: 10m
    limits:
      memory: 20Mi
dex:
  enabled: false
notifications:
  enabled: true
  name: notifications-controller
  resources:
    requests:
      memory: 80Mi
      cpu: 50m
    limits:
      memory: 128Mi
  argocdUrl: ${url}
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
  notifiers: 
    service.teams: |
      recipientUrls:
        %{~ for channel_name, channel_url in argocd_notification_channels ~}
        '${channel_name}': ${channel_url}
        %{~ endfor ~}
  # Default templates from ArgoCD https://github.com/argoproj/argo-helm/blob/main/charts/argo-cd/values.yaml
  templates:
    template.app-deployed: |
      email:
        subject: New version of an application {{.app.metadata.name}} is up and running.
      message: |
        Application {{.app.metadata.name}} is now running new version of deployments manifests.
        Application details: https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
      teams:
        facts: |
          [{
            "name": "Sync Status",
            "value": "{{.app.status.sync.status}}"
          }
          {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "name": "{{$c.type}}",
              "value": "{{$c.message}}",
            }
          {{end}}
          ]
        potentialAction: |-
          [{
            "@type":"OpenUri",
            "name":"Operation Application",
            "targets":[{
              "os":"default",
              "uri":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}"
            }]
          },
          {
            "@type":"OpenUri",
            "name":"Open Repository",
            "targets":[{
              "os":"default",
              "uri":"{{.app.spec.source.repoURL | call .repo.RepoURLToHTTPS}}"
            }]
          }]
        title: New version of an application {{.app.metadata.name}} is up and running.
    template.app-deleted: |
      email:
        subject: Application {{.app.metadata.name}} has been deleted.
      message: Application {{.app.metadata.name}} has been deleted.
      teams:
        title: Application {{.app.metadata.name}} has been deleted.
    template.app-health-degraded: |
      email:
        subject: Application {{.app.metadata.name}} has degraded.
      message: |
        Application {{.app.metadata.name}} has degraded.
        Application details: https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
      teams:
        facts: |
          [{
            "name": "Health Status",
            "value": "{{.app.status.health.status}}"
          }
          {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "name": "{{$c.type}}",
              "value": "{{$c.message}}"
            }
          {{end}}
          ]
        potentialAction: |
          [{
            "@type":"OpenUri",
            "name":"Open Application",
            "targets":[{
              "os":"default",
              "uri":"https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}"
            }]
          }]
        themeColor: '#FF0000'
        title: Application {{.app.metadata.name}} has degraded.
    template.app-sync-running: |
      email:
        subject: Start syncing application {{.app.metadata.name}}.
      message: |
        The sync operation of application {{.app.metadata.name}} has started at {{.app.status.operationState.startedAt}}.
        Sync operation details are available at: https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
      teams:
        facts: |
          [{
            "name": "Sync Status",
            "value": "{{.app.status.sync.status}}"
          },
          {
            "name": "Started at",
            "value": "{{.app.status.operationState.startedAt}}"
          }
          {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "name": "{{$c.type}}",
              "value": "{{$c.message}}"
            }
          {{end}}
          ]
        potentialAction: |-
          [{
            "@type":"OpenUri",
            "name":"Open Operation",
            "targets":[{
              "os":"default",
              "uri":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true"
            }]
          },
          {
            "@type":"OpenUri",
            "name":"Open Repository",
            "targets":[{
              "os":"default",
              "uri":"{{.app.spec.source.repoURL | call .repo.RepoURLToHTTPS}}"
            }]
          }]
        title: Start syncing application {{.app.metadata.name}}.
    template.app-sync-succeeded: |
      email:
        subject: Application {{.app.metadata.name}} has been successfully synced.
      message: |
        Application {{.app.metadata.name}} has been successfully synced at {{.app.status.operationState.finishedAt}}.
        Sync operation details are available at: https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
      teams:
        facts: |
          [{
            "name": "Sync Status",
            "value": "{{.app.status.sync.status}}"
          },
          {
            "name": "Synced at",
            "value": "{{.app.status.operationState.finishedAt}}"
          }
          {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "name": "{{$c.type}}",
              "value": "{{$c.message}}"
            }
          {{end}}
          ]
        potentialAction: |-
          [{
            "@type":"OpenUri",
            "name":"Operation Details",
            "targets":[{
              "os":"default",
              "uri":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true"
            }]
          },
          {
            "@type":"OpenUri",
            "name":"Open Repository",
            "targets":[{
              "os":"default",
              "uri":"{{.app.spec.source.repoURL | call .repo.RepoURLToHTTPS}}"
            }]
          }]
        themeColor: '#000080'
        title: Application {{.app.metadata.name}} has been successfully synced
    template.app-sync-failed: |
      email:
        subject: Failed to sync application {{.app.metadata.name}}.
      message: |
        The sync operation of application {{.app.metadata.name}} has failed at {{.app.status.operationState.finishedAt}} with the following error: {{.app.status.operationState.message}}
        Sync operation details are available at: https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
      teams:
        facts: |
          [{
            "name": "Sync Status",
            "value": "{{.app.status.sync.status}}"
          },
          {
            "name": "Failed at",
            "value": "{{.app.status.operationState.finishedAt}}"
          }
          {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "name": "{{$c.type}}",
              "value": "{{$c.message}}"
            }
          {{end}}
          ]
        potentialAction: |-
          [{
            "@type":"OpenUri",
            "name":"Open Operation",
            "targets":[{
              "os":"default",
              "uri":"https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true"
            }]
          }]
        themeColor: '#FF0000'
        title: Failed to sync application {{.app.metadata.name}}.
    template.app-sync-status-unknown: |
      email:
        subject: Application {{.app.metadata.name}} sync status is 'Unknown'
      message: |
        Application {{.app.metadata.name}} sync is 'Unknown'.
        Application details: https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
        {{range $c := .app.status.conditions}}
            * {{$c.message}}
        {{end}}
      teams:
        facts: |
          [{
            "name": "Sync Status",
            "value": "{{.app.status.sync.status}}"
          }
          {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "name": "{{$c.type}}",
              "value": "{{$c.message}}"
            }
          {{end}}
          ]
        potentialAction: |-
          [{
            "@type":"OpenUri",
            "name":"Open Application",
            "targets":[{
              "os":"default",
              "uri":"https://{{.context.argocdUrl}}/applications/{{.app.metadata.name}}"
            }]
          }]
        title: Application {{.app.metadata.name}} sync status is 'Unknown'
    trigger.on-deployed: |
      - description: Application is synced and healthy. Triggered once per commit.
        oncePer: app.status.operationState?.syncResult?.revision
        send:
        - app-deployed
        when: app.status.operationState != nil and app.status.operationState.phase in ['Succeeded'] and app.status.health.status == 'Healthy' and app.status.sync.status != 'OutOfSync'
    trigger.on-deleted: |
      - description: Application is deleted.
        oncePer: app.metadata.name
        send:
        - app-deleted
        when: app.metadata.deletionTimestamp != nil
    trigger.on-health-degraded: |
      - description: Application has degraded
        send:
        - app-health-degraded
        when: app.status.health.status == 'Degraded'
    trigger.on-sync-running: |
      - description: Application sync is running.
        send:
        - app-sync-running
        when: app.status.operationState != nil and app.status.operationState.phase in ['Running']
    trigger.on-sync-succeeded: |
        - description: Application syncing has succeeded
          send:
          - app-sync-succeeded
          when: app.status.operationState != nil and app.status.operationState.phase in ['Succeeded']
    trigger.on-sync-failed: |
      - description: Application syncing has failed
        send:
        - app-sync-failed
        when: app.status.operationState != nil and app.status.operationState.phase in ['Error', 'Failed']
    trigger.on-sync-status-unknown: |
      - description: Application status is 'Unknown'
        send:
        - app-sync-status-unknown
        when: app.status.sync.status == 'Unknown'
  