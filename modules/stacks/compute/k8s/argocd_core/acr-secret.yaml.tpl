apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: acr-registry
  namespace: ${namespace}
spec:
  dataFrom:
    - sourceRef:
        generatorRef:
          apiVersion: generators.external-secrets.io/v1alpha1
          kind: ACRAccessToken
          name: indiebi-main-acr
  refreshInterval: 2h
  target:
    name: acr-registry
    template:
      type: kubernetes.io/dockerconfigjson
      data:
        .dockerconfigjson: |
          {"auths": {"crindiebimain.azurecr.io": { "auth": "{{ printf "%s:%s" .username .password | b64enc }}"}}}