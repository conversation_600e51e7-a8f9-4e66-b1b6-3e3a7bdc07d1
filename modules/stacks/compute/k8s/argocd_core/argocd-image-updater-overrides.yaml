extraArgs:
  - --interval
  - 30s

config:
  registries:
    - name: Azure Container Registry
      api_url: https://crindiebimain.azurecr.io
      prefix: crindiebimain.azurecr.io
      credentials: pullsecret:argocd/acr-registry
      credsexpire: 1h

podSecurityContext:
  runAsUser: 1000
  runAsGroup: 1000

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

resources:
  requests:
    memory: 64Mi
    cpu: 50m
  limits:
    memory: 128Mi
