apiVersion: v1
kind: Secret
metadata:
  name: sql-server-datasources-config
  namespace: prometheus
  labels:
     grafana_datasource: "1"
type: Opaque
stringData:
  sql-server-datasources.yaml: |-
    apiVersion: 1
    datasources:
      %{~ for database_name in databases ~}
      - name: ${database_name}
        type: mssql
        url: ${sql_server_host}
        uid: sql-server-${database_name}
        user: grafana
        jsonData:
          database: ${database_name}
          maxOpenConns: 100
          maxIdleConns: 100
          maxIdleConnsAuto: true
          connMaxLifetime: 14400
          connectionTimeout: 0
          encrypt: 'false'
        secureJsonData:
          password: ${grafana_password}
      %{~ endfor ~}