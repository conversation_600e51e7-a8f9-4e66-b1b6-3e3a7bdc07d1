# for list of configuration values run: helm show values prometheus-community/kube-prometheus-stack
# Grafana specific: https://github.com/grafana/helm-charts/blob/main/charts/grafana/values.yaml
grafana:
  image:
    tag: ${grafana_image_tag}

  # bug in Grafana requires Recreate if PVC is enabled
  # https://github.com/grafana/helm-charts/issues/1184
  deploymentStrategy:
    type: Recreate
  # provision 10Gi disk for Grafana's data
  persistence:
    enabled: true
    extraPvcLabels:
      stack: compute
      purpose: grafana
  resources:
    requests:
      memory: 400Mi

  # TODO: needs to be set, otherwise complains about client_secret in grafana.ini
  # see: https://github.com/grafana/grafana/issues/80349
  assertNoLeakedSecrets: false

  grafana.ini:
    # required for correct OAuth2 redirects (otherwise it appends port to the domain)
    server:
      root_url: https://grafana.${domain_name}
    # use Managed Identity, required to access Azure Monitor and other Azure data sources
    azure:
      workload_identity_enabled: true

    # authentication configuration
    auth:
      disable_login_form: true
    # Microsoft Entra ID (formerly Azure Active Directory) configuration
    auth.azuread:
      name: Azure AD
      enabled: true
      # required so users that are not added to the app can login
      allow_sign_up: true
      auto_login: false
      client_id: ${grafana_app_client_id}
      scopes: openid email profile
      auth_url: https://login.microsoftonline.com/${tenant_id}/oauth2/v2.0/authorize
      token_url: https://login.microsoftonline.com/${tenant_id}/oauth2/v2.0/token
      # allow only logins from the groups that are assigned to app roles
      allowed_groups: ${allowed_groups}
      # disable default role assignment
      role_attribute_strict: true
      # allow only logins from the organization (tenant)
      allowed_organizations: ${tenant_id}
      # disallow assigning admin role from inside Grafana
      allow_assign_grafana_admin: false
      use_pkce: true

  # enable Workload Identity
  serviceAccount:
    annotations:
      azure.workload.identity/client-id: "${grafana_identity_client_id}"

  podLabels:
    azure.workload.identity/use: "true"

  # enable ingress
  ingress:
    enabled: true
    hosts:
      - grafana.${domain_name}
    tls:
      - secretName: prometheus-grafana-tls
        hosts:
          - grafana.${domain_name}

    annotations:
      kubernetes.io/ingress.class: azure/application-gateway
      cert-manager.io/cluster-issuer: letsencrypt-issuer
      appgw.ingress.kubernetes.io/health-probe-path: /api/health
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/waf-policy-for-path: ${waf_policy_id}
      appgw.ingress.kubernetes.io/rewrite-rule-set: add-response-headers

  # configure data sources
  datasources:
    datasources.yaml:
      apiVersion: 1
      datasources:
        # https://grafana.com/docs/grafana/latest/datasources/azure-monitor/
        - name: Azure Monitor
          type: grafana-azure-monitor-datasource
          access: proxy
          uid: azure-monitor
          jsonData:
            azureAuthType: workloadidentity
            subscriptionId: ${subscription_id}
          version: 1
  
  sidecar:
    resources:
      limits:
        cpu: 100m
        memory: 100Mi
      requests:
        cpu: 50m
        memory: 50Mi

    dashboards:
      folder: /tmp/dashboards
      folderAnnotation: grafana_folder
      provider:
        foldersFromFilesStructure: true
    
    alerts:
      enabled: true

prometheusOperator:
  priorityClassName: high-priority
  resources:
    requests:
      memory: 64Mi

prometheus:
  prometheusSpec:
    retention: ${retention}
    retentionSize: ${retention_size}
    serviceMonitorSelector:
      matchLabels: null
    podMonitorSelector:
      matchLabels: null
    priorityClassName: high-priority
    # configure Persistent Volume Claim for metrics
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: default
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: ${storage_size}
    resources:
      requests:
        memory: 3Gi
    # remoteWrite:
    #  - url: "http://elastic-agent.kube-system:9201/write"

kube-state-metrics:
  priorityClassName: high-priority
  resources:
    requests: 
      memory: 50Mi
  
prometheus-node-exporter:
  resources:
    requests: 
      memory: 50Mi

crds:
  enabled: true
  upgradeJob:
    enabled: true
    forceConflicts: true
