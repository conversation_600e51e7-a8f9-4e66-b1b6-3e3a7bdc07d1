apiVersion: v1
kind: Secret
metadata:
  namespace: prometheus
  name: grafana-contact-points
  labels:
    grafana_alert: "1"
type: Opaque
stringData:
  contactpoints.yaml: |-
    apiVersion: 1
    contactPoints:
      %{~ for team_name, config in alerting ~}
      - orgId: 1
        name: ${team_name}-teams
        receivers:
          - uid: ${team_name}-teams
            type: teams
            settings:
              url: ${config.contact_point}
      %{~ endfor ~}
  
  policies.yaml: |-
    apiVersion: 1
    policies:
      - orgId: 1
        receiver: grafana-default-email
        group_by:
          - alertname
        routes:
          %{~ for team_name, config in alerting ~}
          - receiver: ${team_name}-teams
            group_by:
              - alertname
              - team
            matchers:
              - team = ${team_name}
          %{~ for policy in config.custom_policies ~}
          - receiver: ${team_name}-teams
            group_by:
              - alertname
              %{~ for matcher in policy.matchers ~}
              - ${split("=", matcher)[0]}
              %{~ endfor ~}
            matchers:
              %{~ for matcher in policy.matchers ~}
              - ${matcher}
              %{~ endfor ~}
            %{ if length(try(policy.mute_time_intervals, [])) > 0 }
            mute_time_intervals:
            %{~ for interval in policy.mute_time_intervals ~}
              - ${interval}
            %{~ endfor ~}
            %{ endif }
          %{~ endfor ~}
          %{~ endfor ~}

  mutetimes.yaml: |-
    apiVersion: 1
    muteTimes:
      %{~ for team_name, config in alerting ~}
      %{~ for name, time_intervals in config.mute_times ~}
      - orgId: 1
        name: ${name}
        time_intervals: 
        %{~ for interval in time_intervals ~}
        - times:
          %{~ for time in interval.times ~}
            - start_time: '${time.start_time}'
              end_time: '${time.end_time}'
          %{~ endfor ~}
          location: '${interval.location}'
        %{~ endfor ~}
      %{~ endfor ~}
      %{~ endfor ~}