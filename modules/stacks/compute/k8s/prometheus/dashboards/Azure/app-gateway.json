{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 5621, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "Overview", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 0.25}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "A 4xx"}, "properties": [{"id": "displayName", "value": "4XX ratio"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "A 5xx"}, "properties": [{"id": "displayName", "value": "5XX ratio"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 21, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "ResponseStatus", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "hide": true, "queryType": "Azure Monitor", "refId": "Total", "subscription": "$subscription"}, {"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "HttpStatusGroup", "filters": ["4xx", "5xx"], "operator": "eq"}], "metricName": "ResponseStatus", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto", "top": ""}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "hide": true, "queryType": "Azure Monitor", "refId": "Failed", "subscription": "$subscription"}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "$Failed/$Total", "hide": false, "refId": "A", "type": "math"}], "title": "Failed to total response ratio", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 20, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.2", "targets": [{"azureLogAnalytics": {"dashboardTime": false, "intersectTime": false, "query": "AzureDiagnostics\r\n| where ResourceType == \"APPLICATIONGATEWAYS\" and OperationName == \"ApplicationGatewayAccess\" \r\n| where TimeGenerated >= $__timeFrom and TimeGenerated <= $__timeTo\r\n| where httpStatus_d >= 500\r\n| project Service=replace_regex(backendPoolName_s, @'pool-(.*)-80-.*', @'\\1'), Method=httpMethod_s, URI=requestUri_s, Status=httpStatus_d, SerialNumber=connectionSerialNumber_d\r\n| distinct *\r\n| summarize Count = count() by Service, Method, URI, Status", "resources": ["/subscriptions/$subscription/resourceGroups/$resource_group/providers/$namespace/$resource"], "resultFormat": "table"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Log Analytics", "refId": "A"}], "title": "5XX responses", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 9}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureLogAnalytics": {"dashboardTime": false, "query": "AzureDiagnostics\n| where ResourceType == \"APPLICATIONGATEWAYS\" and OperationName == \"ApplicationGatewayAccess\" \n| where TimeGenerated >= $__timeFrom and TimeGenerated <= $__timeTo\n| where isnotempty(timeTaken_d)\n| summarize \n    [\"response time (p50)\"] = percentile(timeTaken_d, 50), \n    [\"response time (p90)\"] = percentile(timeTaken_d, 90)\n    by bin(TimeGenerated, 1h)\n| order by TimeGenerated asc", "resources": ["/subscriptions/$subscription/resourceGroups/$resource_group/providers/$namespace/$resource"], "resultFormat": "logs"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Log Analytics", "refId": "A"}], "title": "Response time (50th and 90th percentiles)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 11, "panels": [], "title": "Application Gateway", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "The total number of concurrent connections active from clients to the Application Gateway", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 7, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "CurrentConnections", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Current connections", "subscription": "$subscription"}], "title": "Current connections", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "4xx and 5xx HTTP response status returned by Application Gateway", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 200}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 17, "options": {"legend": {"calcs": ["last"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "alias": "4xx", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "HttpStatusGroup", "filters": ["4xx"], "operator": "eq"}], "metricName": "ResponseStatus", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "4xx", "subscription": "$subscription"}, {"azureMonitor": {"aggregation": "Total", "alias": "5xx", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "HttpStatusGroup", "filters": ["5xx"], "operator": "eq"}], "metricName": "ResponseStatus", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "hide": false, "queryType": "Azure Monitor", "refId": "5xx", "subscription": "$subscription"}], "title": "Error responses", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Number of requests that Application Gateway has served with 5xx server error codes. This includes the 5xx codes that are generated from the Application Gateway and the 5xx codes that are generated from the backend. The request count can be further filtered to show count per each/specific backend pool-http setting combination", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "FailedRequests", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Failed requests", "subscription": "$subscription"}], "title": "Failed requests", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Count of successful requests that Application Gateway has served", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 26}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "TotalRequests", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Total requests", "subscription": "$subscription"}], "title": "Total requests", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Average round-trip time between clients and Application Gateway", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 34}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Maximum", "alias": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "ClientRtt", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Client RTT max", "subscription": "$subscription"}, {"azureMonitor": {"aggregation": "Average", "alias": "Average", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "ClientRtt", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "hide": false, "queryType": "Azure Monitor", "refId": "Client RTT avg", "subscription": "$subscription"}], "title": "Client RTT", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "HTTP response status returned by Application Gateway", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 34}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "HttpStatusGroup", "filters": ["1xx"], "operator": "ne"}], "metricName": "ResponseStatus", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Response status", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Number of bytes per second the Application Gateway has served (accounts for only the Content size served by the Application Gateway, it doesn't include data transfers such as TLS header negotiations, TCP/IP packet headers, or retransmissions)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 42}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Average", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "Throughput", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Throughput", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "The average number of new TCP connections per second established from clients to the Application Gateway and from the Application Gateway to the backend members", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 42}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Average", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "NewConnectionsPerSecond", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "New connections per second", "subscription": "$subscription"}], "title": "New connections per second", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Count of bytes sent by the Application Gateway to the clients (accounts for only the Response Content size served by the Application Gateway, it doesn't include data transfers such as TCP/IP packet headers or retransmissions)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 50}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "BytesSent", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Bytes sent", "subscription": "$subscription"}], "title": "Bytes sent", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Count of bytes received by the Application Gateway from the clients (accounts for only the Request content size observed by the Application Gateway, it doesn't include data transfers such as TLS header negotiations, TCP/IP packet headers, or retransmissions)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 50}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.2", "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "BytesReceived", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Bytes received", "subscription": "$subscription"}], "title": "Bytes received", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 58}, "id": 12, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Count of successful requests that WAF engine has served", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "PolicyName", "filters": [], "operator": "eq"}], "metricName": "AzwafTotalRequests", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Total requests", "subscription": "$subscription"}], "title": "Total requests", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Count of custom rule matches", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "PolicyName", "filters": [], "operator": "eq"}], "metricName": "AzwafCustomRule", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Custom rule matches", "subscription": "$subscription"}], "title": "Custom rule matches", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Count of total managed rule matches", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "RuleGroupID", "filters": [], "operator": "eq"}], "metricName": "AzwafSecRule", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Managed rule matches", "subscription": "$subscription"}], "title": "Managed rule matches", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Count of total bot protection rule matches that have been blocked or logged from malicious IP addresses (the IP addresses are sourced from the Microsoft Threat Intelligence feed)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 11}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Total", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "PolicyName", "filters": [], "operator": "eq"}], "metricName": "AzwafBotProtection", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Monitor", "refId": "Bot protection matches", "subscription": "$subscription"}], "title": "Bot protection matches", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "description": "Logged connections blocked by Web Application Firewall rules", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Timestamp"}, "properties": [{"id": "custom.width", "value": 181}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "IP"}, "properties": [{"id": "custom.width", "value": 205}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Hostname"}, "properties": [{"id": "custom.width", "value": 392}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 19}, "id": 19, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"azureLogAnalytics": {"dashboardTime": false, "intersectTime": false, "query": "AzureDiagnostics\n| where  ResourceProvider == \"MICROSOFT.NETWORK\" and Category == \"ApplicationGatewayFirewallLog\" and action_s == \"Blocked\"\n| where TimeGenerated >= $__timeFrom and TimeGenerated <= $__timeTo\n| order by TimeGenerated desc\n| project-keep timeStamp_t, clientIp_s, hostname_s, Message\n| project-rename Timestamp=timeStamp_t, IP=clientIp_s, Hostname=hostname_s\n| project-reorder Timestamp, Hostname, IP, Message\n", "resources": ["/subscriptions/$subscription/resourceGroups/$resource_group/providers/$namespace/$resource"], "resultFormat": "table"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "queryType": "Azure Log Analytics", "refId": "WAF logs"}], "title": "WAF blocked events", "type": "table"}], "title": "Web Application Firewall", "type": "row"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "Azure Monitor", "value": "azure-monitor"}, "hide": 2, "includeAll": false, "name": "datasource", "options": [], "query": "grafana-azure-monitor-datasource", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {"text": "app-dev", "value": "974be8a6-83ee-4087-b47d-7cd8424ba8e5"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "definition": "", "hide": 2, "includeAll": false, "name": "subscription", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Subscriptions", "refId": "A"}, "refresh": 1, "regex": "/^(?!5d37e76e-270f-4dc0-939f-d667a5fb1f57).*/", "type": "query"}, {"current": {"text": "rg-network-dev", "value": "rg-network-dev"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "definition": "", "hide": 2, "includeAll": false, "name": "resource_group", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Resource Groups", "refId": "A", "subscription": "$subscription"}, "refresh": 1, "regex": "/rg-network-.*/", "type": "query"}, {"current": {"text": "microsoft.network/applicationgateways", "value": "microsoft.network/applicationgateways"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "definition": "", "hide": 2, "includeAll": false, "name": "namespace", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Namespaces", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "/microsoft.network/applicationgateways/", "type": "query"}, {"current": {"text": "agw-dev", "value": "agw-dev"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "${datasource}"}, "definition": "", "hide": 2, "includeAll": false, "name": "resource", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "namespace": "$namespace", "queryType": "Azure Resource Names", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Application Gateway", "uid": "e21359d0-7864-421f-a67d-904c0947ddd9", "version": 3}