{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 20, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Usage statistics", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 20, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "// Initial method to count sessions but it looks like it's invalid, hence replaced by the next one\n//\n// traces\n// | where $__timeFilter(timestamp) != \"\" and session_Id !startswith \"qarb\"\n// | summarize make_list(message, 50) by session_Id, bin(timestamp, 1d)\n// | summarize scheduled = countif(list_message has \"Running in 'runAllActive' (scheduled job) mode\"), interactive = countif(list_message !has \"Running in 'runAllActive' (scheduled job) mode\") by timestamp\n// | sort by timestamp asc;\n\ntraces\n| where session_Id !startswith \"qarb\"\n| sort by timestamp asc\n| summarize make_list(message, 500) by session_Id, bin(timestamp, 1d)\n| summarize\n    scheduled = countif(list_message has \"Running in 'runAllActive' (scheduled job) mode\"), \n    interactive = countif(list_message has \"Downloading report for\" and list_message !has \"Running in 'runAllActive' (scheduled job) mode\") \n    by timestamp\n| sort by timestamp asc;", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "interactive vs shceduled usage", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 0, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 4, "x": 20, "y": 1}, "id": 6, "options": {"barRadius": 0, "barWidth": 0.5, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces\n| where $__timeFilter(timestamp)\n| summarize value = count() by hour = hourofday(timestamp)\n| sort by hour asc \n| project tostring(hour), value", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Users activity by hour", "type": "barchart"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-90d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Internal graphs", "uid": "8F4kzqtnz", "version": 2, "weekStart": ""}