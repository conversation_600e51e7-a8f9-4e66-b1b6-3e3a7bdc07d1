{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 14, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 0, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces\n| where (message contains \"INCORRECT_CREDENTIALS\" or message contains \"SESSION_EXPIRED\") and operation_Name contains \"steam\"\nand timestamp > datetime($issueStartDay) and timestamp < datetime($issueEndDay)\n| extend username = tostring(customDimensions.username)\n| project user_AuthenticatedId, username\n| where not(username contains \"@indiebi.com\")\n| distinct user_AuthenticatedId\n| count ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Minimal affected user count", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 5, "y": 0}, "id": 7, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let usersWithIncorrectCredentials = traces\n| where (message contains \"INCORRECT_CREDENTIALS\" or message contains \"SESSION_EXPIRED\") and operation_Name contains \"steam\"\nand timestamp > datetime($issueStartDay) and timestamp < datetime($issueEndDay)\n| extend username = tostring(customDimensions.username)\n| project user_AuthenticatedId, username\n| where not(username contains \"@indiebi.com\")\n| distinct user_AuthenticatedId;\ntraces\n| where user_AuthenticatedId in (usersWithIncorrectCredentials)\nand message contains \"steam\"\nand message contains \"Report send report info result\"\nand timestamp > datetime($issueStartDay)\n| distinct user_AuthenticatedId\n| count ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Users who uploaded steam files", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 10, "y": 0}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let usersWithIncorrectCredentials = traces\n| where (message contains \"INCORRECT_CREDENTIALS\" or message contains \"SESSION_EXPIRED\") and operation_Name contains \"steam\"\nand timestamp > datetime($issueStartDay) and timestamp < datetime($issueEndDay)\n| extend username = tostring(customDimensions.username)\n| project user_AuthenticatedId, username\n| where not(username contains \"@indiebi.com\")\n| distinct user_AuthenticatedId;\nlet usersWhoUploaded = traces\n| where user_AuthenticatedId in (usersWithIncorrectCredentials)\nand message contains \"steam\"\nand message contains \"Report send report info result\"\nand timestamp > datetime($issueStartDay)\n| extend username = tostring(customDimensions.username)\n| project user_AuthenticatedId, username\n| where not(username contains \"@indiebi.com\")\n| distinct user_AuthenticatedId;\nusersWithIncorrectCredentials\n| where not(user_AuthenticatedId in (usersWhoUploaded))\n| count", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Users who DID NOT upload steam files awhile affected", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "user_AuthenticatedId"}, "properties": [{"id": "custom.width", "value": 172}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "username"}, "properties": [{"id": "custom.width", "value": 406}]}]}, "gridPos": {"h": 8, "w": 8, "x": 15, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces\n| where (message contains \"INCORRECT_CREDENTIALS\" or message contains \"SESSION_EXPIRED\") and operation_Name contains \"steam\"\nand timestamp > datetime($issueStartDay) and timestamp < datetime($issueEndDay)\n| extend username = tostring(customDimensions.username)\n| project user_AuthenticatedId, username\n| where not(username contains \"@indiebi.com\")\n| summarize arg_max(user_AuthenticatedId,*) by username\n| order by user_AuthenticatedId asc\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"dimensionFilters": [], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "34d925e9-7558-44da-b9df-6af837f7bc14"}], "title": "All potentially affected users", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 23, "x": 0, "y": 8}, "id": 6, "maxPerRow": 3, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.7", "repeat": "Sources", "repeatDirection": "v", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let usersWithIncorrectCredentials = traces\n| where (message contains \"INCORRECT_CREDENTIALS\" or message contains \"SESSION_EXPIRED\") and operation_Name contains \"steam\"\nand timestamp > datetime($issueStartDay) and timestamp < datetime($issueEndDay)\n| extend username = tostring(customDimensions.username)\n| project user_AuthenticatedId, username\n| where not(username contains \"@indiebi.com\")\n| distinct user_AuthenticatedId;\ntraces\n| where user_AuthenticatedId in (usersWithIncorrectCredentials)\nand message contains $Sources\nand message contains \"Report send report info result\"\nand timestamp > datetime('2023-03-15')\n| extend day = format_datetime(timestamp, 'yyyy-MM-dd')\n| project day, user_AuthenticatedId\n| evaluate pivot(user_AuthenticatedId)\n| order by day asc\n// | order by  timestamp asc\n// | extend timestamp = strcat(timestamp,' - ',format_datetime(datetime_add('day',7,make_datetime(timestamp)),'yyyy-MM-dd'))", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "$Sources file uploads by affected customers", "type": "barchart"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["steam_sales", "steam_wishlists", "steam_impressions"], "value": ["steam_sales", "steam_wishlists", "steam_impressions"]}, "hide": 0, "includeAll": true, "multi": true, "name": "Sources", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": true, "text": "steam_sales", "value": "steam_sales"}, {"selected": true, "text": "steam_wishlists", "value": "steam_wishlists"}, {"selected": true, "text": "steam_impressions", "value": "steam_impressions"}], "query": "steam_sales, steam_wishlists, steam_impressions", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "2023-03-15", "value": "2023-03-15"}, "hide": 0, "includeAll": false, "multi": false, "name": "issueStartDay", "options": [{"selected": true, "text": "2023-03-15", "value": "2023-03-15"}], "query": "2023-03-15", "queryValue": "2023-01-01", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "2023-03-22", "value": "2023-03-22"}, "hide": 0, "includeAll": false, "multi": false, "name": "issueEndDay", "options": [{"selected": true, "text": "2023-03-22", "value": "2023-03-22"}], "query": "2023-03-22", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "2023-03-15T23:00:00.000Z", "to": "2023-03-20T22:59:59.000Z"}, "timepicker": {}, "timezone": "", "title": "Steam outage", "uid": "Ou_rala4k", "version": 2, "weekStart": ""}