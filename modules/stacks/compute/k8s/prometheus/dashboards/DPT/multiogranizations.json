{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 9, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 10, "x": 0, "y": 0}, "id": 9, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "amount"}]}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\n| where $__timeFilter(timestamp) and name == \"Found multiple organizations\" and customDimensions.amount > 0 and not(user_Id contains \"qarb\") and operation_Name matches regex \"CLI scrape: ${sources:regex}$\" \n| sort by timestamp asc \n| extend source=trim(@\"[\\s]+\", replace_string(operation_Name, \"CLI scrape: \", \"\"))\n| summarize amount=max(toint(customDimensions.amount)) by user_Id, source\n| sort by amount", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Number of multiorgs per client", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 4, "x": 10, "y": 0}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"query": "customEvents\n| where name == \"Found multiple organizations\" and customDimensions.amount > 1\n| distinct user_Id\n| summarize count()", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Total users having more than one organization", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "customDimensions_organization"}, "properties": [{"id": "displayName", "value": "Organization"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_Id"}, "properties": [{"id": "displayName", "value": "User ID"}]}]}, "gridPos": {"h": 9, "w": 10, "x": 14, "y": 0}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents \n| where $__timeFilter(timestamp) and name == \"Ignoring organization\" and not(user_Id contains \"qarb\") and operation_Name matches regex \"CLI scrape: ${sources:regex}$\" \n| distinct user_Id, tostring(customDimensions[\"organization\"])", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Ignored organizations", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 67, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\n| where $__timeFilter(timestamp) and name == \"Found multiple organizations\" and customDimensions.amount > 0 and not(user_Id contains \"qarb\") and operation_Name matches regex \"CLI scrape: ${sources:regex}\" \n| summarize max(toint(customDimensions.amount)) by user_Id, operation_Name, bin(timestamp, 3d)\n| summarize HavingMultiorgAmount = count() by tostring(max_customDimensions_amount), timestamp\n| sort by timestamp asc ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Users with specified multiorgs amount stacked $sources", "transformations": [{"id": "renameByRegex", "options": {"regex": "(HavingMultiorgAmount\\s)(.*)", "renamePattern": "Clients with $2 multiorgs"}}], "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\n| where $__timeFilter(timestamp) and name == \"Found multiple organizations\" and customDimensions.amount > 0 and not(user_Id contains \"qarb\") and operation_Name matches regex \"CLI scrape: ${sources:regex}\" \n| summarize max(toint(customDimensions.amount)) by user_Id, operation_Name, bin(timestamp, 3d)\n| summarize HavingMultiorgAmount = count() by tostring(max_customDimensions_amount), timestamp\n| sort by timestamp asc ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Users with specified multiorgs amount $sources", "transformations": [{"id": "renameByRegex", "options": {"regex": "(HavingMultiorgAmount\\s)(.*)", "renamePattern": "Clients with $2 multiorgs"}}], "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "hide": 0, "includeAll": true, "label": "Sources", "multi": true, "name": "sources", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "steam_sales", "value": "steam_sales"}, {"selected": false, "text": "steam_wishlists", "value": "steam_wishlists"}, {"selected": false, "text": "steam_impressions", "value": "steam_impressions"}, {"selected": false, "text": "steam_wishlist_country", "value": "steam_wishlist_country"}, {"selected": false, "text": "steam_discounts", "value": "steam_discounts"}, {"selected": false, "text": "microsoft_sales", "value": "microsoft_sales"}, {"selected": false, "text": "nintendo_sales", "value": "nintendo_sales"}, {"selected": false, "text": "humble_sales", "value": "humble_sales"}, {"selected": false, "text": "playstation_sales", "value": "playstation_sales"}, {"selected": false, "text": "meta_rift_sales", "value": "meta_rift_sales"}, {"selected": false, "text": "meta_quest_sales", "value": "meta_quest_sales"}, {"selected": false, "text": "app_store_sales", "value": "app_store_sales"}, {"selected": false, "text": "google_sales", "value": "google_sales"}, {"selected": false, "text": "gog_sales", "value": "gog_sales"}, {"selected": false, "text": "epic_sales", "value": "epic_sales"}], "query": "steam_sales, steam_wishlists, steam_impressions,steam_wishlist_country, steam_discounts, microsoft_sales,nintendo_sales, humble_sales, playstation_sales, meta_rift_sales, meta_quest_sales, app_store_sales, google_sales, gog_sales, epic_sales,", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Multiorganizations", "uid": "hBJ3bUtnk", "version": 3, "weekStart": ""}