{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 6, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "App Versions", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 70, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 0, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 15, "x": 0, "y": 1}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"azureLogAnalytics": {"query": "traces \n| where $__timeFilter(timestamp) != \"\" and session_Id !contains \"qarb\"\n| where  message == \"Telemetry started.\"\n| distinct bin(timestamp, 1d), cloud_RoleInstance, application_Version\n| summarize Version = count() by timestamp,  iff(application_Version == \"\", \"0.14.x\", application_Version)\n| order by  timestamp asc ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Versions usage", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "user_Id"}, "properties": [{"id": "custom.width", "value": 259}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "newestVersion"}, "properties": [{"id": "custom.width", "value": 85}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "deviceName"}, "properties": [{"id": "custom.width", "value": 165}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 150}]}]}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 1}, "id": 26, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "newestVersion"}]}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"query": "traces\n| where $__timeFilter(timestamp) and user_Id != \"\" and user_Id !contains \"qarb\"\n| summarize arg_max(timestamp, *) by user_Id, cloud_RoleInstance\n| project user_Id, newestVersion = iff(application_Version == \"\", \"0.14.x\", application_Version), timestamp, deviceName = cloud_RoleInstance;", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Lastest user version", "type": "table"}, {"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 20, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Scrape events", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 50}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "StartedScrapes"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SuccessfulScrapes"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "FailedScrapes"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "FinishedWithoutUpload"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 10}, "id": 14, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.3", "targets": [{"azureLogAnalytics": {"query": "let start= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> started\" and not(user_Id contains \"qarb\")\n| summarize StartedScrapes = count() by (format_datetime(timestamp, 'yyyy-MM-dd')), name;\nlet success= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> succeeded!\" and not(user_Id contains \"qarb\")\n| summarize SuccessfulScrapes = count() by (format_datetime(timestamp, 'yyyy-MM-dd')), name;\nlet failed= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON>er failed unexpectedly!\" and not(user_Id contains \"qarb\")\n| summarize FailedScrapes = count() by (format_datetime(timestamp, 'yyyy-MM-dd')), name;\nlet finishWithoutUpload = customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON>er finished without upload\" and not(user_Id contains \"qarb\")\n| summarize FinishedWithoutUpload = count() by (format_datetime(timestamp, 'yyyy-MM-dd')), name;\nstart\n| join kind=fullouter success on timestamp\n| join kind=fullouter failed on timestamp\n| join kind=fullouter finishWithoutUpload on timestamp\n| order by timestamp asc\n\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Started/Finished/Failed scrapes", "transformations": [{"id": "configFromData", "options": {}}], "type": "barchart"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "customDimensions_error"}, "properties": [{"id": "custom.width", "value": 984}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 223}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "customDimensions_platform"}, "properties": [{"id": "custom.width", "value": 261}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_Id"}, "properties": [{"id": "custom.width", "value": 288}]}]}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 20}, "id": 12, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "timestamp"}]}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"query": "customEvents\n| where name == \"<PERSON><PERSON><PERSON> failed unexpectedly!\" and $__timeFilter(timestamp) and not(user_Id contains \"qarb\")\n| project source=customDimensions.source, user_Id, timestamp, customDimensions.error\n| order by timestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Failed scrapes", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "customDimensions_error"}, "properties": [{"id": "custom.width", "value": 570}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 157}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "customDimensions_platform"}, "properties": [{"id": "custom.width", "value": 215}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_Id"}, "properties": [{"id": "custom.width", "value": 274}]}]}, "gridPos": {"h": 15, "w": 14, "x": 0, "y": 34}, "id": 27, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "timestamp"}]}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"query": "customEvents\n| where name == \"<PERSON><PERSON><PERSON> finished without upload\" and $__timeFilter(timestamp) and not(user_Id contains \"qarb\")\n| project source=customDimensions.source, user_Id, timestamp, customDimensions.error\n| order by timestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Finished without upload", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "operation_Name"}, "properties": [{"id": "custom.width", "value": 234}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 166}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_Id"}, "properties": [{"id": "custom.width", "value": 294}]}]}, "gridPos": {"h": 15, "w": 10, "x": 14, "y": 34}, "id": 16, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "timestamp"}]}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"query": "let start= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> started\" and not(user_Id contains \"qarb\");\nlet success= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> succeeded!\" and not(user_Id contains \"qarb\");\nlet failed= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> failed unexpectedly!\" and not(user_Id contains \"qarb\");\nstart\n| join kind=leftanti  success on operation_Id\n| join kind=leftanti  failed on operation_Id\n| project timestamp, user_Id, operation_Name\n| order by timestamp asc\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"dimensionFilters": [], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "34d925e9-7558-44da-b9df-6af837f7bc14"}], "title": "Interrupted (non finished/failed) scrapes", "type": "table"}, {"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 49}, "id": 18, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Workbench", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "Successful scrapes per client in given timeframe", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "super-light-orange", "value": 20}, {"color": "light-orange", "value": 50}, {"color": "dark-orange", "value": 60}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 50}, "id": 29, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {"titleSize": 10, "valueSize": 30}, "textMode": "value_and_name"}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"query": "customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> succeeded!\" and not(user_Id contains \"qarb\") \n| summarize succes = count() by bin(timestamp, 1d), user_Id\n| order by timestamp asc  ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Successful scrapes per client", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"log": 10, "type": "log"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"pattern": "e (.*)", "result": {"index": 0, "text": "foo $1"}}, "type": "regex"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 14, "x": 0, "y": 70}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "targets": [{"azureLogAnalytics": {"query": "let topEvents = customEvents\r\n| where $__timeFilter(timestamp)\r\n| top-hitters 5 of name;\r\n\r\ncustomEvents\r\n| where $__timeFilter(timestamp)\r\n| join kind=leftouter (topEvents) on name\r\n| summarize [\"Event:\"]=count() by name1, bin(timestamp, $__interval)\r\n| order by timestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Custom Events", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Event: 1": "Other events"}}}], "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Event"}, "properties": [{"id": "custom.width", "value": 491}]}]}, "gridPos": {"h": 12, "w": 10, "x": 14, "y": 70}, "id": 2, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"query": "customEvents\r\n| top-hitters 10 of name\r\n| project-rename Event=name, [\"Count (Approx)\"]=approximate_count_name", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"], "resultFormat": "table"}, "azureMonitor": {"dimensionFilters": [], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "34d925e9-7558-44da-b9df-6af837f7bc14"}], "title": "Top 10 Custom Events", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "Top five exception messages are shown as distinct series", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"log": 10, "type": "log"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"pattern": "e (.*)", "result": {"index": 0, "text": "foo $1"}}, "type": "regex"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 14, "x": 0, "y": 82}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "targets": [{"azureLogAnalytics": {"query": "let topExceptions = exceptions\r\n| where $__timeFilter(timestamp)\r\n| top-hitters 5 of innermostMessage;\r\n\r\nexceptions\r\n| where $__timeFilter(timestamp)\r\n| join kind=leftouter (topExceptions) on innermostMessage\r\n| summarize [\"Event:\"]=count() by innermostMessage1, bin(timestamp, $__interval)\r\n| order by timestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Scraper Crashes", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Event: 1": "Other errors"}}}], "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Event"}, "properties": [{"id": "custom.width", "value": 499}]}]}, "gridPos": {"h": 12, "w": 10, "x": 14, "y": 82}, "id": 3, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"query": "exceptions\r\n| top-hitters 10 of innermostMessage\r\n| project-rename Event=innermostMessage, [\"Count (Approx)\"]=approximate_count_innermostMessage", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"], "resultFormat": "table"}, "azureMonitor": {"dimensionFilters": [], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "34d925e9-7558-44da-b9df-6af837f7bc14"}], "title": "Top 10 Crash Reasons", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Scraper Events Overview", "uid": "LB0MzwD7z", "version": 2, "weekStart": ""}