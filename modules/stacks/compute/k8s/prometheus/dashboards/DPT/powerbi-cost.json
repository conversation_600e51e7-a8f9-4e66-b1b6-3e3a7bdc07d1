{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 43, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 50}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT SUM(cost_month)/sum(client_count) FROM (\n  SELECT *, cost_hour * 735 AS cost_month FROM (\n    SELECT c.id, c.name, c.tier, c.is_default, c.state, COUNT(DISTINCT p.studio_id) AS client_count, \n      CASE\n          WHEN c.tier = 'A1' and c.state = 'ACTIVE' THEN 1\n          WHEN c.tier = 'A2' and c.state = 'ACTIVE' THEN 2\n          WHEN c.tier = 'A3' and c.state = 'ACTIVE' THEN 4\n          WHEN c.tier = 'A4' and c.state = 'ACTIVE' THEN 8\n          WHEN c.tier = 'A5' and c.state = 'ACTIVE' THEN 16\n          WHEN c.tier = 'A6' and c.state = 'ACTIVE' THEN 32\n          ELSE 0\n      END AS cost_hour    \n    FROM [WebApp].[shard] AS s\n    LEFT JOIN [WebApp].[profile] AS p ON p.permission_set_uuid = s.permission_set_uuid\n    LEFT JOIN [WebApp].[version] AS v ON v.shard_version = s.version\n    LEFT JOIN [WebApp].[capacity] AS c ON c.id = s.capacity_id\n    WHERE v.id = p.active_version_id\n    GROUP BY c.id, c.name, c.tier, c.is_default, c.state\n  ) t\n) x", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Estimated monthly PBI cost per customer", "type": "stat"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT SUM(cost_month) FROM  (\n  SELECT *, cost_hour * 735 AS cost_month FROM (\n    SELECT c.id, c.name, c.tier, c.is_default, c.state, COUNT(*) AS shard_count, \n      CASE\n        WHEN c.tier = 'A1' and c.state = 'ACTIVE' THEN 1\n        WHEN c.tier = 'A2' and c.state = 'ACTIVE' THEN 2\n        WHEN c.tier = 'A3' and c.state = 'ACTIVE' THEN 4\n        WHEN c.tier = 'A4' and c.state = 'ACTIVE' THEN 8\n        WHEN c.tier = 'A5' and c.state = 'ACTIVE' THEN 16\n        WHEN c.tier = 'A6' and c.state = 'ACTIVE' THEN 32\n        ELSE 0\n      END AS cost_hour    \n    FROM [WebApp].[shard] AS s\n    LEFT JOIN [WebApp].[profile] as p ON p.permission_set_uuid = s.permission_set_uuid\n    LEFT JOIN [WebApp].[version] AS v ON v.shard_version = s.version\n    LEFT JOIN [WebApp].[capacity] AS c ON c.id = s.capacity_id\n    WHERE v.id = p.active_version_id\n    GROUP BY c.id, c.name, c.tier, c.is_default, c.state\n  ) t\n) x", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Estimated cost per month", "type": "stat"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 0}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT SUM(cost_hour) FROM  (\n  SELECT *, cost_hour * 735 AS cost_month FROM (\n    SELECT c.id, c.name, c.tier, c.is_default, c.state, COUNT(*) AS shard_count, \n      CASE\n        WHEN c.tier = 'A1' and c.state = 'ACTIVE' THEN 1\n        WHEN c.tier = 'A2' and c.state = 'ACTIVE' THEN 2\n        WHEN c.tier = 'A3' and c.state = 'ACTIVE' THEN 4\n        WHEN c.tier = 'A4' and c.state = 'ACTIVE' THEN 8\n        WHEN c.tier = 'A5' and c.state = 'ACTIVE' THEN 16\n        WHEN c.tier = 'A6' and c.state = 'ACTIVE' THEN 32\n        ELSE 0\n      END AS cost_hour    \n    FROM [WebApp].[shard] AS s\n    LEFT JOIN WebApp.profile AS p ON p.permission_set_uuid = s.permission_set_uuid\n    LEFT JOIN WebApp.version AS v ON v.shard_version = s.version\n    LEFT JOIN WebApp.capacity AS c ON c.id = s.capacity_id\n    WHERE v.id = p.active_version_id\n    GROUP BY c.id, c.name, c.tier, c.is_default, c.state\n  ) t\n) x", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Estimated cost per hour", "type": "stat"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 6}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT *, cost_hour * 735 as cost_month FROM (\n  SELECT c.id, c.name, c.tier, c.is_default, c.state, COUNT(*) AS shard_count, \n  CASE\n        WHEN c.tier = 'A1' and c.state = 'ACTIVE' THEN 1\n        WHEN c.tier = 'A2' and c.state = 'ACTIVE' THEN 2\n        WHEN c.tier = 'A3' and c.state = 'ACTIVE' THEN 4\n        WHEN c.tier = 'A4' and c.state = 'ACTIVE' THEN 8\n        WHEN c.tier = 'A5' and c.state = 'ACTIVE' THEN 16\n        WHEN c.tier = 'A6' and c.state = 'ACTIVE' THEN 32\n        ELSE 0\n    END AS cost_hour    \n  FROM [WebApp].[shard] AS s\n  LEFT JOIN [WebApp].[profile] AS p ON p.permission_set_uuid = s.permission_set_uuid\n  LEFT JOIN [WebApp].[version] AS v ON v.shard_version = s.version\n  LEFT JOIN [WebApp].[capacity] AS c ON c.id = s.capacity_id\n  WHERE v.id = p.active_version_id\n  GROUP BY c.id, c.name, c.tier, c.is_default, c.state\n) t\nORDER BY state, tier DESC", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "PBI instances summary", "type": "table"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 12}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT p.studio_id, s.workspace_id, c.id, c.name, c.state, c.tier\nFROM [WebApp].[shard] AS s\nLEFT JOIN [WebApp].[profile] AS p ON p.permission_set_uuid = s.permission_set_uuid\nLEFT JOIN [WebApp].[version] AS v ON v.shard_version = s.version\nLEFT JOIN [WebApp].[capacity] AS c ON c.id = s.capacity_id\nWHERE v.id = p.active_version_id", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Shard inspection", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "db-dataset-manager", "value": "sql-server-db-dataset-manager"}, "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "mssql", "refresh": 1, "regex": "/.*dataset-manager/", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Power BI Cost", "uid": "cb63e8c5-38e2-48d7-9e37-8774fa4a5dce", "version": 1, "weekStart": ""}