{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 17, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Scraper config summary", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ ACTIVE (in last 7d)"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ ERROR"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ INACTIVE"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ CONFIGURED"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ SCHEDULED"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ Session expired"}, "properties": [{"id": "color", "value": {"fixedColor": "light-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ CHURNED"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ Unexpected error"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ Dependencies sync error"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ Incorrect 2fa"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ Temporary portal issue"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count_ MISSING_PERMISSIONS"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let startDate = ago(30d);\nrange day from startDate to now() step 1d\n| extend dummy=1\n| join kind=inner\n    (customEvents\n      | where timestamp > startDate\n      and  name == 'Source state update'\n      and customDimensions.username !endswith \"indiebi.com\"\n      and customDimensions.source == $Sources\n      and user_AuthenticatedId != ''\n      | extend scraper_status = customDimensions.status, errorType = customDimensions.errorType\n      | project-rename last_day=timestamp\n      | extend dummy=1\n    ) on $left.dummy==$right.dummy\n| where last_day < day and scraper_status != ''\n| summarize arg_max(last_day, *) by user_AuthenticatedId, day\n| extend status = iif(day - last_day > 7d and day - last_day < 30d and scraper_status == 'RUNNING_SCRAPE', \"INACTIVE\", scraper_status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'SESSION_EXPIRED', \"Session expired\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'INCORRECT_2FA', \"Incorrect 2fa\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'UNEXPECTED_ERROR', \"Unexpected error\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'INCORRECT_CREDENTIALS', \"Incorrect credentials\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'DEPENDENCIES_SYNC_ERROR', \"Dependencies sync error\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'SECRET_EXPIRED', \"Secret expired (MS)\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'TEMPORARY_PORTAL_ISSUE', \"Temporary portal issue\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'MISSING_PERMISSIONS', \"MISSING_PERMISSIONS\", status)\n| extend status = iif(status in (\"RUNNING_SCRAPE\",\"CONFIGURED\"), \"ACTIVE (in last 7d)\", status)\n| where status != \"INACTIVE\"\n| project user_AuthenticatedId, day, status\n| summarize count(), make_list(user_AuthenticatedId) by tostring(status), day\n| project count_, day, status\n| order by day asc ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}, {"azureLogAnalytics": {"intersectTime": false, "query": "let startDate = ago(30d);\nrange day from startDate to now() step 1d\n| extend dummy=1\n| join kind=inner\n    (customEvents\n      | where timestamp > startDate - 120d\n      and  name == 'Source state update'\n      and customDimensions.username !endswith \"indiebi.com\"\n      and customDimensions.source == $Sources\n      and user_AuthenticatedId != ''\n      | extend scraper_status = customDimensions.status, errorType = customDimensions.errorType\n      | project-rename last_day=timestamp\n      | extend dummy=1\n    ) on $left.dummy==$right.dummy\n| where last_day < day and scraper_status != ''\n| summarize arg_max(last_day, *) by user_AuthenticatedId, day\n// | extend status = iif(day - last_day > 7d and day - last_day < 30d and scraper_status == 'RUNNING_SCRAPE', \"INACTIVE\", scraper_status)\n| extend status = iif(day - last_day > 7d and day - last_day < 30d, \"INACTIVE\", scraper_status)\n| extend status = iif(day - last_day > 30d, \"CHURNED\", status)// TODO all states\n| where status in (\"INACTIVE\",\"CHURNED\")\n| project user_AuthenticatedId, day, status\n| summarize count(), make_list(user_AuthenticatedId) by tostring(status), day\n| project count_, day, status\n| order by day asc ", "resources": ["/subscriptions/34d925e9-7558-44da-b9df-6af837f7bc14/resourceGroups/rg-electron-prod/providers/microsoft.insights/components/appi-indiebidesktop-prod-001"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": true, "queryType": "Azure Log Analytics", "refId": "B", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}, {"azureLogAnalytics": {"intersectTime": false, "query": "let startDate = ago(1d);\nrange day from startDate to now() step 1d\n| extend dummy=1\n| join kind=inner\n    (customEvents\n      | where timestamp > startDate\n      and  name == 'Source state update'\n      and customDimensions.username !endswith \"indiebi.com\"\n      and customDimensions.source == $Sources\n      and user_AuthenticatedId != ''\n      | extend scraper_status = customDimensions.status, errorType = customDimensions.errorType\n      | project-rename last_day=timestamp\n      | extend dummy=1\n    ) on $left.dummy==$right.dummy\n| where last_day < day and scraper_status != ''\n| summarize arg_max(last_day, *) by user_AuthenticatedId, day\n| extend status = iif(day - last_day > 7d and day - last_day < 30d and scraper_status == 'RUNNING_SCRAPE', \"INACTIVE\", scraper_status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'SESSION_EXPIRED', \"Session expired\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'INCORRECT_2FA', \"Incorrect 2fa\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'UNEXPECTED_ERROR', \"Unexpected error\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'INCORRECT_CREDENTIALS', \"Incorrect credentials\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'DEPENDENCIES_SYNC_ERROR', \"Dependencies sync error\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'SECRET_EXPIRED', \"Secret expired (MS)\", status)\n| extend status = iif(scraper_status == 'ERROR' and errorType == 'TEMPORARY_PORTAL_ISSUE', \"Temporary portal issue\", status)\n| extend status = iif(status in (\"RUNNING_SCRAPE\",\"CONFIGURED\"), \"ACTIVE\", status)\n| where status != \"INACTIVE\" and status != \"ACTIVE\"\n| project user_AuthenticatedId, status", "resources": ["/subscriptions/34d925e9-7558-44da-b9df-6af837f7bc14/resourceGroups/rg-electron-prod/providers/microsoft.insights/components/appi-indiebidesktop-prod-001"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": true, "queryType": "Azure Log Analytics", "refId": "C", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "$Sources states over time", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 21, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Full scraper usages", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 34}, "id": 23, "maxPerRow": 3, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces\n| where message contains $Sources\nand message contains \"Report send report info result\"\nand $__timeFilter(timestamp)\n| where  \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\n| extend day = format_datetime(timestamp, 'yyyy-MM-dd')\n| project day, user_AuthenticatedId\n| evaluate pivot(user_AuthenticatedId)\n| order by day asc\n// | order by  timestamp asc\n// | extend timestamp = strcat(timestamp,' - ',format_datetime(datetime_add('day',7,make_datetime(timestamp)),'yyyy-MM-dd'))", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "$Sources - Full scraper usage ", "type": "barchart"}, {"collapsed": true, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 82}, "id": 19, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "", "axisPlacement": "hidden", "axisSoftMax": 1, "axisSoftMin": -1, "barAlignment": -1, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "max": 1, "min": -1, "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "yellow", "value": -0.05}, {"color": "green", "value": 0.05}]}}, "overrides": [{"matcher": {"id": "byType", "options": "time"}, "properties": [{"id": "custom.axisPlacement", "value": "auto"}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 75}, "id": 15, "maxPerRow": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents      \n| where  name == 'Source state update' and timestamp > now(-30d)\n//and user_AuthenticatedId in (705, 2,9,10,11,12,13,14,15)\n| where  \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\nand customDimensions.source == $Sources\n| extend status = customDimensions.status, errorType = customDimensions.errorType\n| extend pulse = \niif(customDimensions.status == 'RUNNING_SCRAPE' or customDimensions.status == 'CONFIGURED' or customDimensions.status == 'SCHEDULED', 1, \niif(customDimensions.status == 'ERROR' and customDimensions.errorType == 'SESSION_EXPIRED' , 0, -1))\n| project user_AuthenticatedId, timestamp, pulse\n| order by timestamp asc ", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Scraper Pulse $Sources for user $Sources", "type": "timeseries"}], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Pulses", "type": "row"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["steam_sales", "steam_wishlists", "steam_impressions", "steam_wishlist_country", "steam_discounts", "microsoft_sales", "nintendo_sales", "humble_sales", "playstation_sales", "playstation_wishlist_actions", "meta_rift_sales", "meta_quest_sales", "app_store_sales", "google_sales", "gog_sales", "epic_sales"], "value": ["steam_sales", "steam_wishlists", "steam_impressions", "steam_wishlist_country", "steam_discounts", "microsoft_sales", "nintendo_sales", "humble_sales", "playstation_sales", "playstation_wishlist_actions", "meta_rift_sales", "meta_quest_sales", "app_store_sales", "google_sales", "gog_sales", "epic_sales"]}, "hide": 0, "includeAll": false, "multi": true, "name": "Sources", "options": [{"selected": true, "text": "steam_sales", "value": "steam_sales"}, {"selected": true, "text": "steam_wishlists", "value": "steam_wishlists"}, {"selected": true, "text": "steam_impressions", "value": "steam_impressions"}, {"selected": true, "text": "steam_wishlist_country", "value": "steam_wishlist_country"}, {"selected": true, "text": "steam_discounts", "value": "steam_discounts"}, {"selected": true, "text": "microsoft_sales", "value": "microsoft_sales"}, {"selected": true, "text": "nintendo_sales", "value": "nintendo_sales"}, {"selected": true, "text": "humble_sales", "value": "humble_sales"}, {"selected": true, "text": "playstation_sales", "value": "playstation_sales"}, {"selected": true, "text": "playstation_wishlist_actions", "value": "playstation_wishlist_actions"}, {"selected": true, "text": "meta_rift_sales", "value": "meta_rift_sales"}, {"selected": true, "text": "meta_quest_sales", "value": "meta_quest_sales"}, {"selected": true, "text": "app_store_sales", "value": "app_store_sales"}, {"selected": true, "text": "google_sales", "value": "google_sales"}, {"selected": true, "text": "gog_sales", "value": "gog_sales"}, {"selected": true, "text": "epic_sales", "value": "epic_sales"}], "query": "steam_sales, steam_wishlists, steam_impressions,steam_wishlist_country, steam_discounts, microsoft_sales,nintendo_sales, humble_sales, playstation_sales, playstation_wishlist_actions, meta_rift_sales, meta_quest_sales, app_store_sales, google_sales, gog_sales, epic_sales,", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": true, "text": "", "value": ""}, "hide": 0, "name": "UserFilter", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-30d", "to": "now+1d"}, "timepicker": {}, "timezone": "", "title": "States overview", "uid": "5Bye4Gv4k", "version": 1, "weekStart": ""}