{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 7, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 5, "x": 0, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"query": "traces\r\n  | where $__timeFilter(timestamp)\r\n  | where \"$SessionID\" == \"\" or session_Id==\"$SessionID\"\r\n  | where \"$TestSessions\"==\"Include\" or session_Id !contains \"@indiebi.com\"\r\n  | distinct session_Id", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Scraper sessions", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-green", "mode": "thresholds"}, "custom": {"fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "#EAB839", "value": 1}, {"color": "light-red", "value": 2}, {"color": "dark-red", "value": 3}, {"color": "dark-purple", "value": 4}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 19, "x": 5, "y": 0}, "id": 6, "interval": "60", "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": false}, "mergeValues": false, "rowHeight": 0.9, "showValue": "never", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"query": "traces\r\n| where $__timeFilter(timestamp)\r\n| where \"$TestSessions\"==\"Include\" or session_Id !contains \"@indiebi.com\"\r\n| where \"$SessionID\" == \"\" or session_Id==\"$SessionID\"\r\n| summarize [\"Session:\"]=max(severityLevel) by session_Id, bin(timestamp, $__interval)\r\n| order by timestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"], "resultFormat": "time_series"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Scraping sessions", "type": "state-timeline"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 182}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 351}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_City"}, "properties": [{"id": "custom.width", "value": 121}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_CountryOrRegion"}, "properties": [{"id": "custom.width", "value": 201}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "session_Id"}, "properties": [{"id": "custom.width", "value": 263}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 13}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"query": "customEvents \r\n    | where $__timeFilter(timestamp)\r\n    | where \"$TestSessions\"==\"Include\" or user_Id !endswith \"@indiebi.com\"\r\n    | where \"$SessionID\" == \"\" or session_Id==\"$SessionID\"\r\n    | order by timestamp desc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Recorded Events", "transformations": [{"id": "organize", "options": {"excludeByName": {"_ResourceId": true, "appId": true, "appName": true, "application_Version": true, "client_Browser": true, "client_IP": true, "client_Model": true, "client_OS": true, "client_StateOrProvince": true, "client_Type": true, "cloud_RoleInstance": true, "cloud_RoleName": true, "customMeasurements": true, "iKey": true, "itemCount": true, "itemId": true, "itemType": true, "operation_Id": true, "operation_Name": true, "operation_ParentId": true, "operation_SyntheticSource": true, "sdkVersion": true, "user_AccountId": true, "user_AuthenticatedId": true, "user_Id": true}, "indexByName": {"_ResourceId": 30, "appId": 24, "appName": 25, "application_Version": 13, "client_Browser": 21, "client_City": 18, "client_CountryOrRegion": 20, "client_IP": 17, "client_Model": 15, "client_OS": 16, "client_StateOrProvince": 19, "client_Type": 14, "cloud_RoleInstance": 23, "cloud_RoleName": 22, "customDimensions": 4, "customMeasurements": 5, "iKey": 26, "itemCount": 29, "itemId": 28, "itemType": 3, "name": 2, "operation_Id": 7, "operation_Name": 6, "operation_ParentId": 8, "operation_SyntheticSource": 9, "sdkVersion": 27, "session_Id": 1, "timestamp": 0, "user_AccountId": 12, "user_AuthenticatedId": 11, "user_Id": 10}, "renameByName": {}}}], "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 21}, "id": 8, "maxDataPoints": 500, "options": {"dedupStrategy": "none", "enableLogDetails": false, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"azureLogAnalytics": {"query": "traces\r\n  | where $__timeFilter(timestamp)\r\n  | where \"$TestSessions\"==\"Include\" or session_Id !contains \"@indiebi.com\"\r\n  | where \"$SessionID\" == \"\" or session_Id==\"$SessionID\"\r\n  | order by timestamp desc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Trace", "type": "logs"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "", "value": ""}, "description": "If set, filters down to the selected session", "hide": 0, "label": "", "name": "SessionID", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": true, "text": "Exclude", "value": "Exclude"}, "description": "Flip to include/exclude sessions originating from @indiebi.com accounts.", "hide": 0, "includeAll": false, "multi": false, "name": "TestSessions", "options": [{"selected": false, "text": "Include", "value": "Include"}, {"selected": true, "text": "Exclude", "value": "Exclude"}], "query": "Include, Exclude", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Activity Explorer", "uid": "EK_Yuhdnk", "version": 2, "weekStart": ""}