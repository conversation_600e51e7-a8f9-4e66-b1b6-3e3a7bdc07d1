{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 11, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "${Sources}", "type": "row"}, {"alert": {"alertRuleTags": {}, "conditions": [{"evaluator": {"params": [60], "type": "lt"}, "operator": {"type": "and"}, "query": {"params": ["A", "6h", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "1m", "frequency": "1h", "handler": 1, "message": "Scraper # health test for platform: ${Platforms}", "name": "Super error test is super failing!", "noDataState": "no_data", "notifications": [{"uid": "NYf1YBc7z"}]}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr", "seriesBy": "max"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 50}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "ScrapesVolume"}, "properties": [{"id": "custom.axisPlacement", "value": "right"}, {"id": "unit", "value": "none"}, {"id": "custom.drawStyle", "value": "bars"}, {"id": "custom.fillOpacity", "value": 25}, {"id": "custom.lineWidth", "value": 0}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, {"id": "max"}, {"id": "custom.gradientMode", "value": "none"}, {"id": "color", "value": {"fixedColor": "semi-dark-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "id": 2, "maxPerRow": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"query": "let success= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> succeeded!\" and not(user_Id contains \"qarb\") and customDimensions.source == $Sources\n| summarize SuccessfulScrapes = count() by bin(timestamp, $interval), name;\nlet failed= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON>raper failed unexpectedly!\" and not(user_Id contains \"qarb\") and customDimensions.source == $Sources\n| summarize FailedScrapes = count() by bin(timestamp, $interval), name;\nsuccess\n| join kind=fullouter failed on timestamp\n| extend sanitizedFailedScrapes =  iff(FailedScrapes > -1, todouble(FailedScrapes),0.0)\n| extend sanitizedSuccessfulScrapes =  iff(SuccessfulScrapes > -1, todouble(SuccessfulScrapes),0.0)\n| extend sanitizedTimestamp = iff(timestamp < now() , timestamp, timestamp1)\n| extend successRate = (sanitizedSuccessfulScrapes / (sanitizedFailedScrapes+sanitizedSuccessfulScrapes))*100\n| project sanitizedTimestamp, successRate\n| order by sanitizedTimestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"dimensionFilters": [], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "34d925e9-7558-44da-b9df-6af837f7bc14"}, {"azureLogAnalytics": {"query": "customEvents\r\n| where $__timeFilter(timestamp) and (name == \"<PERSON><PERSON><PERSON> succeeded!\" or name == \"<PERSON><PERSON><PERSON> failed unexpectedly!\") and not(user_Id contains \"qarb\") and customDimensions.source == $Sources\r\n| summarize ScrapesVolume = count() by bin(timestamp, $interval)\r\n| order by timestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "B", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "thresholds": [{"colorMode": "critical", "op": "lt", "value": 60, "visible": true}], "title": "Scraper # health test- ${Sources}", "type": "timeseries"}, {"collapsed": true, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 73}, "id": 16, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 2}, "id": 18, "options": {"showHeader": true}, "pluginVersion": "8.2.7", "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"query": "customEvents\n| where name == \"<PERSON><PERSON><PERSON> failed unexpectedly!\" and $__timeFilter(timestamp) and not(user_Id contains \"qarb\") and customDimensions.source == $Sources\n| project timestamp, user_Id, source=customDimensions.source, error=customDimensions.error\n| order by timestamp asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Failed Scrapes $Sources", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "left", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 0}, "displayName": "Success rate", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "totalScrapes"}, "properties": [{"id": "custom.axisPlacement", "value": "right"}, {"id": "unit"}, {"id": "custom.axisLabel", "value": "Total Scrapes"}, {"id": "displayName", "value": "Total Scrapes"}]}]}, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 23}, "id": 40, "options": {"barWidth": 0.9, "groupWidth": 0.65, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "always", "stacking": "none", "text": {}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.7", "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"query": "let success= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON><PERSON><PERSON> succeeded!\" and not(user_Id contains \"qarb\") and customDimensions.source == $Sources\n| summarize SuccessfulScrapes = count() by user_Id;\nlet failed= customEvents\n| where $__timeFilter(timestamp) and name == \"<PERSON>raper failed unexpectedly!\" and not(user_Id contains \"qarb\") and customDimensions.source == $Sources\n| summarize FailedScrapes = count() by user_Id;\nsuccess\n| join kind=fullouter failed on user_Id\n| extend sanitizedFailedScrapes =  iff(FailedScrapes > -1, todouble(FailedScrapes),0.0)\n| extend sanitizedSuccessfulScrapes =  iff(SuccessfulScrapes > -1, todouble(SuccessfulScrapes),0.0)\n| extend sanitizedUserId =  iff(user_Id == '', user_Id1, user_Id)\n| extend successRate = (sanitizedSuccessfulScrapes / (sanitizedFailedScrapes+sanitizedSuccessfulScrapes))*100\n| project sanitizedUserId, successRate, totalScrapes = sanitizedFailedScrapes+sanitizedSuccessfulScrapes\n| order by successRate asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod", "/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-bin-prod/providers/Microsoft.Insights/components/appi-insights-scraper-bin-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Success rate per client for:  $Sources", "type": "barchart"}], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Debug information", "type": "row"}, {"collapsed": true, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 74}, "id": 74, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 230}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "id": 72, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"query": "traces \n| where cloud_RoleName == \"mail-scraper\" and message startswith \"Scraping \" and message endswith \" messages...\" and $__timeFilter(timestamp)\n| project timestamp, message\n| order by timestamp desc \n", "resource": "/subscriptions/34d925e9-7558-44da-b9df-6af837f7bc14/resourceGroups/indiebi-production/providers/microsoft.insights/components/production-logs"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "<PERSON><PERSON> - Health", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 258}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3}, "id": 76, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"query": "traces \n| where cloud_RoleName == \"mail-scraper\" and message contains \"Response from report service\" and $__timeFilter(timestamp)\n| project timestamp, message\n| order by timestamp desc \n\n\n\n\n\n", "resource": "/subscriptions/34d925e9-7558-44da-b9df-6af837f7bc14/resourceGroups/indiebi-production/providers/microsoft.insights/components/production-logs"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Reports send to Report Service V2", "type": "table"}], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "<PERSON><PERSON> - Health", "type": "row"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "description": "Available platforms", "hide": 0, "includeAll": true, "label": "Sources", "multi": true, "name": "Sources", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "steam_sales", "value": "steam_sales"}, {"selected": false, "text": "steam_wishlists", "value": "steam_wishlists"}, {"selected": false, "text": "steam_impressions", "value": "steam_impressions"}, {"selected": false, "text": "steam_wishlist_country", "value": "steam_wishlist_country"}, {"selected": false, "text": "steam_discounts", "value": "steam_discounts"}, {"selected": false, "text": "microsoft_sales", "value": "microsoft_sales"}, {"selected": false, "text": "nintendo_sales", "value": "nintendo_sales"}, {"selected": false, "text": "humble_sales", "value": "humble_sales"}, {"selected": false, "text": "playstation_sales", "value": "playstation_sales"}, {"selected": false, "text": "meta_rift_sales", "value": "meta_rift_sales"}, {"selected": false, "text": "meta_quest_sales", "value": "meta_quest_sales"}, {"selected": false, "text": "app_store_sales", "value": "app_store_sales"}, {"selected": false, "text": "google_sales", "value": "google_sales"}, {"selected": false, "text": "gog_sales", "value": "gog_sales"}, {"selected": false, "text": "epic_sales", "value": "epic_sales"}], "query": "steam_sales, steam_wishlists, steam_impressions,steam_wishlist_country, steam_discounts, microsoft_sales,nintendo_sales, humble_sales, playstation_sales, meta_rift_sales, meta_quest_sales, app_store_sales, google_sales, gog_sales, epic_sales,", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "1h", "value": "1h"}, "hide": 0, "label": "Interval", "name": "interval", "options": [{"selected": false, "text": "30m", "value": "30m"}, {"selected": true, "text": "1h", "value": "1h"}, {"selected": false, "text": "3h", "value": "3h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}], "query": "30m,1h,3h,6h,12h,1d,7d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Scraper platform health monitor", "uid": "ro5dYBcnk", "version": 2, "weekStart": ""}