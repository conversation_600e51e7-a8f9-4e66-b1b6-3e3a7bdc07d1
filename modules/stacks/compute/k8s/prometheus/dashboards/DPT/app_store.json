{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 18, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces \n| where $__timeFilter(timestamp) \n| where message startswith '{\\\"'\n| extend parsed=parse_json(message)\n| where parsed.source == \"app_store_sales\"\n| where parsed.message contains \"Request to API failed with code 213\"\n| union ( // Pad a table with null bins (https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/binfunction#pad-a-table-with-null-bins)\n    range x from 1 to 1 step 1\n    | mv-expand timestamp=range($__timeFrom(), $__timeTo(), 1d) to typeof(datetime)\n    )\n| summarize users=dcount(user_AuthenticatedId) - 1 by bin(timestamp, 1d) // -1 because we introduce artificial rows with empty userAuthenticatedId", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "213 - no data to download", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "errorCode"}, "properties": [{"id": "custom.width", "value": 160}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_AuthenticatedId"}, "properties": [{"id": "custom.width", "value": 183}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 217}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 8, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let regexp=\"app.core.exceptions.ScraperException: \\\\('(.*)', '(.*)'\\\\)\";\ntraces \n| where $__timeFilter(timestamp) and message startswith '{\\\"'\n| extend parsed=parse_json(message)\n| where parsed.source == \"app_store_sales\" and parsed.type == \"error\" \n| extend errorMsg = extract(regexp, 1, tostring(parsed.data)), errorCode = extract(regexp, 2, tostring(parsed.data))\n| project timestamp, user_AuthenticatedId, errorCode, errorMsg", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "API Errors", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces \n| where $__timeFilter(timestamp) and message startswith '{\\\"'\n| extend parsed=parse_json(message)\n| where parsed.source == \"app_store_sales\" and parsed.type == \"error\" and parsed.errorType == \"TEMPORARY_PORTAL_ISSUE\"\n| union ( // Pad a table with null bins (https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/binfunction#pad-a-table-with-null-bins)\n    range x from 1 to 1 step 1\n    | mv-expand timestamp=range($__timeFrom(), $__timeTo(), 1d) to typeof(datetime)\n    )\n| summarize users=dcount(user_AuthenticatedId) - 1 by bin(timestamp, 1d) // -1 because we introduce artificial rows with empty userAuthenticatedId", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"], "resultFormat": "time_series"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "TEMPORARY_PORTAL_ISSUE", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 10, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let allUsers = traces\n    | where message contains \"Starting scrape source: app_store_sales\" and $__timeFilter(timestamp)\n    | distinct user_AuthenticatedId;\nlet okUsers =     traces\n    | where message contains \"Info result\" and $__timeFilter(timestamp)\n    | extend json = parse_json(replace_string(message, \"Report send report info result\", \"\"))\n    | where json.source == \"app_store_sales\"\n    | distinct user_AuthenticatedId;\ntraces\n| where user_AuthenticatedId in (allUsers) and user_AuthenticatedId !in (okUsers) \n| distinct user_AuthenticatedId", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"], "resultFormat": "time_series"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Upload: fail - users", "type": "barchart"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "How many unique users got the 213 error message from the API", "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"match": "null", "result": {"index": 0, "text": "0"}}, "type": "special"}], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces \n| where $__timeFilter(timestamp) and message has \"Received no sales error for this date.\"\n| union ( // Pad a table with null bins (https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/binfunction#pad-a-table-with-null-bins)\n    range x from 1 to 1 step 1\n    | mv-expand timestamp=range($__timeFrom(), $__timeTo(), 1d) to typeof(datetime)\n    )\n| summarize users=dcount(user_AuthenticatedId) - 1 by bin(timestamp, 1d) // -1 because we introduce artificial rows with empty userAuthenticatedId", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Received no sales error for this date", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces\n    | where message contains \"Info result\" and $__timeFilter(timestamp)\n    | extend json = parse_json(replace_string(message, \"Report send report info result\", \"\"))\n    | where json.source == \"app_store_sales\"\n| union ( // Pad a table with null bins (https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/binfunction#pad-a-table-with-null-bins)\n    range x from 1 to 1 step 1\n    | mv-expand timestamp=range($__timeFrom(), $__timeTo(), 1d) to typeof(datetime)\n    )\n| summarize users=dcount(user_AuthenticatedId) - 1 by bin(timestamp, 1d) // -1 because we introduce artificial rows with empty userAuthenticatedId", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Upload: success", "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "App Store", "uid": "-GMMW4LVz", "version": 2, "weekStart": ""}