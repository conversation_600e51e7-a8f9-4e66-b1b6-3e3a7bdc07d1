{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 11456, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "Version distribution", "type": "row"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "#fff899"}, {"color": "#ffee52", "value": 25}, {"color": "#56a64b", "value": 50}, {"color": "#37872d", "value": 75}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 1}, "id": 1, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\n  active_version_id as version, COUNT(*)\nFROM [WebApp].[profile] \nWHERE deletion_timestamp is null\nGROUP BY active_version_id\nUNION  \nSELECT 'total active accounts count' as version, COUNT(*) FROM [WebApp].[profile] WHERE deletion_timestamp is null\nORDER BY version desc", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Studio version distribution", "type": "bargauge"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 5, "panels": [], "title": "Release details", "type": "row"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 3, "x": 0, "y": 10}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["first"], "fields": "/.*/", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\n  id as Version, shard_version as \"Shard Version\"\nFROM\n  WebApp.version\nWHERE is_default = 1", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Default Version", "type": "stat"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 3, "y": 10}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(*) FROM (\nSELECT\n  permission_set_uuid, version_id\nFROM\n  [WebApp].[release]\nWHERE status in ('REQUESTED', 'REQUESTED_SHARD', 'REQUESTED_ASSIGN')\ngroup by permission_set_uuid, version_id) as x", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Unique requested releases count", "type": "stat"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 7, "y": 10}, "id": 10, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(*) from (\nSELECT\n  ps.uuid, ps.permission_set\nFROM\n  [WebApp].[permission_set] as ps\nLEFT JOIN WebApp.profile p on p.permission_set_uuid = ps.uuid \ngroup by ps.uuid, ps.permission_set\nHAVING count(studio_id) > 0) as t", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Shards", "type": "stat"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "logs_link"}, "properties": [{"id": "links", "value": [{"title": "LOGS", "url": "https://indiebi.kb.westeurope.azure.elastic-cloud.com/s/dpt/app/discover#/view/fef5204a-205e-4511-bd23-c3da76b28e7a?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-1y,to:now))&_a=(columns:!(log.level,message),dataSource:(dataViewId:'67984ee5-1eef-436c-81da-c39cc2650d86',type:dataView),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'67984ee5-1eef-436c-81da-c39cc2650d86',key:service.name,negate:!f,params:(query:dataset-manager),type:phrase),query:(match_phrase:(service.name:dataset-manager)))),grid:(columns:(log.level:(width:111))),hideChart:!t,interval:auto,query:(language:kuery,query:'dataset_release.uuid%20:%201${__data.fields.uuid}'),sort:!(!('@timestamp',desc)))"}]}]}]}, "gridPos": {"h": 8, "w": 14, "x": 10, "y": 10}, "id": 7, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT 'LOGS' AS [logs_link], r.*, ps.permission_set\nFROM WebApp.release as r\nLEFT OUTER JOIN [WebApp].profile as p ON (r.studio_id = p.studio_id)\nJOIN WebApp.permission_set as ps on (r.permission_set_uuid = ps.uuid)\nWHERE status in ('REQUESTED', 'REQUESTED_SHARD', 'REQUESTED_ASSIGN')\nORDER BY status DESC, CAST(CASE WHEN (p.studio_id is null) THEN (0) ELSE (1) END AS BIT), r.creation_timestamp", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Requested releases", "type": "table"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 3, "y": 14}, "id": 9, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\n  count(*)\nFROM\n [WebApp].[release]\nWHERE status in ('REQUESTED', 'REQUESTED_SHARD', 'REQUESTED_ASSIGN')", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Requested releases count", "type": "stat"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 7, "y": 14}, "id": 11, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(studio_id) from [WebApp].[profile] where deletion_timestamp is null", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Users", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 12, "panels": [], "title": "Failed releases", "type": "row"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "logs_link"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "LOGS", "url": "https://indiebi.kb.westeurope.azure.elastic-cloud.com/s/dpt/app/discover#/view/fef5204a-205e-4511-bd23-c3da76b28e7a?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-1y,to:now))&_a=(columns:!(log.level,message),dataSource:(dataViewId:'67984ee5-1eef-436c-81da-c39cc2650d86',type:dataView),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'67984ee5-1eef-436c-81da-c39cc2650d86',key:service.name,negate:!f,params:(query:dataset-manager),type:phrase),query:(match_phrase:(service.name:dataset-manager)))),grid:(columns:(log.level:(width:111))),hideChart:!t,interval:auto,query:(language:kuery,query:'dataset_release.uuid%20:%201${__data.fields.uuid}'),sort:!(!('@timestamp',desc)))"}]}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 19}, "id": 13, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "WITH NewestReleaseUUIDs AS (\n    SELECT \n        [uuid], \n        [studio_id], \n        [version_id], \n        [permission_set_uuid], \n        [creation_timestamp], \n        [status], \n        [is_full_recreate], \n        [try_count],\n        ROW_NUMBER() OVER (PARTITION BY [studio_id] ORDER BY [creation_timestamp] DESC) AS RowNum\n    FROM \n        [WebApp].[release]\n)\nSELECT\n    'LOGS' AS [logs_link],\n    [uuid], \n    [studio_id], \n    [version_id], \n    [permission_set_uuid], \n    [creation_timestamp], \n    [status], \n    [is_full_recreate], \n    [try_count]\nFROM \n    NewestReleaseUUIDs\nWHERE \n    RowNum = 1 and [status] = 'FAILED'\nORDER BY \n    [creation_timestamp] DESC;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Failed releases", "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 14, "panels": [], "title": "Permission set", "type": "row"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "shard_version"}, "properties": [{"id": "custom.width", "value": 132}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "studio_id"}, "properties": [{"id": "custom.width", "value": 119}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "active_version_id"}, "properties": [{"id": "custom.width", "value": 149}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "workspace_id"}, "properties": [{"id": "custom.width", "value": 325}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "permission_set_uuid"}, "properties": [{"id": "custom.width", "value": 353}]}]}, "gridPos": {"h": 21, "w": 24, "x": 0, "y": 28}, "id": 15, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "11.6.4", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT p.studio_id, p.active_version_id, p.permission_set_uuid, ps.permission_set, v.shard_version, s.workspace_id, s.workspace_name\nFROM WebApp.profile AS p\nJOIN WebApp.permission_set AS ps ON (p.permission_set_uuid = ps.uuid)\nJOIN WebApp.version AS v ON (v.id = p.active_version_id)\nJOIN WebApp.shard AS s ON (v.shard_version = s.version and p.permission_set_uuid = s.permission_set_uuid)\nWHERE p.studio_id in ($studio_id) and ('$workspace_id' = '' or s.workspace_id = '$workspace_id') and p.deletion_timestamp is null\nORDER BY p.studio_id", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Studio permission set and workspace", "type": "table"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "db-dataset-manager", "value": "sql-server-db-dataset-manager"}, "hide": 2, "includeAll": false, "name": "datasource", "options": [], "query": "mssql", "refresh": 1, "regex": "/.*dataset-manager/", "type": "datasource"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "mssql", "uid": "sql-server-db-dataset-manager"}, "definition": "SELECT p.studio_id FROM WebApp.profile as p ORDER BY p.studio_id ", "includeAll": true, "multi": true, "name": "studio_id", "options": [], "query": "SELECT p.studio_id FROM WebApp.profile as p ORDER BY p.studio_id ", "refresh": 1, "regex": "", "sort": 3, "type": "query"}, {"current": {}, "name": "workspace_id", "options": [], "query": "", "type": "textbox"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Releases", "uid": "ae01af4a-36b2-41e6-a08b-bca084458bb7", "version": 7, "weekStart": ""}