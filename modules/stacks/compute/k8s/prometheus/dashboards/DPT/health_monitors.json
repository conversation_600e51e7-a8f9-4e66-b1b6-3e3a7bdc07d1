{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 16, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 12, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Error browser", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "details"}, "properties": [{"id": "custom.width", "value": 3960}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "type"}, "properties": [{"id": "custom.width", "value": 269}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "customDimensions"}, "properties": [{"id": "custom.width", "value": 1462}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "operation_Name"}, "properties": [{"id": "custom.width", "value": 172}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "email"}, "properties": [{"id": "custom.width", "value": 282}]}]}, "gridPos": {"h": 18, "w": 24, "x": 0, "y": 1}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "details"}]}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let knownTesterIds = dynamic([693,489,723,664,214]);\nlet scpexceptions = exceptions \n| where $__timeFilter(timestamp)\nand \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\nand not(user_AuthenticatedId in (knownTesterIds))\n| where cloud_RoleName == \"ScraperLib\"\n| project timestamp, operation_Name, user_AuthenticatedId, cloud_RoleInstance, email=customDimensions.username, details, customDimensions,application_Version, type, message;\nscpexceptions\n| where not(email endswith \"indiebi.com\")\nand not(details contains \"SESSION_EXPIRED\")\nand not(details contains \"INCORRECT_2FA\")\nand not(details contains \"MANUAL_ACTION_REQUIRED\")\nand not(details contains \"MISSING_PERMISSIONS\")\nand not(details contains \"INCORRECT_CREDENTIALS\")\nand not(details contains \"TEMPORARY_PORTAL_ISSUE\")\nand not(details contains \"SECRET_EXPIRED\")\n| order by timestamp desc\n\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"dimensionFilters": [], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "34d925e9-7558-44da-b9df-6af837f7bc14"}], "title": "CLIENT ScraperLib prod errors", "type": "table"}, {"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 10, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Investigations", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 0, "y": 20}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let firstUserBatch = dynamic([728,735,732,731,730,738,739, 15,134,9]);\nlet usersWithUpload = traces\n| where user_AuthenticatedId in (firstUserBatch) and message contains \"info result\" and $__timeFilter(timestamp)\n| distinct user_AuthenticatedId;\nlet usersWithoutUpload = traces\n| where user_AuthenticatedId in (firstUserBatch) and not(user_AuthenticatedId in (usersWithUpload)) and $__timeFilter(timestamp)\n| distinct user_AuthenticatedId;\ntraces\n|where user_AuthenticatedId in (usersWithoutUpload) and message contains \"Starting scrape\" and $__timeFilter(timestamp)\n| distinct user_AuthenticatedId", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Users who tried to scrape but did not upload", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "dark-red", "mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 0, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 11, "x": 5, "y": 20}, "id": 6, "options": {"barRadius": 0, "barWidth": 1, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "normal", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let tracesMocked = datatable(timestamp:datetime, message:dynamic, customDimensions:dynamic) [\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-09),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    //\n    datetime(2023-02-10),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-10),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-10),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-10),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-11),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-11),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-11),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-11),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-11),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n    //\n    datetime(2023-02-13),\n    dynamic({\"source\":\"steam_wishlists\",\"timestamp\":\"2023-02-09T12:33:21.147Z\",\"data\":\"bannedIP User was banned on STEAM The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"type\":\"error\",\"errorType\":\"IP_BANNED\",\"logLevel\":\"warn\",\"originalError\":{\"telemetryLogLevel\":\"warn\",\"message\":\"bannedIP User was banned on STEAM\",\"suggestedAction\":\"The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.\",\"errorType\":\"IP_BANNED\",\"userFriendlyMessage\":\"bannedIP User was banned on STEAM\",\"type\":7}}),\n    dynamic({\"_MS.ProcessedByMetricExtractors\":\"(Name:'Traces', Ver:'1.1')\",\"username\":\"<EMAIL>\"}),\n];\n\n\ntraces//Mocked\n| where timestamp >= ago(30d)\n| where message contains \"User was banned on STEAM\"\n| extend user = customDimensions.username, whenDate = format_datetime(timestamp, \"yyyy-MM-dd\")\n| project whenDate, user=tostring(customDimensions.username)\n| order by whenDate desc\n| evaluate pivot(user)\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Bans on Steam", "type": "barchart"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 8, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "traces\n| where message contains \".PeriodLinks a:last-of-type\"\n| summarize max(timestamp), count() by user_Id\n| order by max_timestamp\n| project-rename last_error = max_timestamp, number_of_errors = count_\n| join kind=inner (\n    customEvents\n    | where name == \"<PERSON><PERSON><PERSON> succeeded!\" and operation_Name contains \"steam_sales\"\n    | summarize max(timestamp) by user_Id\n    | project-rename last_successful_scrape = max_timestamp\n) on user_Id\n| project user_Id, last_successful_scrape, last_error, potentially_still_affected = iif(last_error > last_successful_scrape, 'yes', 'no'), how_long_has_been_affected = iif(last_error > last_successful_scrape, -datetime_diff('day', last_successful_scrape, now()), dynamic('-'))\n| order by potentially_still_affected, how_long_has_been_affected, last_successful_scrape", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Accounts affected by \"unpublished games\" error on Steam Sales", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": "", "value": ""}, "hide": 0, "name": "UserFilter", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Health monitors", "uid": "UHZ38mO4k", "version": 2, "weekStart": ""}