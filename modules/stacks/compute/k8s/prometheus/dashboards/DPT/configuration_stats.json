{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 17, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 37, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Users 'stuck' in invalid states", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 1}, "id": 41, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\n| where $__timeFilter(timestamp)\n      and application_Version startswith \"2.\"\n      and name == 'Source state update'\n      and customDimensions.username !endswith \"indiebi.com\"\n      and customDimensions.source == $Sources\n      and user_AuthenticatedId != ''\n      and \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\n| extend scraper_status = customDimensions.status, errorType = customDimensions.errorType\n| project scraper_status, errorType,user_AuthenticatedId, timestamp, application_Version\n| summarize arg_max(timestamp, *) by user_AuthenticatedId\n| where scraper_status == \"ERROR\"\n| extend inStateForDays = datetime_diff('day',now(),timestamp)\n| project-reorder inStateForDays, user_AuthenticatedId, scraper_status, errorType\n| order by inStateForDays desc, user_AuthenticatedId asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "$Sources scraper stuck in state", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 33, "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Logout statistics", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 15, "w": 24, "x": 0, "y": 9}, "id": 29, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "horizontal", "showValue": "always", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.7", "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\n| where name == 'Source state update' and (customDimensions.errorType == 'SESSION_EXPIRED' and customDimensions.previousErrorType != 'SESSION_EXPIRED') and customDimensions.source == $Sources and $__timeFilter(timestamp)  // time filter breaks this?!\n| where \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\n| order by timestamp asc\n| project timestamp=  format_datetime(bin(timestamp, 7d),'yyyy-MM-dd'), userId = iff(user_AccountId != '',user_AccountId,user_AuthenticatedId)\n| evaluate pivot(userId)\n| order by  timestamp asc\n| extend timestamp = strcat(timestamp,' - ',format_datetime(datetime_add('day',7,make_datetime(timestamp)),'yyyy-MM-dd'))\n\n\n\n\n\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-scraper-lib-prod/providers/Microsoft.Insights/components/appi-insights-scraper-lib-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Logout frequency for: $Sources per week", "type": "barchart"}, {"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 11, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Configuration times $LoginMethod", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.7", "repeat": "LoginMethod", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let results = customEvents\n| where name in (\"Source setup start.\", \"Successfully logged in to scraper manually.\", \"Source setup finished.\") and $__timeFilter(timestamp)\n| where \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\n| extend operation_Id = tostring(customDimensions.operationId), source = customDimensions.source, mode= tostring(customDimensions.mode)\n| where mode == $LoginMethod\n| order by timestamp desc;\nresults\n| join results on operation_Id\n| where name == \"Source setup start.\" and name1 in (\"Successfully logged in to scraper manually.\", \"Source setup finished.\")\n| project what=strcat (source,  \"_\",mode), when = make_datetime(format_datetime(timestamp, \"yyyy-MM-dd\")), setupTime = (timestamp1 - timestamp)\n| summarize setupTime = avg(setupTime)/1s by what, when\n| where setupTime > 0\n|order by when asc\n\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Average Source Setup: $LoginMethod", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "setupTime"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 7, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.7", "repeat": "LoginMethod", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let results = customEvents\n| where name in (\"Source setup start.\", \"Successfully logged in to scraper manually.\", \"Source setup finished.\") and $__timeFilter(timestamp)\n| where \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\n| extend operation_Id = tostring(customDimensions.operationId), source = tostring(customDimensions.source), mode= tostring(customDimensions.mode)\n| where mode == $LoginMethod\n| order by timestamp desc;\nresults\n| join results on operation_Id\n| where name == \"Source setup start.\" and name1 in (\"Successfully logged in to scraper manually.\", \"Source setup finished.\")\n| summarize arg_max(timestamp,*) by timestamp1, user_AuthenticatedId, mode, source\n| project source, when = make_datetime(format_datetime(timestamp, \"yyyy-MM-dd\")), setupTime = (timestamp1 - timestamp), userId=user_AuthenticatedId, mode\n| summarize setupTime = avg(setupTime)/1s by source, userId\n| where setupTime > 0\n| project whowhat = strcat(userId,'_',source), setupTime\n| order by setupTime asc\n\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Average Configuration time per portal for each user: $LoginMethod", "transformations": [], "type": "barchart"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "setupTime"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 41}, "id": 22, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "let results = customEvents\n| where name in (\"Source setup start.\", \"Successfully logged in to scraper manually.\", \"Source setup finished.\") and $__timeFilter(timestamp)\n| where \"$UserFilter\" == \"\" or user_AuthenticatedId==\"$UserFilter\"\n| extend operation_Id = tostring(customDimensions.operationId), source = tostring(customDimensions.source), mode= tostring(customDimensions.mode)\n| order by timestamp desc;\nresults\n| join results on operation_Id\n| where name == \"Source setup start.\" and name1 in (\"Successfully logged in to scraper manually.\", \"Source setup finished.\")\n| summarize arg_max(timestamp,*) by timestamp1, user_AuthenticatedId, mode, source\n| project source, when = make_datetime(format_datetime(timestamp, \"yyyy-MM-dd\")), setupTime = (timestamp1 - timestamp), userId=user_AuthenticatedId, mode\n| summarize setupTime=sum(setupTime)/1s by whowhen=strcat(userId, ':',format_datetime(when, \"yyyy-MM-dd\"))\n| order by setupTime asc\n\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Total scraper setup time per user: $LoginMethod", "type": "barchart"}, {"collapsed": false, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 9, "panels": [], "targets": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "refId": "A"}], "title": "Canceling", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 51}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents \n| where customDimensions.scraperSetupCancelled == true and $__timeFilter(timestamp)\n| extend portal = customDimensions.portal, whenDate = make_datetime(format_datetime(timestamp, \"yyyy-MM-dd\"))\n| summarize _ = count() by whenDate, tostring(portal)\n| order by whenDate asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Canceled scraper configurations", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "source"}, "properties": [{"id": "custom.width", "value": 123}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 178}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user"}, "properties": [{"id": "custom.width", "value": 319}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "error"}, "properties": [{"id": "custom.width", "value": 719}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 60}, "id": 44, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "exceptions\n| where $__timeFilter(timestamp)\n| where customDimensions.message == 'Scraper | Login to source failed.'\n| order by timestamp desc\n| project timestamp, source=customDimensions.source, error=details[0].message, user=customDimensions.user, user_id=customDimensions.user_id", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Reasons why in-app configuration failed", "type": "table"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "source"}, "properties": [{"id": "custom.width", "value": 123}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 178}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user"}, "properties": [{"id": "custom.width", "value": 319}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "error"}, "properties": [{"id": "custom.width", "value": 719}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 67}, "id": 47, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "exceptions\n| where $__timeFilter(timestamp)\n| where customDimensions.message == 'Scraper | Saving manual session failed.'\n| order by timestamp desc\n| project timestamp, source=customDimensions.source, error=details[0].message, user=customDimensions.user, user_id=customDimensions.user_id", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "Reasons configuration via Manual Login failed", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["inAppLogin", "browserLogin"], "value": ["inAppLogin", "browserLogin"]}, "hide": 0, "includeAll": true, "multi": true, "name": "LoginMethod", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": true, "text": "inAppLogin", "value": "inAppLogin"}, {"selected": true, "text": "browserLogin", "value": "browserLogin"}], "query": "inAppLogin, browserLogin", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "name": "UserFilter", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": true, "text": ["steam_sales"], "value": ["steam_sales"]}, "description": "", "hide": 0, "includeAll": false, "multi": true, "name": "Sources", "options": [{"selected": true, "text": "steam_sales", "value": "steam_sales"}, {"selected": false, "text": "steam_wishlists", "value": "steam_wishlists"}, {"selected": false, "text": "steam_impressions", "value": "steam_impressions"}, {"selected": false, "text": "steam_wishlist_country", "value": "steam_wishlist_country"}, {"selected": false, "text": "steam_discounts", "value": "steam_discounts"}, {"selected": false, "text": "microsoft_sales", "value": "microsoft_sales"}, {"selected": false, "text": "nintendo_sales", "value": "nintendo_sales"}, {"selected": false, "text": "humble_sales", "value": "humble_sales"}, {"selected": false, "text": "playstation_sales", "value": "playstation_sales"}, {"selected": false, "text": "meta_rift_sales", "value": "meta_rift_sales"}, {"selected": false, "text": "meta_quest_sales", "value": "meta_quest_sales"}, {"selected": false, "text": "app_store_sales", "value": "app_store_sales"}, {"selected": false, "text": "google_sales", "value": "google_sales"}, {"selected": false, "text": "gog_sales", "value": "gog_sales"}, {"selected": false, "text": "epic_sales", "value": "epic_sales"}], "query": "steam_sales, steam_wishlists, steam_impressions,steam_wishlist_country, steam_discounts, microsoft_sales,nintendo_sales, humble_sales, playstation_sales, meta_rift_sales, meta_quest_sales, app_store_sales, google_sales, gog_sales, epic_sales,", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Configuration stats", "uid": "GDE0KzDVz", "version": 2, "weekStart": ""}