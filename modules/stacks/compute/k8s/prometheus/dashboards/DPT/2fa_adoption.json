{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 19, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 0}, "id": 2, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "horizontal", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.2.7", "repeat": "Sources", "repeatDirection": "h", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\r\n| where $__timeFilter(timestamp) and customDimensions.emitObject <> \"\" and not(user_AuthenticatedId in (214,223,407,438,458,578,664,884))\r\n| extend emit = parse_json(tostring(parse_json(tostring(parse_json(customDimensions.emitObject))).message)), formated_date = format_datetime(bin(timestamp, 1d),'yyyy-MM-dd')\r\n| where emit.type == 'dualAuth' and emit.attempt == 1 and emit.success == '' and tostring(emit.source) == $Sources\r\n| project tostring(formated_date), user_AuthenticatedId\r\n| evaluate pivot(user_AuthenticatedId)\r\n| order by  formated_date asc", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "2FA flow started for $Sources", "transformations": [], "transparent": true, "type": "barchart"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "started"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "success"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "unfinished"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 10}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\r\n| where $__timeFilter(timestamp) and customDimensions.emitObject <> \"\" and not(user_AuthenticatedId in (214,223,407,438,458,578,664,884))\r\n| extend emit = parse_json(tostring(parse_json(tostring(parse_json(customDimensions.emitObject))).message))\r\n| summarize success = countif(emit.type == 'dualAuth' and emit.success == 'true'), started = countif(emit.type == 'dualAuth' and emit.retryCount == 0 and emit.success == '') by timestamp=  format_datetime(bin(timestamp, 1d),'yyyy-MM-dd')\r\n| sort by todatetime(timestamp) asc \r\n| project started, success, timestamp = todatetime(timestamp)\r\n", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"], "resultFormat": "table"}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "2FA success rate", "transformations": [], "transparent": true, "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "second_attempt"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "third_attempt"}, "properties": [{"id": "color", "value": {"fixedColor": "light-yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 20}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.2.7", "targets": [{"azureLogAnalytics": {"intersectTime": false, "query": "customEvents\r\n| where $__timeFilter(timestamp) and customDimensions.emitObject <> \"\" and not(user_AuthenticatedId in (214,223,407,438,458,578,664,884))\r\n| extend emit = parse_json(tostring(parse_json(tostring(parse_json(customDimensions.emitObject))).message))\r\n| where emit.type == 'dualAuth' \r\n| summarize started = countif(emit.retryCount == 0 and emit.success == ''), c0 = countif(emit.success == 'true' and emit.retryCount == 0), c1 = countif(emit.success == 'true' and emit.retryCount == 1), c2 = countif(emit.success == 'true' and emit.retryCount == 2), c3 = countif(emit.success == 'true' and emit.retryCount == 3), c4 = countif(emit.success == 'true' and emit.retryCount == 4) by timestamp=  format_datetime(bin(timestamp, 1d),'yyyy-MM-dd')\r\n| sort by todatetime(timestamp) asc\r\n| project todatetime(timestamp), first_attempt = c0, second_attempt = c1, third_attempt = c2, fourth_attempt = c3, fifth_attempt = c4, unfinished = started - (c0 + c1 + c2 + c3 + c4)", "resources": ["/subscriptions/5d37e76e-270f-4dc0-939f-d667a5fb1f57/resourceGroups/rg-insights-electron-app-prod/providers/Microsoft.Insights/components/appi-insights-electron-app-prod"]}, "azureMonitor": {"timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Log Analytics", "refId": "A", "subscription": "97bd5d65-c018-4632-b6c3-0feeb064b3d1"}], "title": "2FA attempt", "transformations": [], "transparent": true, "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": ["gog_sales", "steam_impressions", "steam_wishlists"], "value": ["gog_sales", "steam_impressions", "steam_wishlists"]}, "hide": 0, "includeAll": false, "multi": true, "name": "Sources", "options": [{"selected": true, "text": "steam_wishlists", "value": "steam_wishlists"}, {"selected": true, "text": "gog_sales", "value": "gog_sales"}, {"selected": true, "text": "steam_impressions", "value": "steam_impressions"}], "query": "steam_wishlists, gog_sales, steam_impressions", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "2FA adaptation", "uid": "0Hhjago4z", "version": 2, "weekStart": ""}