{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 42, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "auto", "cellOptions": {"type": "color-text"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "id"}, "properties": [{"id": "custom.width", "value": 89}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "studio_id"}, "properties": [{"id": "custom.width", "value": 103}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "source"}, "properties": [{"id": "custom.width", "value": 179}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "file_path_raw"}, "properties": [{"id": "custom.width", "value": 355}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "original_name"}, "properties": [{"id": "custom.width", "value": 390}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "portal"}, "properties": [{"id": "custom.width", "value": 118}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "date_from"}, "properties": [{"id": "custom.width", "value": 169}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "date_to"}, "properties": [{"id": "custom.width", "value": 168}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "upload_date"}, "properties": [{"id": "custom.width", "value": 171}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "no_data"}, "properties": [{"id": "custom.width", "value": 118}]}]}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-dataset-manager", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT *\nFROM [WebApp].[report]\nWHERE portal in ($portal)\nand studio_id in ($studio_id)\nORDER BY upload_date DESC", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "All Reports", "type": "table"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "red", "mode": "fixed"}, "custom": {"align": "auto", "cellOptions": {"mode": "basic", "type": "color-background"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 11}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-report-service", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT upload_date, studio_id, id, portal, source, file_path_raw,  state\nFROM [WebApp].[report]\nWHERE state = 'FAILED' \nand portal in ($portal)\nand studio_id in ($studio_id)\nORDER BY id desc", "refId": "A", "sql": {"columns": [{"parameters": [{"name": "*", "type": "functionParameter"}], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}, "table": "WebApp.report"}], "title": "FAILED", "type": "table"}, {"datasource": {"type": "mssql", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "custom": {"align": "auto", "cellOptions": {"mode": "basic", "type": "color-background"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "studio_id"}, "properties": [{"id": "custom.width", "value": 97}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "upload_date"}, "properties": [{"id": "custom.width", "value": 192}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "id"}, "properties": [{"id": "custom.width", "value": 122}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "portal"}, "properties": [{"id": "custom.width", "value": 169}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "source"}, "properties": [{"id": "custom.width", "value": 221}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "file_path_raw"}, "properties": [{"id": "custom.width", "value": 370}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 11}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.1.5", "targets": [{"dataset": "db-report-service", "datasource": {"type": "mssql", "uid": "${datasource}"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT upload_date, studio_id, id, portal, source, file_path_raw,  state\nFROM [WebApp].[report]\nWHERE state = 'PENDING' \nand portal in ($portal)\nand studio_id in ($studio_id)\nORDER BY id desc", "refId": "A", "sql": {"columns": [{"parameters": [{"name": "*", "type": "functionParameter"}], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}, "table": "WebApp.report"}], "title": "PENDING", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "mssql", "uid": "sql-server-db-report-service"}, "definition": "SELECT portal FROM [WebApp].[report] GROUP BY portal ORDER BY portal ASC; ", "hide": 0, "includeAll": true, "multi": true, "name": "portal", "options": [], "query": "SELECT portal FROM [WebApp].[report] GROUP BY portal ORDER BY portal ASC; ", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "mssql", "uid": "sql-server-db-report-service"}, "definition": "SELECT studio_id FROM [WebApp].[report] GROUP BY studio_id ORDER BY studio_id ASC; ", "hide": 0, "includeAll": true, "multi": true, "name": "studio_id", "options": [], "query": "SELECT studio_id FROM [WebApp].[report] GROUP BY studio_id ORDER BY studio_id ASC; ", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "db-report-service", "value": "sql-server-db-report-service"}, "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "mssql", "refresh": 1, "regex": "/.*report-service/", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Reports", "uid": "e054958b-c6a2-4c67-a339-6a6915e0037d", "version": 2, "weekStart": ""}