{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 27, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 99, "panels": [], "title": "SLIs", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 1}, "id": 118, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"secretstore\", result=\"error\"}[15m])))\n/\n(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"secretstore\"}[15m])))\n> 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "SecretStore error rate [15m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 4, "y": 1}, "id": 121, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"clustersecretstore\", result=\"error\"}[15m])))\n/\n(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"clustersecretstore\"}[15m])))\n> 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "ClusterSecretStore error rate [15m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 8, "y": 1}, "id": 119, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"externalsecret\", result=\"error\"}[15m])))\n/\n(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"externalsecret\"}[15m])))\n> 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "ExternalSecret error rate [15m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 12, "y": 1}, "id": 120, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "(sum(irate(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"clusterexternalsecret\", result=\"error\"}[15m])))\n/\n(sum(irate(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"clusterexternalsecret\"}[15m])))\n> 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "ClusterExternalSecret error rate [15m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 16, "y": 1}, "id": 122, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"pushsecret\", result=\"error\"}[15m])))\n/\n(sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"pushsecret\"}[15m])))\n> 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "PushSecret error rate [15m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 1}, "id": 123, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(increase(externalsecret_provider_api_calls_count{service=~\".*external-secrets.*\", status=\"error\"}[15m]))\n/\nsum(increase(externalsecret_provider_api_calls_count{service=~\".*external-secrets.*\"}[15m]))\n> 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Provider error rate [15m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 7}, "id": 147, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(\n  workqueue_depth{service=~\"external-secrets.*\"}\n) by (name)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Workqueue depth", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 0, "y": 8}, "id": 145, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(increase(controller_runtime_webhook_requests_total{service=~\"external-secrets.*\",code=\"500\"}[15m]))\n/\nsum(increase(controller_runtime_webhook_requests_total{service=~\"external-secrets.*\"}[15m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Webhook error rate [15m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 100}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 4, "y": 8}, "id": 146, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "histogram_quantile(0.99,\n  sum(rate(controller_runtime_webhook_latency_seconds_bucket{service=~\"external-secrets.*\"}[5m])) by (le)\n)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Webhook latency [5m]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 16, "y": 8}, "id": 148, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "histogram_quantile(0.99,\n  sum(rate(controller_runtime_reconcile_time_seconds_bucket{service=~\"external-secrets.*\"}[5m])) by (le)\n)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Reconcile latency [p99]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 20, "y": 8}, "id": 149, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(increase(controller_runtime_reconcile_total{service=~\"external-secrets.*\",controller=~\"$controller\",result=\"error\"}[1m]))\n/\nsum(increase(controller_runtime_reconcile_total{service=~\"external-secrets.*\",controller=~\"$controller\"}[1m]))\n> 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "reconcile error rate [p99]", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 14}, "id": 124, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "increase(externalsecret_provider_api_calls_count{service=~\".*external-secrets.*\", status=\"error\"}[15m])", "legendFormat": "{{provider}}/{{call}}", "range": true, "refId": "A"}], "title": "Provider errors [15m]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.hidden", "value": true}]}]}, "gridPos": {"h": 8, "w": 7, "x": 8, "y": 14}, "id": 125, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": [], "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "sum(externalsecret_status_condition{condition=\"Ready\",status=\"False\"}) by (namespace, name) == 1", "format": "table", "instant": true, "legendFormat": "{{provider}}/{{call}}", "range": false, "refId": "A"}], "title": "Not Ready ExternalSecrets  [15m]", "transformations": [], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 14}, "id": 126, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(increase(externalsecret_sync_calls_error[15m])) by (name, namespace)", "legendFormat": "{{namespace}}/{{name}}", "range": true, "refId": "A"}], "title": "ExternalSecret sync call errors [15m]", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 27, "panels": [{"datasource": "$datasource", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 53, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(increase(controller_runtime_webhook_requests_total{service=~\".*external-secrets.*\"}[1m])) by (webhook)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "requests by path per minute", "type": "timeseries"}, {"datasource": "$datasource", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 67, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(controller_runtime_webhook_requests_in_flight{service=~\".*external-secrets.*\"}) by (webhook)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "requests in flight", "type": "timeseries"}, {"datasource": "$datasource", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 80, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(increase(controller_runtime_webhook_requests_total{service=~\".*external-secrets.*\"}[1m])) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "requests by code per minute", "type": "timeseries"}, {"datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 54, "options": {"calculate": false, "cellGap": 1, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "Oranges", "steps": 64}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false}}, "pluginVersion": "9.5.2", "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(rate(controller_runtime_webhook_latency_seconds_bucket{service=~\".*external-secrets.*\"}[$__rate_interval])) by (le)", "legendFormat": "{{le}}", "range": true, "refId": "A"}], "title": "webhook latency", "type": "heatmap"}], "title": "Admission Control Webhook", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 17, "panels": [{"datasource": "$datasource", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 0, "y": 17}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(controller_runtime_active_workers{service=~\".*external-secrets.*\",controller=~\"$controller\"}) by (controller)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "active workers by controller", "type": "timeseries"}, {"datasource": "$datasource", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 7, "y": 17}, "id": 37, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(workqueue_depth{service=~\".*external-secrets.*\"}) by (name)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "workqueue depth", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 17}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(increase(externalsecret_provider_api_calls_count{service=~\".*external-secrets.*\"}[1m])) by(provider, call, status)", "legendFormat": "{{provider}}/{{call}}={{status}}", "range": true, "refId": "A"}], "title": "API calls by provider", "type": "timeseries"}, {"datasource": "$datasource", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3.4285714285714284, "x": 0, "y": 25}, "id": 5, "maxPerRow": 12, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.2", "repeat": "controller", "repeatDirection": "h", "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(controller_runtime_max_concurrent_reconciles{service=~\".*external-secrets.*\",controller=\"$controller\"}) by (controller)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "max concurrent: $controller", "type": "stat"}, {"datasource": "$datasource", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 3.4285714285714284, "x": 0, "y": 31}, "id": 3, "maxPerRow": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "controller", "repeatDirection": "h", "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "sum(increase(controller_runtime_reconcile_total{service=~\".*external-secrets.*\",controller=~\"$controller\"}[1m])) by (result)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "reconcile rate per minute: $controller", "type": "timeseries"}, {"datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 3.4285714285714284, "x": 0, "y": 39}, "id": 39, "maxPerRow": 8, "options": {"calculate": false, "cellGap": 1, "cellValues": {"unit": "short"}, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "Oranges", "steps": 10}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "max": "5", "min": 0, "reverse": false, "unit": "s"}}, "pluginVersion": "9.5.2", "repeat": "controller", "repeatDirection": "h", "targets": [{"datasource": "$datasource", "editorMode": "code", "expr": "rate(controller_runtime_reconcile_time_seconds_bucket{service=~\".*external-secrets.*\",controller=~\"$controller\"}[$__rate_interval])", "legendFormat": "{{le}}", "range": true, "refId": "A"}], "title": "reconcile time latency: $controller", "type": "heatmap"}], "title": "Controllers", "type": "row"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "hide": 0, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "$datasource"}, "definition": "label_values(controller_runtime_active_workers{service=~\".*external-secrets.*\"},  controller)", "hide": 0, "includeAll": true, "multi": true, "name": "controller", "options": [], "query": {"query": "label_values(controller_runtime_active_workers{service=~\".*external-secrets.*\"},  controller)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "External Secrets Operator", "uid": "84db2171-bcad-430a-ad42-e689b26a9cbf", "version": 9, "weekStart": ""}