{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Visualize metrics provided by KEDA", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 790, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Total number of KEDA custom resources per namespace for each custom resource type (CRD)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 4, "x": 0, "y": 0}, "id": 18, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "keda_resource_totals{type=\"scaled_job\", exported_namespace=\"$namespace\"}", "instant": false, "legendFormat": "", "range": true, "refId": "A"}], "title": "Number of Scaled Jobs", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "The total number of errors encountered for all scalers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Errors/sec"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "http-demo"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "scaledObject"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "keda-system/keda-operator-metrics-apiserver"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 10, "x": 4, "y": 0}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(job) (rate(keda_scaler_errors{}[$__rate_interval]))", "legendFormat": "{{ job }}", "range": true, "refId": "A"}], "title": "Scaler Total Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "The number of errors that have occurred for each scaler", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Errors/sec"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "http-demo"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "scaler"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "prometheusScaler"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 10, "x": 14, "y": 0}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(scaler) (rate(keda_scaler_errors{exported_namespace=~\"$namespace\", scaledObject=~\"$scaled_job\", scaler=~\"$scaler\"}[$__rate_interval]))", "legendFormat": "{{ scaler }}", "range": true, "refId": "A"}], "title": "Scaler Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "The number of errors that have occurred for each scaled job", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Errors/sec"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "http-demo"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 10}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "rate(keda_scaled_job_errors{exported_namespace=~\"$namespace\", scaledJob=~\"$scaled_job\"}[$__rate_interval])", "legendFormat": "{{ <PERSON><PERSON>ob }}", "range": true, "refId": "A"}], "title": "Scaled Jobs Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "The latency of retrieving current metric from each scaler", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 20}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "keda_scaler_metrics_latency{exported_namespace=\"$namespace\", scaledObject=~\"$scaled_job\"}", "instant": false, "legendFormat": "{{ scaledObject }}", "range": true, "refId": "A"}], "title": "Scaler Metrics Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "The current value for each scaler’s metric that would be used by the HPA in computing the target average", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "http-demo"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 20}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(metric) (rate(keda_scaler_metrics_value{exported_namespace=~\"$namespace\", metric=~\"$metric\", scaledObject=~\"$scaled_job\"}[$__rate_interval]))", "legendFormat": "{{ metric }}", "range": true, "refId": "A"}], "title": "Scaler Metric Value", "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "default", "value": "default"}, "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": "dpt", "value": "dpt"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(keda_scaler_active,exported_namespace)", "hide": 0, "includeAll": false, "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(keda_scaler_active,exported_namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(keda_scaler_active{exported_namespace=\"$namespace\"},scaledObject)", "hide": 0, "includeAll": true, "multi": true, "name": "scaled_job", "options": [], "query": {"query": "label_values(keda_scaler_active{exported_namespace=\"$namespace\"},scaledObject)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "azureServiceBusScaler", "value": "azureServiceBusScaler"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(keda_scaler_active{exported_namespace=\"$namespace\"},scaler)", "hide": 2, "includeAll": false, "multi": false, "name": "scaler", "options": [], "query": {"query": "label_values(keda_scaler_active{exported_namespace=\"$namespace\"},scaler)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "s0-azure-servicebus-data-jobs-topic", "value": "s0-azure-servicebus-data-jobs-topic"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(keda_scaler_active{exported_namespace=\"$namespace\"},metric)", "hide": 2, "includeAll": false, "multi": false, "name": "metric", "options": [], "query": {"query": "label_values(keda_scaler_active{exported_namespace=\"$namespace\"},metric)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "KEDA", "uid": "0ef41ae4-22c4-459b-a6e6-23f96605665b", "version": 10, "weekStart": ""}