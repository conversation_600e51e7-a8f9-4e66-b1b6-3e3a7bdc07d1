{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4243, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "fieldMinMax": false, "mappings": [], "max": 1, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "red", "value": 85}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 0}, "id": 1, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum by (node) (container_memory_usage_bytes{container != \"POD\", container != \"\"}) / sum by (node) (kube_node_status_capacity{resource=\"memory\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Memory utilization (RSS)", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "red", "value": 85}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 0}, "id": 11, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum by (node) (container_memory_working_set_bytes{container != \"POD\", container != \"\"}) / sum by (node) (kube_node_status_capacity{resource=\"memory\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Memory utilization (working set)", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "red", "value": 85}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 0}, "id": 5, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (node) (rate(container_cpu_usage_seconds_total{container != \"POD\"}[5m])) / sum by (node) (kube_node_status_capacity{resource=\"cpu\"})", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "CPU utilization", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 10}, "id": 7, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (node, condition) (kube_node_status_condition{status = \"true\"}) > 0", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Node condition", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "condition": false, "node": false}, "includeByName": {}, "indexByName": {"Time": 0, "Value": 3, "condition": 2, "node": 1}, "renameByName": {"condition": "Condition", "node": "Node"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Node"}, "properties": [{"id": "custom.width", "value": 298}]}]}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 10}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 2, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Node"}]}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (node) (kube_node_status_capacity{resource=\"memory\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Memory capacity", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value": "Total memory", "node": "Node"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 10}, "id": 6, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 2, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Node"}]}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (node) (kube_node_status_capacity{resource=\"cpu\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "CPU capacity", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value": "Total CPU (cores)", "node": "Node"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 20}, "id": 8, "maxPerRow": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "node_pool_prefixes", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum (kube_node_info{node=~\"${node_pool_prefixes:raw}-.*\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node pool scaling ($node_pool_prefixes)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 29}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"editorMode": "code", "expr": "clamp_min(\n  count(\n    (\n      (1 - (\n        sum by (node) (rate(container_cpu_usage_seconds_total{container!=\"\", node=~\".*default.*\"}[5m]))\n        /\n        sum by (node) (kube_node_status_allocatable{resource=\"cpu\", node=~\".*default.*\"})\n      )) > 0.5\n      and\n      (1 - (\n        sum by (node) (container_memory_working_set_bytes{container!=\"\", node=~\".*default.*\"})\n        /\n        sum by (node) (kube_node_status_allocatable{resource=\"memory\", node=~\".*default.*\"})\n      )) > 0.5\n    )\n  ) - min_over_time(sum (kube_node_info{node=~\".*default.*\"})[24h:]), 0)", "legendFormat": "count", "range": true, "refId": "A"}], "title": "Underutilized nodes above minimum (default node pool)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 29}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"editorMode": "code", "expr": "clamp_min(\n  count(\n    (\n      (1 - (\n        sum by (node) (rate(container_cpu_usage_seconds_total{container!=\"\", node=~\".*webapps.*\"}[5m]))\n        /\n        sum by (node) (kube_node_status_allocatable{resource=\"cpu\", node=~\".*webapps.*\"})\n      )) > 0.5\n      and\n      (1 - (\n        sum by (node) (container_memory_working_set_bytes{container!=\"\", node=~\".*webapps.*\"})\n        /\n        sum by (node) (kube_node_status_allocatable{resource=\"memory\", node=~\".*webapps.*\"})\n      )) > 0.5\n    )\n  ) - min_over_time(sum (kube_node_info{node=~\".*webapps.*\"})[24h:]), 0)", "legendFormat": "count", "range": true, "refId": "A"}], "title": "Underutilized nodes above minimum (webapps node pool)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 29}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"editorMode": "code", "expr": "clamp_min(\n  count(\n    (\n      (1 - (\n        sum by (node) (rate(container_cpu_usage_seconds_total{container!=\"\", node=~\".*datajobs.*\"}[5m]))\n        /\n        sum by (node) (kube_node_status_allocatable{resource=\"cpu\", node=~\".*datajobs.*\"})\n      )) > 0.5\n      and\n      (1 - (\n        sum by (node) (container_memory_working_set_bytes{container!=\"\", node=~\".*datajobs.*\"})\n        /\n        sum by (node) (kube_node_status_allocatable{resource=\"memory\", node=~\".*datajobs.*\"})\n      )) > 0.5\n    )\n  ) - min_over_time(sum (kube_node_info{node=~\".*datajobs.*\"})[24h:]), 0)", "legendFormat": "count", "range": true, "refId": "A"}], "title": "Underutilized nodes above minimum (datajobs node pool)", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "All", "value": "$__all"}, "hide": 2, "includeAll": true, "multi": true, "name": "node_pool_prefixes", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "aks-default", "value": "aks-default"}, {"selected": false, "text": "aks-webapps", "value": "aks-webapps"}, {"selected": false, "text": "aks-datajobs", "value": "aks-datajobs"}], "query": "aks-default,aks-webapps,aks-datajobs", "type": "custom"}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Cluster nodes summary", "uid": "faa183d5-f90f-4876-a2cb-bdca967b3100", "version": 3, "weekStart": ""}