{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "", "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1375, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (pod, namespace) (kube_pod_status_reason{reason=\"Evicted\"}) > 0", "format": "table", "instant": true, "legendFormat": "", "range": false, "refId": "A"}], "title": "Evicted pods", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Time": "", "Value": "Count", "namespace": "Namespace", "pod": "Pod"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum (kube_pod_status_reason{reason=\"Evicted\"})", "format": "time_series", "instant": false, "legendFormat": "Evictions", "range": true, "refId": "A"}], "title": "Evicted pods over time", "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Evictions", "uid": "d69393b0-6df7-4546-9eb0-2cb4bd301c78", "version": 2, "weekStart": ""}