{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1255, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Usage/requests ratio"}, "properties": [{"id": "unit"}, {"id": "decimals", "value": 2}]}]}, "gridPos": {"h": 22, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": true, "fields": ["Value #maximum memory usage", "Value #memory requests", "Value #memory limits"], "reducer": ["sum"], "show": true}, "frameIndex": 0, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Usage/requests ratio"}]}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (max_over_time(container_memory_working_set_bytes{container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"}[$__range]) and on(namespace, container) kube_pod_container_resource_requests{resource=\"memory\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "maximum memory usage"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (kube_pod_container_resource_requests{resource=\"memory\", container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"})", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "memory requests"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (kube_pod_container_resource_limits{resource=\"memory\", container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"})", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "memory limits"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (max_over_time(container_memory_working_set_bytes{container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"}[$__range]) and on(namespace, container) kube_pod_container_resource_requests{resource=\"memory\"})\n/\nmax by (namespace, container) (kube_pod_container_resource_requests{resource=\"memory\", container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"})", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "usage to requests ratio"}], "title": "Memory usage vs requests", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "Value #usage to requests ratio": false}, "indexByName": {"Time": 0, "Value #maximum memory usage": 3, "Value #memory limits": 5, "Value #memory requests": 4, "container": 2, "namespace": 1}, "renameByName": {"Value #Maximum memory usage": "Maximum memory usage", "Value #Memory limits": "Memory limits", "Value #Memory request": "Memory requests", "Value #maximum memory usage": "Maximum usage", "Value #memory limits": "Limits", "Value #memory request": "Memory requests", "Value #memory requests": "Requests", "Value #usage to requests ratio": "Usage/requests ratio", "container": "Container", "namespace": "Namespace"}}}, {"id": "joinByField", "options": {"mode": "outerTabular"}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": true}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Usage/requests ratio"}, "properties": [{"id": "unit"}, {"id": "decimals", "value": 2}]}]}, "gridPos": {"h": 22, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": true, "fields": ["Value #maximum cpu usage", "Value #cpu limits", "Value #cpu requests"], "reducer": ["sum"], "show": true}, "frameIndex": 0, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Usage/requests ratio"}]}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (irate(container_cpu_usage_seconds_total{container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"}[$__range]) and on(namespace, container) kube_pod_container_resource_requests{resource=\"cpu\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "maximum cpu usage"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (kube_pod_container_resource_requests{resource=\"cpu\", container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"}) ", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "cpu requests"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (kube_pod_container_resource_limits{resource=\"cpu\", container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"}) ", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "cpu limits"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace, container) (irate(container_cpu_usage_seconds_total{container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"}[$__range]) and on(namespace, container) kube_pod_container_resource_requests{resource=\"cpu\"})\n/\nmax by (namespace, container) (kube_pod_container_resource_requests{resource=\"cpu\", container!=\"POD\", container!=\"\", node=~\"$node_prefix.*\"}) ", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "usage to requests ratio"}], "title": "CPU usage vs requests", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "Value #usage to requests ratio": false}, "indexByName": {"Time": 0, "Value #maximum memory usage": 3, "Value #memory limits": 5, "Value #memory requests": 4, "container": 2, "namespace": 1}, "renameByName": {"Value #Maximum memory usage": "Maximum memory usage", "Value #Memory limits": "Memory limits", "Value #Memory request": "Memory requests", "Value #cpu limits": "Limits", "Value #cpu requests": "Requests", "Value #maximum cpu usage": "Maximum usage", "Value #maximum memory usage": "Maximum memory usage", "Value #memory limits": "Memory limits", "Value #memory request": "Memory requests", "Value #memory requests": "Memory requests", "Value #usage to requests ratio": "Usage/requests ratio", "container": "Container", "namespace": "Namespace"}}}, {"id": "joinByField", "options": {"mode": "outerTabular"}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 22}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "group by(namespace, container) (kube_pod_container_status_running == 1 unless on (namespace, container) (kube_pod_container_resource_requests{resource=\"memory\"} or kube_pod_container_resource_limits{resource=\"memory\"}))", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Containers without memory requests/limits", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true}, "indexByName": {"Time": 0, "Value": 3, "container": 2, "namespace": 1}, "renameByName": {"container": "Container", "namespace": "Namespace"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 22}, "id": 5, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "group by(namespace, container) (kube_pod_container_status_running == 1 unless on (namespace, container) (kube_pod_container_resource_requests{resource=\"cpu\"} or kube_pod_container_resource_limits{resource=\"cpu\"}))", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Containers without CPU requests/limits", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true}, "indexByName": {"Time": 0, "Value": 3, "container": 2, "namespace": 1}, "renameByName": {"container": "Container", "namespace": "Namespace"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 30}, "id": 6, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_node_status_capacity{resource=\"memory\", node=~\"$node_prefix.*\"}) by (node)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node memory", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 41}, "id": 7, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kube_node_status_capacity{resource=\"cpu\", node=~\"$node_prefix.*\"}) by (node)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Node CPU", "type": "stat"}], "refresh": "", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": true, "text": "aks-default", "value": "aks-default"}, "definition": "label_values(node)", "hide": 0, "includeAll": false, "label": "Node prefix", "multi": false, "name": "node_prefix", "options": [], "query": {"qryType": 1, "query": "label_values(node)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/^(aks-[^-]+)/", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Resource requests vs usage", "uid": "fca02bb2-6b41-4ef4-a6e2-b45c2a8421a9", "version": 1, "weekStart": ""}