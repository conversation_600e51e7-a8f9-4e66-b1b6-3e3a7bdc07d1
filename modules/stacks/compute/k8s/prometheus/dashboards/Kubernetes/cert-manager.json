{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 931, "links": [], "liveNow": false, "panels": [{"datasource": {"uid": "$datasource"}, "description": "The number of certificates in the ready state.", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "True"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum by (condition) (certmanager_certificate_ready_status)", "interval": "", "legendFormat": "{{condition}}", "refId": "A"}], "title": "Certificates Ready", "type": "stat"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "#EAB839", "value": 604800}, {"color": "green", "value": 1209600}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "min(certmanager_certificate_expiration_timestamp_seconds > 0) - time()", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"uid": "$datasource"}, "expr": "vector(1250000)", "hide": true, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Soonest Cert Expiry", "type": "stat"}, {"datasource": {"uid": "$datasource"}, "description": "Status of the certificates. Values are True, False or Unknown.", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [{"options": {"": {"text": "Yes"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Ready Status"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON>id <PERSON>"}, "properties": [{"id": "unit", "value": "dateTimeAsIso"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON>id <PERSON>"}, "properties": [{"id": "unit", "value": "dateTimeAsIso"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 9, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "<PERSON>id <PERSON>"}]}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "label_join(avg by (name, namespace, condition, exported_namespace) (certmanager_certificate_ready_status == 1), \"namespaced_name\", \"-\", \"namespace\", \"exported_namespace\", \"name\")", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"uid": "$datasource"}, "expr": "label_join(avg by (name, namespace, exported_namespace) (certmanager_certificate_expiration_timestamp_seconds) * 1000, \"namespaced_name\", \"-\", \"namespace\", \"exported_namespace\", \"name\")", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Certificates", "transformations": [{"id": "seriesToColumns", "options": {"byField": "namespaced_name"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Value #A": true, "exported_namespace": false, "exported_namespace 1": false, "exported_namespace 2": true, "name 1": true, "namespace 2": true, "namespaced_name": true}, "indexByName": {"Time 1": 8, "Time 2": 10, "Value #A": 6, "Value #B": 5, "condition": 4, "exported_namespace 1": 1, "exported_namespace 2": 11, "name 1": 9, "name 2": 3, "namespace": 0, "namespace 1": 2, "namespaced_name": 7}, "renameByName": {"Time 1": "", "Value #B": "<PERSON>id <PERSON>", "condition": "Ready Status", "exported_namespace": "Certificate Namespace", "exported_namespace 1": "Certificate Namespace", "exported_namespace 2": "", "name": "Certificate", "name 2": "Certificate", "namespace": "Namespace", "namespace 1": "Namespace", "namespaced_name": ""}}}], "type": "table"}, {"datasource": {"uid": "$datasource"}, "description": "The rate of controller sync requests.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 7, "interval": "20s", "maxDataPoints": 250, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum by (controller) (\n  rate(certmanager_controller_sync_call_count[$__rate_interval])\n)", "interval": "", "legendFormat": "{{controller}}", "refId": "A"}], "title": "Controller Sync Requests/sec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "CPU Usage and limits, as percent of a vCPU core. ", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "max"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU"}, "properties": [{"id": "custom.fillOpacity", "value": 10}, {"id": "custom.fillOpacity", "value": 50}]}, {"matcher": {"id": "byRegexp", "options": "/Request.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF9830", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "byRegexp", "options": "/Limit.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}, "id": 12, "interval": "1m", "links": [], "maxDataPoints": 250, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "disableTextWrap": false, "editorMode": "builder", "expr": "avg by(pod) (rate(container_cpu_usage_seconds_total{container=\"cert-manager-controller\"}[$__rate_interval]))", "format": "time_series", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "interval": "", "intervalFactor": 2, "legendFormat": "CPU {{pod}}", "range": true, "refId": "A", "useBackend": false}], "title": "CPU", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Memory utilisation and limits.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "max"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory"}, "properties": [{"id": "custom.fillOpacity", "value": 10}, {"id": "custom.fillOpacity", "value": 50}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Request"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF9830", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Limit"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}, "id": 16, "interval": "1m", "links": [], "maxDataPoints": 250, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "disableTextWrap": false, "editorMode": "builder", "expr": "avg by(pod) (container_memory_usage_bytes{container=\"cert-manager-controller\"})", "format": "time_series", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "interval": "", "intervalFactor": 1, "legendFormat": "Memory {{pod}}", "range": true, "refId": "A", "useBackend": false}], "title": "Memory", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "description": "Network ingress/egress.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "max"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "transmit"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}, "id": 18, "interval": "1m", "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "avg(\n  sum without (interface) (\n    rate(container_network_receive_bytes_total{namespace=\"cert-manager\"}[$__rate_interval])\n  )\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "receive", "refId": "A"}, {"datasource": {"uid": "$datasource"}, "expr": "avg(\n  sum without (interface) (\n    rate(container_network_transmit_bytes_total{namespace=\"cert-manager\"}[$__rate_interval])\n  )\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "transmit", "refId": "B"}], "title": "Network", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "description": "Rate of requests to ACME provider.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 6, "interval": "20s", "maxDataPoints": 250, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum by (method, path, status) (\n  rate(certmanager_http_acme_client_request_count[$__rate_interval])\n)", "interval": "", "legendFormat": "{{method}} {{path}} {{status}}", "refId": "A"}], "title": "ACME HTTP Requests/sec", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "description": "Average duration of requests to ACME provider. ", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 10, "interval": "30s", "maxDataPoints": 250, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.2", "targets": [{"datasource": {"uid": "$datasource"}, "expr": "sum by (method, path, status) (rate(certmanager_http_acme_client_request_duration_seconds_sum[$__rate_interval]))\n/\nsum by (method, path, status) (rate(certmanager_http_acme_client_request_duration_seconds_count[$__rate_interval]))", "interval": "", "legendFormat": "{{method}} {{path}} {{status}}", "refId": "A"}], "title": "ACME HTTP Request avg duration", "type": "timeseries"}], "refresh": "1m", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "prometheus"}, "hide": 0, "includeAll": false, "label": "Data Source", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Cert Manager", "uid": "d7e34afe-ca2f-451c-b536-15e96e545a70", "version": 2, "weekStart": ""}