{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3958, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 7, "panels": [], "repeat": "database", "title": "$database", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 1}, "id": 4, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.5.2", "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "cpu_percent", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Current CPU percentage", "type": "gauge"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 1}, "id": 5, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.5.2", "targets": [{"azureMonitor": {"aggregation": "Average", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "disk_iops_consumed_percentage", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Current data IO percentage", "type": "gauge"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.5.2", "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "active_connections", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Current connections count", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 0, "y": 7}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "cpu_percent", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "CPU percentage", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 8, "y": 7}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"azureMonitor": {"aggregation": "Average", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "disk_iops_consumed_percentage", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Data IO percentage", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 16, "y": 7}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.2", "targets": [{"azureMonitor": {"aggregation": "Average", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "active_connections", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Connections count", "type": "timeseries"}], "preload": false, "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "app-dev", "value": "974be8a6-83ee-4087-b47d-7cd8424ba8e5"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "name": "subscription", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Subscriptions", "refId": "A"}, "refresh": 1, "regex": "/^(?!5d37e76e-270f-4dc0-939f-d667a5fb1f57).*/", "type": "query"}, {"allowCustomValue": false, "current": {"text": "rg-indiebi-postgresql-server-dev", "value": "rg-indiebi-postgresql-server-dev"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "includeAll": false, "name": "resource_group", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Resource Groups", "refId": "A", "subscription": "$subscription"}, "refresh": 1, "regex": "/^rg-.*-postgresql-server-.*/", "type": "query"}, {"current": {"text": "microsoft.dbforpostgresql/flexibleservers", "value": "microsoft.dbforpostgresql/flexibleservers"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "name": "namespace", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Namespaces", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "/microsoft.dbforpostgresql\\/flexibleservers/", "type": "query"}, {"allowCustomValue": false, "current": {"text": ["psql-indiebi-postgresql-server-dev"], "value": ["psql-indiebi-postgresql-server-dev"]}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "includeAll": false, "multi": true, "name": "database", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "namespace": "$namespace", "queryType": "Azure Resource Names", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "/.*(?<!master)$/", "sort": 1, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Databases (Postgres)", "uid": "23c69472-d252-46b4-9a0f-7cc0da23b7e6", "version": 2, "weekStart": ""}