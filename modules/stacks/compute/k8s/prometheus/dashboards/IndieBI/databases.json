{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 7, "panels": [], "repeat": "database", "repeatDirection": "h", "title": "$database", "type": "row"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 1}, "id": 4, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.4.1", "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "cpu_percent", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Current CPU percentage", "type": "gauge"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 1}, "id": 5, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.4.1", "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "physical_data_read_percent", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Current data IO percentage", "type": "gauge"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.1", "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "sessions_count", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Current session count", "type": "stat"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 0, "y": 7}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "cpu_percent", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "CPU percentage", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 8, "y": 7}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "physical_data_read_percent", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Data IO percentage", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 16, "y": 7}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Maximum", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "sessions_count", "metricNamespace": "$namespace", "resources": [{"metricNamespace": "$namespace", "resourceGroup": "$resource_group", "resourceName": "$database", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Session count", "type": "timeseries"}], "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "app-dev", "value": "974be8a6-83ee-4087-b47d-7cd8424ba8e5"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "multi": false, "name": "subscription", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Subscriptions", "refId": "A"}, "refresh": 1, "regex": "/^(?!5d37e76e-270f-4dc0-939f-d667a5fb1f57).*/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "rg-indiebi-db-server-dev", "value": "rg-indiebi-db-server-dev"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "multi": false, "name": "resource_group", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Resource Groups", "refId": "A", "subscription": "$subscription"}, "refresh": 1, "regex": "/^rg-indiebi-db-server-.*/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "microsoft.sql/servers/databases", "value": "microsoft.sql/servers/databases"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "multi": false, "name": "namespace", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Namespaces", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "/.*databases/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["sql-indiebi-db-server-dev/db-dataset-manager"], "value": ["sql-indiebi-db-server-dev/db-dataset-manager"]}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 0, "includeAll": false, "multi": true, "name": "database", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "namespace": "$namespace", "queryType": "Azure Resource Names", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "/.*(?<!master)$/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Databases", "uid": "cef225ac-3623-48aa-93f6-f1c647f1f63a", "version": 5, "weekStart": ""}