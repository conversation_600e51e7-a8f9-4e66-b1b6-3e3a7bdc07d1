{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 3, "panels": [], "repeat": "service", "repeatDirection": "h", "title": "$service", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 0, "y": 1}, "id": 5, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "time() - kube_pod_created{namespace=\"$namespace\", pod=~\"$service-deployment-.*\"}", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 5, "y": 1}, "id": 21, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_pod_container_status_running{namespace=\"$namespace\", pod=~\"$service-deployment-.*\"}", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Running", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "yellow", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 1}, "id": 22, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(container_memory_working_set_bytes{namespace=\"$namespace\", pod=~\"$service.*\", container!=\"\", image!=\"\"}) by (container)", "instant": true, "legendFormat": "Usage", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(cluster:namespace:pod_memory:active:kube_pod_container_resource_requests{namespace=\"$namespace\", pod=~\"$service.*\", container!=\"\"}) by (container)", "hide": false, "instant": true, "legendFormat": "Requests", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(cluster:namespace:pod_memory:active:kube_pod_container_resource_limits{namespace=\"$namespace\", pod=~\"$service.*\", container!=\"\"}) by (container)", "hide": false, "instant": true, "legendFormat": "Limits", "range": false, "refId": "C"}], "title": "Current memory usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 1}, "id": 23, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", pod=~\"$service.*\", container!=\"\"}) by (container)", "instant": true, "legendFormat": "Usage", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_requests{namespace=\"$namespace\", pod=~\"$service.*\", container!=\"\"}) by (container)", "hide": false, "instant": true, "legendFormat": "Requests", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_limits{namespace=\"$namespace\", pod=~\"$service.*\", container!=\"\"}) by (container)", "hide": false, "instant": true, "legendFormat": "Limits", "range": false, "refId": "C"}], "title": "Current CPU usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/Memory limits.*/"}, "properties": [{"id": "custom.lineWidth", "value": 3}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}]}, {"matcher": {"id": "byRegexp", "options": "/Memory requests.*/"}, "properties": [{"id": "custom.lineWidth", "value": 3}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "color", "value": {"fixedColor": "dark-orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 14, "w": 12, "x": 0, "y": 7}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(\n    container_memory_working_set_bytes{namespace=\"$namespace\", container=~\"$service\"}\n  * on(namespace, pod)\n    group_left(workload) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"}\n) by (pod)\n", "instant": false, "legendFormat": "Memory usage {{pod}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "kube_pod_container_resource_limits{job=\"kube-state-metrics\", namespace=\"$namespace\", container=~\"$service\", resource=\"memory\"}", "hide": false, "instant": false, "legendFormat": "Memory limits {{pod}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", container=~\"$service\", resource=\"memory\"}", "hide": false, "instant": false, "legendFormat": "Memory requests {{pod}}", "range": true, "refId": "C"}], "title": "Saturation (memory)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/CPU limits.*/"}, "properties": [{"id": "custom.lineWidth", "value": 3}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}]}, {"matcher": {"id": "byRegexp", "options": "/CPU requests.*/"}, "properties": [{"id": "custom.lineWidth", "value": 3}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "color", "value": {"fixedColor": "dark-orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 14, "w": 12, "x": 12, "y": 7}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", container=~\"$service\", container!=\"\"}) by (pod)", "instant": false, "legendFormat": "CPU usage {{pod}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "kube_pod_container_resource_limits{job=\"kube-state-metrics\", namespace=\"$namespace\", container=~\"$service\", resource=\"cpu\"}", "hide": false, "instant": false, "legendFormat": "CPU limits {{pod}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", container=~\"$service\", resource=\"cpu\"}", "hide": false, "instant": false, "legendFormat": "CPU requests {{pod}}", "range": true, "refId": "C"}], "title": "Saturation (CPU)", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Terminations"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Restarts"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 21}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "sum by (container) (changes(kube_pod_container_status_terminated{container=~\"$service\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Terminations", "range": true, "refId": "Terminations", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by(container) (increase(kube_pod_container_status_restarts_total{container=~\"$service\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": false, "legendFormat": "Restarts", "range": true, "refId": "Restarts", "useBackend": false}], "title": "Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 21}, "id": 26, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum without (endpoint, instance, job, namespace, service, uid) (kube_pod_container_status_last_terminated_reason{pod=~\"$service-deployment-.*\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Last terminated reasons", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true}, "includeByName": {}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 13, "w": 12, "x": 0, "y": 29}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(\n    irate(container_network_receive_bytes_total{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\"}[$__rate_interval])\n    * on (namespace, pod)\n    group_left(workload) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$service-deployment.*\"}\n) by (pod)\n", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Receive bandwith", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 13, "w": 12, "x": 12, "y": 29}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(\n    irate(container_network_transmit_bytes_total{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\"}[$__rate_interval])\n    * on (namespace, pod)\n    group_left(workload) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$service-deployment.*\"}\n) by (pod)\n", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Transmit bandwith", "type": "timeseries"}], "refresh": "1m", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "cpt", "value": "cpt"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(namespace)", "hide": 0, "includeAll": false, "multi": false, "name": "namespace", "options": [], "query": {"qryType": 1, "query": "label_values(namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/cpt|dpt|saas/", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": ["backoffice"], "value": ["backoffice"]}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values({namespace=\"$namespace\"},container)", "hide": 0, "includeAll": false, "multi": true, "name": "service", "options": [], "query": {"qryType": 1, "query": "label_values({namespace=\"$namespace\"},container)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/.*(?<!-before-deployment|kube-state-metrics|-job)$/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Services", "uid": "b62a19fd-32b0-47fa-9a3b-b9220c3854b0", "version": 5, "weekStart": ""}