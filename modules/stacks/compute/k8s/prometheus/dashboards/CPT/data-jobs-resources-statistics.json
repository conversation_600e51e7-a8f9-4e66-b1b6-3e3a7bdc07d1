{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3087, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Average"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Median"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": "p95"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "p100"}]}]}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 0, "showHeader": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (container) (avg_over_time(container_memory_usage_bytes{pod=~\"${data_job}.*\", container!=\"\"}[1d]))", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (container) (quantile(0.5, max_over_time(container_memory_usage_bytes{pod=~\"${data_job}.*\", container!=\"\"}[1d])))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (container) (quantile(0.95, max_over_time(container_memory_usage_bytes{pod=~\"${data_job}.*\", container!=\"\"}[1d])))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (container) (quantile(1, max_over_time(container_memory_usage_bytes{pod=~\"${data_job}.*\", container!=\"\"}[1d])))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "E"}], "title": "Memory usage statistics", "transformations": [{"id": "merge", "options": {}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Average"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Median"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": "p95"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "p100"}]}]}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 4}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 0, "showHeader": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "avg by (container) (irate(container_cpu_usage_seconds_total{pod=~\"${data_job}.*\", container!=\"\"}[1d]))", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (container) (quantile(0.5, irate(container_cpu_usage_seconds_total{pod=~\"${data_job}.*\", container!=\"\"}[1d])))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (container) (quantile(0.95, irate(container_cpu_usage_seconds_total{pod=~\"${data_job}.*\", container!=\"\"}[1d])))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max by (container) (quantile(1, irate(container_cpu_usage_seconds_total{pod=~\"${data_job}.*\", container!=\"\"}[1d])))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "E"}], "title": "CPU usage statistics", "transformations": [{"id": "merge", "options": {}}], "type": "table"}], "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": true, "text": "core-silver-job", "value": "core-silver-job"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values({namespace=\"dpt\"},container)", "hide": 0, "includeAll": false, "multi": false, "name": "data_job", "options": [], "query": {"qryType": 1, "query": "label_values({namespace=\"dpt\"},container)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/^(?!.*-heavy).*-job.*$/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Data jobs resources statistics (excluding heavy)", "uid": "752dcab7-38ab-4dd9-9293-c94e4e23d3df", "version": 1, "weekStart": ""}