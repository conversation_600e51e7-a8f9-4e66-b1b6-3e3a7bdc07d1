{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3094, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "Messages that landed in dead letter topic and couldn't be handled", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 0}, "id": 1, "links": [{"targetBlank": true, "title": "Service Bus Explorer", "url": "https://portal.azure.com/#@indiebi.com/resource/subscriptions/${subscription}/resourceGroups/${resource_group}/providers/Microsoft.ServiceBus/namespaces/${resource}/topics/data-jobs-deadletter-topic/explorer"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Average", "alias": "", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "EntityName", "filters": ["data-jobs-deadletter-topic"], "operator": "eq"}], "metricName": "DeadletteredMessages", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "A", "subscription": "$subscription"}], "title": "Dead-lettered messages (data-jobs-deadletter)", "type": "timeseries"}, {"datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "description": "Message metrics for the data-jobs topic", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 11}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"azureMonitor": {"aggregation": "Maximum", "alias": "Message count", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [{"dimension": "EntityName", "filters": ["data-jobs-topic"], "operator": "eq"}], "metricName": "Messages", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto", "top": ""}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "queryType": "Azure Monitor", "refId": "Message count", "subscription": "$subscription"}, {"azureMonitor": {"aggregation": "Total", "alias": "Completed messages", "allowedTimeGrainsMs": [60000, 300000, 900000, 1800000, 3600000, 21600000, 43200000, 86400000], "dimensionFilters": [], "metricName": "CompleteMessage", "metricNamespace": "$namespace", "region": "", "resources": [{"metricNamespace": "$namespace", "region": "", "resourceGroup": "$resource_group", "resourceName": "$resource", "subscription": "$subscription"}], "timeGrain": "auto", "top": ""}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "hide": false, "queryType": "Azure Monitor", "refId": "Completed messages", "subscription": "$subscription"}], "title": "Message statistics (data-jobs)", "type": "timeseries"}], "refresh": "", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "app-dev", "value": "974be8a6-83ee-4087-b47d-7cd8424ba8e5"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "multi": false, "name": "subscription", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Subscriptions", "refId": "A"}, "refresh": 1, "regex": "/^(?!5d37e76e-270f-4dc0-939f-d667a5fb1f57).*/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "rg-pipeline-manager-dev", "value": "rg-pipeline-manager-dev"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "multi": false, "name": "resource_group", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Resource Groups", "refId": "A", "subscription": "$subscription"}, "refresh": 1, "regex": "/^rg-pipeline-manager-.*/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "microsoft.servicebus/namespaces", "value": "microsoft.servicebus/namespaces"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "multi": false, "name": "namespace", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "queryType": "Azure Namespaces", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "/microsoft.servicebus\\/namespaces/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "sb-data-pipeline-dev", "value": "sb-data-pipeline-dev"}, "datasource": {"type": "grafana-azure-monitor-datasource", "uid": "azure-monitor"}, "definition": "", "hide": 2, "includeAll": false, "multi": false, "name": "resource", "options": [], "query": {"azureLogAnalytics": {"query": "", "resources": []}, "namespace": "$namespace", "queryType": "Azure Resource Names", "refId": "A", "resourceGroup": "$resource_group", "subscription": "$subscription"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Data pipeline Service Bus", "uid": "c1956377-06a0-471b-ae21-************", "version": 2, "weekStart": ""}