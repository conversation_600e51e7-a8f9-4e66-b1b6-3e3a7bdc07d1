terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}


locals {
  kubernetes_namespace = "prometheus"
  grafana_name         = "prometheus-grafana"
}

resource "azurerm_user_assigned_identity" "grafana_identity" {
  resource_group_name = var.resource_group
  location            = var.common.location
  name                = "id-${local.grafana_name}-${var.environment.name}"
  tags                = var.tags
}

resource "azurerm_role_assignment" "servicebus_role_assignment" {
  scope                = "/subscriptions/${var.environment.subscription_id}" # TODO: should it be the whole sub?
  role_definition_name = "Monitoring Reader"
  principal_id         = azurerm_user_assigned_identity.grafana_identity.principal_id
}

resource "azurerm_federated_identity_credential" "federated_identity_credential_grafana" {
  name                = "fic-${local.grafana_name}-${var.environment.name}"
  resource_group_name = var.resource_group
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.grafana_identity.id
  subject             = "system:serviceaccount:${local.kubernetes_namespace}:${local.grafana_name}"
}


resource "random_uuid" "grafana_admin_role_id" {}
resource "random_uuid" "grafana_viewer_role_id" {}
resource "random_uuid" "grafana_editor_role_id" {}

resource "azuread_application" "grafana_app_registration" {
  display_name            = "IndieBI Grafana (${var.environment.name})"
  group_membership_claims = ["ApplicationGroup", "SecurityGroup"]

  feature_tags {
    enterprise            = true
    gallery               = false
    custom_single_sign_on = true
  }

  optional_claims {
    access_token {
      name      = "groups"
      essential = true
    }

    id_token {
      name      = "groups"
      essential = true
    }
  }

  web {
    redirect_uris = ["https://grafana.${var.environment.domain}/login/azuread", "https://grafana.${var.environment.domain}/"]
  }

  required_resource_access {
    resource_app_id = "********-0000-0000-c000-************" # Microsoft Graph

    resource_access {
      id   = "e1fe6dd8-ba31-4d61-89e7-88639da4683d" # User.Read
      type = "Scope"
    }
  }

  app_role {
    allowed_member_types = ["User"]
    description          = "Grafana org admin Users"
    display_name         = "Grafana Org Admin"
    id                   = random_uuid.grafana_admin_role_id.result
    enabled              = true
    value                = "Admin"
  }

  app_role {
    allowed_member_types = ["User"]
    description          = "Grafana read only Users"
    display_name         = "Grafana Viewer"
    id                   = random_uuid.grafana_viewer_role_id.result
    enabled              = true
    value                = "Viewer"
  }

  app_role {
    allowed_member_types = ["User"]
    description          = "Grafana Editor Users"
    display_name         = "Grafana Editor"
    id                   = random_uuid.grafana_editor_role_id.result
    enabled              = true
    value                = "Editor"
  }
}

resource "azuread_service_principal" "grafana" {
  client_id                     = azuread_application.grafana_app_registration.client_id
  preferred_single_sign_on_mode = "oidc"
  login_url                     = "https://grafana.${var.environment.domain}/login/azuread"
  feature_tags {
    custom_single_sign_on = true
    # set to true so tf doesn't try to set it to null with every plan/apply 
    enterprise = true
  }
}

resource "azuread_app_role_assignment" "admin_role_assignment" {
  for_each            = var.configuration.grafana.admins
  app_role_id         = random_uuid.grafana_admin_role_id.result
  principal_object_id = each.value
  resource_object_id  = azuread_service_principal.grafana.object_id

  lifecycle {
    create_before_destroy = true
  }
}

resource "azuread_app_role_assignment" "viewer_role_assignment" {
  for_each            = var.configuration.grafana.viewers
  app_role_id         = random_uuid.grafana_viewer_role_id.result
  principal_object_id = each.value
  resource_object_id  = azuread_service_principal.grafana.object_id

  lifecycle {
    create_before_destroy = true
  }
}

resource "azuread_app_role_assignment" "editor_role_assignment" {
  for_each            = var.configuration.grafana.editors
  app_role_id         = random_uuid.grafana_editor_role_id.result
  principal_object_id = each.value
  resource_object_id  = azuread_service_principal.grafana.object_id

  lifecycle {
    create_before_destroy = true
  }
}

resource "azuread_application_password" "grafana_app_password" {
  application_id = azuread_application.grafana_app_registration.id
  display_name   = "Grafana SSO"
  end_date       = var.common.keys_secrets_expiration_date
}

resource "azurerm_web_application_firewall_policy" "grafana_waf_policy" {
  name                = "waf-policy-grafana-${var.environment.name}"
  resource_group_name = var.resource_group
  location            = var.common.location
  tags                = var.tags
  policy_settings {
    enabled                     = true
    mode                        = "Detection"
    request_body_check          = true
    file_upload_limit_in_mb     = 50
    max_request_body_size_in_kb = 1024
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }
}

resource "random_password" "grafana_admin_password" {
  length  = 128
  special = false
}

resource "helm_release" "prometheus" {
  name = "prometheus"

  repository       = "https://prometheus-community.github.io/helm-charts"
  chart            = "kube-prometheus-stack"
  namespace        = local.kubernetes_namespace
  create_namespace = true
  version          = "75.15.1"

  set_sensitive {
    name  = "grafana.grafana\\.ini.auth\\.azuread.client_secret"
    value = azuread_application_password.grafana_app_password.value
  }

  set_sensitive {
    name  = "grafana.adminPassword"
    value = random_password.grafana_admin_password.result
  }

  values = [
    templatefile("${path.module}/prometheus-overrides.yaml.tpl",
      {
        storage_size               = var.configuration.storage_size
        retention                  = var.configuration.retention
        retention_size             = var.configuration.retention_size
        grafana_identity_client_id = azurerm_user_assigned_identity.grafana_identity.client_id
        tenant_id                  = var.common.tenant_id
        domain_name                = var.environment.domain
        subscription_id            = var.environment.subscription_id
        grafana_app_client_id      = azuread_application.grafana_app_registration.client_id
        grafana_image_tag          = var.configuration.grafana.image.tag
        allowed_groups             = join(" ", var.configuration.grafana.admins, var.configuration.grafana.editors, var.configuration.grafana.viewers)
        waf_policy_id              = azurerm_web_application_firewall_policy.grafana_waf_policy.id
      }
    )
  ]

  timeout = 600
  lifecycle {
    create_before_destroy = true
    # avoids leaking sensitive data through metadata field
    # see: https://github.com/hashicorp/terraform-provider-helm/issues/1004
    # this gives a nasty warning though...
    ignore_changes = [metadata]
  }

}

resource "random_password" "grafana_sql_user_password" {
  length  = 128
  special = false
}

# all the values in this block assume certain convention
# if the convention changes values need to be updated
locals {
  database_names = [
    "db-dataset-manager",
    "db-pipeline-manager",
    "db-report-service",
    "db-user-service-v2"
  ]
  sql_server_host = "sql-indiebi-db-server-${var.environment.name}.database.windows.net"
}

resource "helm_release" "sql_server_datasources" {
  name        = "sql-server-datasources"
  chart       = "../components/kubernetes-resource"
  namespace   = "prometheus"
  max_history = 5

  values = [
    <<-EOF
      configurations:
        - ${indent(4, templatefile("${path.module}/datasources/sql-server-datasources.yaml.tpl",
    {
      databases        = local.database_names
      sql_server_host  = local.sql_server_host
      grafana_password = random_password.grafana_sql_user_password.result
    }
))}
      EOF
]

lifecycle {
  create_before_destroy = true
  # needed so secrets don't get leaked in the metadata
  ignore_changes = [metadata]
}

depends_on = [helm_release.prometheus]

}

resource "helm_release" "grafana_contact_points" {
  name        = "grafana-contact-points"
  chart       = "../components/kubernetes-resource"
  namespace   = "prometheus"
  max_history = 5

  values = [
    <<-EOF
      configurations:
        - ${indent(4, templatefile("${path.module}/alerting/grafana-contact-points.yaml.tpl",
    {
      alerting = var.configuration.grafana.alerting
    }
))}
      EOF
]

lifecycle {
  create_before_destroy = true
  # needed so secrets don't get leaked in the metadata
  ignore_changes = [metadata]
}

depends_on = [helm_release.prometheus]

}

module "dns_entry" {
  source                       = "../../../../components/dns_entry"
  name                         = "grafana${var.environment.dns_extension != null ? var.environment.dns_extension : ""}"
  dns_zone_resource_group_name = var.top_dns_zone.resource_group_name
  dns_zone_name                = var.top_dns_zone.name
  ip                           = var.ip_address

  providers = {
    azurerm.connectivity = azurerm.connectivity
  }
}

# Dashboards

locals {
  dashboards_root = "${path.module}/dashboards"
  # list all JSON files from subdirectories of `dashboards_root`
  # generate a map in the form of: {"subdirectory": [{"dashboard1.json": "contents"}, {"dashboard2.json": "contents"}]}
  dashboards = {
    for json_file in fileset(local.dashboards_root, "**/*.json") :
    dirname(trimprefix(json_file, "${local.dashboards_root}/")) => {
      basename(json_file) = file("${local.dashboards_root}/${json_file}")
    }...
  }
}

resource "helm_release" "grafana_dashboards" {
  for_each    = local.dashboards
  name        = "grafana-dashboards-${lower(each.key)}"
  chart       = "../components/kubernetes-resource"
  namespace   = "prometheus"
  max_history = 1

  values = [
    <<-EOF
      configurations:
        - ${indent(4, templatefile("${path.module}/dashboards/dashboards-config-map.yaml.tpl",
    {
      name           = "grafana-dashboards-${lower(each.key)}"
      grafana_folder = each.key
      # template expects a map, value is a tuple of maps, it needs to be merged into a single map
      dashboards = merge(each.value...)
    }
))}
      EOF
]

lifecycle {
  create_before_destroy = true
}
}
