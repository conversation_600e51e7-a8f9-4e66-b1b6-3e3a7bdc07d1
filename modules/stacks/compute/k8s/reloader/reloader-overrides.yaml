reloader:
  reloadStrategy: annotations
  reloadOnCreate: true
  deployment:
    containerSecurityContext:
      capabilities:
        drop:
          - ALL
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
    priorityClassName: normal-priority
    resources:
      requests:
        cpu: 50m
        memory: 110Mi
      limits:
        cpu: 100m
        memory: 300Mi
  podMonitor:
    enabled: true
  