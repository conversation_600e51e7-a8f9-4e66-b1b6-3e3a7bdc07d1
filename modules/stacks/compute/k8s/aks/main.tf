resource "azurerm_user_assigned_identity" "aks_identity" {
  // we're using an explicitly created User Assigned identity so that
  // we can add additional role assignments to the cluster as needed
  name                = "id-${var.workload_name}-${var.environment.name}"
  resource_group_name = var.resource_group
  location            = var.common.location
  tags                = var.tags
}

resource "azurerm_role_assignment" "aks_network_role" {
  role_definition_name = "Network Contributor"
  scope                = var.network_resource_group_id
  principal_id         = azurerm_user_assigned_identity.aks_identity.principal_id

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_kubernetes_cluster" "aks" {
  #checkov:skip=CKV_AZURE_4: "Ensure Azure AKS cluster monitoring is enabled" - we don't use Azure Monitor to monitor our clusters
  #checkov:skip=CKV_AZURE_115: "Ensure that AKS enables private clusters" - requires GitLab runner in Azure
  #checkov:skip=CKV_AZURE_117: "Ensure that AKS uses disk encryption set" - requires cluster recreation
  #checkov:skip=CKV_AZURE_141: "Ensure Azure Kubernetes Service (AKS) local admin account is disabled" - we need this for kubernetes provider, see PLT-5173
  #checkov:skip=CKV_AZURE_172: "Ensure autorotation of Secrets Store CSI Driver secrets for AKS clusters" - we do not use secrets store CSI driver
  #checkov:skip=CKV_AZURE_226: "Ensure ephemeral disks are used for OS disks" - requires VMs of certain size, which would incur extra cost
  #checkov:skip=CKV_AZURE_227: "Ensure that the AKS cluster encrypt temp disks, caches, and data flows between Compute and Storage resources" - requires feature enabled at subscription level  
  #checkov:skip=CKV_AZURE_232: "Ensure that only critical system pods run on system nodes" - we're using our own deployment scheme for pods
  name                = "aks-${var.workload_name}-${var.environment.name}"
  location            = var.common.location
  resource_group_name = var.resource_group
  sku_tier            = var.configuration.sku_tier
  dns_prefix          = "aks-${var.workload_name}-${var.environment.name}"

  node_resource_group = "rg-k8s-node-${var.environment.name}"

  automatic_upgrade_channel = "stable"
  // We aim at avoiding maintenance windows during work hours in US and Europe
  maintenance_window_auto_upgrade {
    frequency   = "Weekly"
    interval    = 1
    duration    = 6
    start_time  = "01:00"
    day_of_week = "Monday"
    utc_offset  = "+00:00"
  }

  node_os_upgrade_channel = "NodeImage"
  // We aim at avoiding maintenance windows during work hours in US and Europe
  maintenance_window_node_os {
    frequency  = "Daily"
    interval   = 1
    duration   = 6
    start_time = "01:00"
    utc_offset = "+00:00"
  }

  oidc_issuer_enabled       = true
  workload_identity_enabled = true
  tags                      = var.tags

  default_node_pool {
    name      = "default"
    vm_size   = var.configuration.default_node_pool.vm_size
    min_count = var.configuration.default_node_pool.min_count
    max_count = var.configuration.default_node_pool.max_count

    // Note:
    // The address space required for the cluster depends on the number of nodes in the cluster and the number of
    // pods per node. Also be aware that AKS runs its own pods by default (for operation, monitoring, logging).
    // A '/27' network is too small for Kubernetes.
    max_pods = 50

    vnet_subnet_id              = var.subnet_config.id
    auto_scaling_enabled        = true
    temporary_name_for_rotation = "temp"

    upgrade_settings {
      drain_timeout_in_minutes      = 0
      max_surge                     = "10%"
      node_soak_duration_in_minutes = 0
    }
  }

  network_profile {
    network_plugin = "azure"
    dns_service_ip = "************"
    service_cidr   = "***********/16"
    // assign IPs to pods from the subnet using Azure CNI
    network_policy = "azure"
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.aks_identity.id]
  }

  http_application_routing_enabled = false

  // Note:
  // Important Portal vs CLI difference:
  // - CLI - RBAC is enabled by default (must pass --disable-rbac to disable)
  // - Portal - RBAC is disabled by default
  role_based_access_control_enabled = true

  # TODO: once we will migrate this to cicd local_account_disabled should be set to true. This will cause problems when authenticating to aks to create k8s namespaces and heml charts
  # To solve this problem we will have to use kubelogin get-token in kubernetes and helm providers. For more info how to do it have a look here:
  # https://github.com/hashicorp/terraform-provider-azurerm/issues/17182
  # https://discuss.hashicorp.com/t/aad-integrated-kubernetes-cluster-no-longer-able-to-use-kubernetes-provider-following-disabling-local-admin-account/33439
  # https://azure.github.io/kubelogin/cli/get-token.html 
  # local_account_disabled = true
  azure_active_directory_role_based_access_control {
    azure_rbac_enabled = true
    tenant_id          = var.common.tenant_id
  }

  api_server_access_profile {
    authorized_ip_ranges = ["${var.common.firewall_public_ip_address}/32"]
  }

  # turned off for now to allow for vault purging
  # disk_encryption_set_id = azurerm_disk_encryption_set.des.id
  # pushed till networking config phase
  # private_cluster_enabled = true
  azure_policy_enabled = true

  lifecycle {
    ignore_changes = [
      default_node_pool[0].node_count,
      microsoft_defender
    ]
  }

  ingress_application_gateway {
    gateway_id = var.application_gateway.id
  }
}

resource "azurerm_role_assignment" "gateway_reader_role" {
  principal_id         = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
  role_definition_name = "Reader"
  scope                = var.network_resource_group_id
}

resource "azurerm_role_assignment" "gateway_contributor_role" {
  principal_id         = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
  role_definition_name = "Contributor"
  scope                = var.network_resource_group_id
}

resource "azurerm_role_assignment" "join_subnet_role" {
  principal_id         = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
  role_definition_name = "Network Contributor"
  scope                = var.application_gateway.subnet_id
}

resource "azurerm_role_assignment" "network_contributor_role_sub" {
  principal_id         = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
  role_definition_name = "Network Contributor"
  scope                = "/subscriptions/${var.environment.subscription_id}"
}

resource "azurerm_kubernetes_cluster_node_pool" "data_jobs_node_pool" {
  #checkov:skip=CKV_AZURE_168: "Ensure Azure Kubernetes Cluster (AKS) nodes should use a minimum number of 50 pods" - false positive, see: https://github.com/bridgecrewio/checkov/issues/4290
  #checkov:skip=CKV_AZURE_227: "Ensure that the AKS cluster encrypt temp disks, caches, and data flows between Compute and Storage resources" - requires feature enabled at subscription level
  name                  = "datajobs"
  vnet_subnet_id        = var.subnet_config.id
  kubernetes_cluster_id = azurerm_kubernetes_cluster.aks.id
  auto_scaling_enabled  = true
  node_count            = 1
  vm_size               = var.configuration.data_jobs_node_pool.vm_size
  min_count             = var.configuration.data_jobs_node_pool.min_count
  max_count             = var.configuration.data_jobs_node_pool.max_count
  max_pods              = var.configuration.data_jobs_node_pool.max_pods
  node_taints           = ["dedicated=datajobs:NoSchedule"]
  node_labels           = { "dedicated" = "datajobs" }

  upgrade_settings {
    drain_timeout_in_minutes      = 0
    max_surge                     = "10%"
    node_soak_duration_in_minutes = 0
  }

  lifecycle {
    prevent_destroy = true
    ignore_changes  = [node_count]
  }
}

resource "azurerm_kubernetes_cluster_node_pool" "web_apps_node_pool" {
  #checkov:skip=CKV_AZURE_168: "Ensure Azure Kubernetes Cluster (AKS) nodes should use a minimum number of 50 pods" - false positive, see: https://github.com/bridgecrewio/checkov/issues/4290
  #checkov:skip=CKV_AZURE_227: "Ensure that the AKS cluster encrypt temp disks, caches, and data flows between Compute and Storage resources" - requires feature enabled at subscription level
  name                  = "webapps"
  vnet_subnet_id        = var.subnet_config.id
  kubernetes_cluster_id = azurerm_kubernetes_cluster.aks.id
  auto_scaling_enabled  = true
  node_count            = 1
  vm_size               = var.configuration.web_apps_node_pool.vm_size
  min_count             = var.configuration.web_apps_node_pool.min_count
  max_count             = var.configuration.web_apps_node_pool.max_count
  node_taints           = ["dedicated=webapps:NoSchedule"]
  node_labels           = { "dedicated" = "webapps" }

  upgrade_settings {
    drain_timeout_in_minutes      = 0
    max_surge                     = "10%"
    node_soak_duration_in_minutes = 0
  }

  lifecycle {
    prevent_destroy = true
    ignore_changes  = [node_count]
  }
}

resource "azurerm_role_assignment" "cluster_user" {
  for_each = toset([for admin in var.configuration.namespace_admins : admin.principal_id])

  scope                = azurerm_kubernetes_cluster.aks.id
  role_definition_name = "Azure Kubernetes Service Cluster User Role"
  principal_id         = each.value
}


resource "azurerm_role_assignment" "ns_rbac_admin" {
  for_each = { for index, value in var.configuration.namespace_admins : index => value }

  scope                = "${azurerm_kubernetes_cluster.aks.id}/namespaces/${each.value["namespace"]}"
  role_definition_name = "Azure Kubernetes Service RBAC Admin"
  principal_id         = each.value["principal_id"]
}

resource "azurerm_role_assignment" "cluster_admin" {
  for_each = toset(var.infra_admins)

  scope                = azurerm_kubernetes_cluster.aks.id
  role_definition_name = "Azure Kubernetes Service RBAC Cluster Admin"
  principal_id         = each.value
}

resource "helm_release" "priority_classes" {
  name        = "priority-classes"
  chart       = "../components/kubernetes-resource"
  namespace   = "kube-public"
  max_history = 1

  values = [
    <<-EOF
      configurations:
        - ${indent(4, file("${path.module}/priority_classes/high.yaml"))}
        - ${indent(4, file("${path.module}/priority_classes/normal.yaml"))}
        - ${indent(4, file("${path.module}/priority_classes/low.yaml"))}
      EOF
  ]
}

resource "azurerm_role_assignment" "acr_pull" {
  role_definition_name = "AcrPull"
  scope                = var.common.acr_resource_id
  principal_id         = azurerm_kubernetes_cluster.aks.kubelet_identity[0].object_id

  lifecycle {
    create_before_destroy = true
  }
}
