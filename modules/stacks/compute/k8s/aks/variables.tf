variable "workload_name" {
  type    = string
  default = "main-cluster"
}

variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "configuration" {
  type = any
}

variable "infra_admins" {
  type = list(any)
}

variable "application_gateway" {
  type = map(string)
}

variable "network_resource_group_id" {
  type = string
}

variable "resource_group" {
  type = string
}

variable "subnet_config" {
  type = map(any)
}

variable "tags" {
  type = map(string)
}
