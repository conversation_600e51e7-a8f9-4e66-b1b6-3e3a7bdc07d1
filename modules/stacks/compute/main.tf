terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

resource "azurerm_resource_group" "rg_monitoring" {
  name     = "rg-monitoring-${var.environment.name}"
  location = var.common.location
  tags     = var.tags
}

resource "azurerm_log_analytics_workspace" "env_log_workspace" {
  name                = "la-${var.environment.name}"
  location            = var.common.location
  resource_group_name = azurerm_resource_group.rg_monitoring.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
  tags                = var.tags
}

resource "azurerm_role_assignment" "log_contributor" {
  for_each = toset(var.infra_admins)

  scope                = azurerm_log_analytics_workspace.env_log_workspace.id
  role_definition_name = "Log Analytics Contributor"
  principal_id         = each.value
}

resource "azurerm_role_assignment" "log_reader" {
  for_each = toset(var.infra_readers)

  scope                = azurerm_log_analytics_workspace.env_log_workspace.id
  role_definition_name = "Log Analytics Reader"
  principal_id         = each.value
}

module "network" {
  source                     = "./network"
  common                     = var.common
  environment                = var.environment
  vnet                       = var.vnet
  infra_admins               = var.infra_admins
  private_dns_zone_ids       = var.private_dns_zone_ids
  tags                       = var.tags
  log_analytics_workspace_id = azurerm_log_analytics_workspace.env_log_workspace.id

  depends_on = [azurerm_role_assignment.log_contributor]
}


module "central_key_vault" {
  source              = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault?ref=1.1.4"
  name                = "single-click"
  environment         = var.environment.name
  location            = var.common.location
  tenant_id           = var.common.tenant_id
  readers             = toset(concat(var.infra_readers, var.infra_admins))
  subnet_id           = module.network.subnet_config.id
  private_dns_zone_id = var.private_dns_zone_ids.vault
  sku_name            = "standard"
  tags                = var.tags
}

data "azurerm_dns_zone" "public_dns_zone" {
  name     = var.top_dns_zone.name
  provider = azurerm.connectivity
}

module "k8s" {
  source                   = "./k8s"
  environment              = var.environment
  common                   = var.common
  aks_configuration        = var.aks_configuration
  infra_admins             = var.infra_admins
  argocd_configuration     = var.argocd_configuration
  prometheus_configuration = var.prometheus_configuration
  registry_configuration   = var.registry_configuration
  top_dns_zone             = var.top_dns_zone
  central_key_vault        = module.central_key_vault

  network_resource_group = module.network.resource_group
  subnet_config          = module.network.subnet_config
  application_gateway    = module.network.application_gateway
  dns_zone_id            = data.azurerm_dns_zone.public_dns_zone.id

  tags = var.tags

  depends_on = [azurerm_role_assignment.log_contributor]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
