
resource "azurerm_resource_group" "network_rg" {
  name     = "rg-${var.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = var.tags
}

# TODO: we'd like to configure Key Vault access through private endpoint
resource "azurerm_subnet" "subnet" {
  name                              = "snet-${var.workload_name}-${var.environment.name}"
  resource_group_name               = var.vnet.rg_name
  virtual_network_name              = var.vnet.name
  address_prefixes                  = [var.vnet.workload_subnet_address_space]
  service_endpoints                 = ["Microsoft.KeyVault"]
  private_endpoint_network_policies = "Enabled"

  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
  }
}

resource "azurerm_public_ip" "aks_ingress_public_ip" {
  // static public ip to be used by NGINX Ingress
  name                = "pip-${var.workload_name}-${var.environment.name}"
  resource_group_name = azurerm_resource_group.network_rg.name
  location            = var.common.location
  allocation_method   = "Static"
  sku                 = "Standard"
  zones               = ["1", "2", "3"]
  tags                = var.tags
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
  }
}

resource "azurerm_network_security_group" "nsg" {
  name                = "nsg-${var.workload_name}-${var.environment.name}"
  location            = var.common.location
  resource_group_name = var.vnet.rg_name
  tags                = var.tags
}

resource "azurerm_subnet_network_security_group_association" "subnet_nsg_assoc" {
  subnet_id                 = azurerm_subnet.subnet.id
  network_security_group_id = azurerm_network_security_group.nsg.id
}

data "azurerm_route_table" "default_route_table" {
  name                = var.vnet.default_route_table
  resource_group_name = var.vnet.rg_name
}

resource "azurerm_subnet_route_table_association" "subnet_rt_assoc" {
  subnet_id      = azurerm_subnet.subnet.id
  route_table_id = data.azurerm_route_table.default_route_table.id

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_subnet" "agw_subnet" {
  name                              = "snet-${var.workload_name}-${var.environment.name}-agw"
  resource_group_name               = var.vnet.rg_name
  virtual_network_name              = var.vnet.name
  address_prefixes                  = [var.vnet.app_gateway_subnet_address_space]
  private_endpoint_network_policies = "Enabled"

  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
  }
}

resource "azurerm_network_security_group" "nsg_agw" {
  name                = "nsg-${var.workload_name}-${var.environment.name}-agw"
  location            = var.common.location
  resource_group_name = var.vnet.rg_name

  tags = var.tags
}

resource "azurerm_subnet_network_security_group_association" "subnet_nsg_agw_assoc" {
  subnet_id                 = azurerm_subnet.agw_subnet.id
  network_security_group_id = azurerm_network_security_group.nsg_agw.id
}

resource "azurerm_subnet" "function_app_subnet" {
  name                              = "snet-${var.workload_name}-${var.environment.name}-func"
  resource_group_name               = var.vnet.rg_name
  virtual_network_name              = var.vnet.name
  private_endpoint_network_policies = "Enabled"

  address_prefixes = [var.vnet.function_app_subnet_address_space]

  delegation {
    name = "function-app-delegation"
    service_delegation {
      name = "Microsoft.Web/serverFarms"
      actions = [
        "Microsoft.Network/virtualNetworks/subnets/action",
        # event though there should exist terraform wants to read them, see ignore changes below
        "Microsoft.Network/virtualNetworks/read",
        "Microsoft.Network/virtualNetworks/subnets/join/action"
      ]
    }
  }
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
    ignore_changes        = [delegation.0.service_delegation["actions"]]
  }
}

resource "azurerm_network_security_group" "nsg_func" {
  name                = "nsg-${var.workload_name}-${var.environment.name}-func"
  location            = var.common.location
  resource_group_name = var.vnet.rg_name
  tags                = var.tags
}

resource "azurerm_subnet_network_security_group_association" "subnet_nsg_fapp_assoc" {
  subnet_id                 = azurerm_subnet.function_app_subnet.id
  network_security_group_id = azurerm_network_security_group.nsg_func.id
}

resource "azurerm_route_table" "route_table_agw" {
  name                = "rt-${var.workload_name}-${var.environment.name}-agw"
  location            = var.common.location
  resource_group_name = azurerm_resource_group.network_rg.name
  tags                = var.tags
}

resource "azurerm_route" "route_vpn_to_firewall" {
  name                   = "route-vpn-back-to-firewall"
  resource_group_name    = azurerm_resource_group.network_rg.name
  route_table_name       = azurerm_route_table.route_table_agw.name
  address_prefix         = var.common.vpn_address_space
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = var.vnet.firewall_private_ip
}

resource "azurerm_route" "route_internal_to_firewall" {
  name                   = "route-internal-back-to-firewall"
  resource_group_name    = azurerm_resource_group.network_rg.name
  route_table_name       = azurerm_route_table.route_table_agw.name
  address_prefix         = "10.0.0.0/8" # azure internal network
  next_hop_type          = "VirtualAppliance"
  next_hop_in_ip_address = var.vnet.firewall_private_ip
}

resource "azurerm_subnet_route_table_association" "agw_rt_association" {
  subnet_id      = azurerm_subnet.agw_subnet.id
  route_table_id = azurerm_route_table.route_table_agw.id
}

resource "azurerm_network_security_rule" "allow_http_rule" {
  name                         = "allow-public-http"
  priority                     = 110
  direction                    = "Inbound"
  access                       = "Allow"
  protocol                     = "Tcp"
  source_port_range            = "*"
  source_address_prefix        = "Internet"
  destination_port_range       = "443"
  destination_address_prefixes = [var.vnet.app_gateway_subnet_address_space, azurerm_public_ip.aks_ingress_public_ip.ip_address]
  resource_group_name          = var.vnet.rg_name
  network_security_group_name  = azurerm_network_security_group.nsg_agw.name
}

resource "azurerm_network_security_rule" "allow_inbound_for_agw" {
  name                        = "allow-inbound-65200-65535"
  priority                    = 100
  direction                   = "Inbound"
  access                      = "Allow"
  protocol                    = "Tcp"
  source_port_range           = "*"
  source_address_prefix       = "Internet"
  destination_port_range      = "65200-65535"
  destination_address_prefix  = "*"
  resource_group_name         = var.vnet.rg_name
  network_security_group_name = azurerm_network_security_group.nsg_agw.name
}

resource "azurerm_web_application_firewall_policy" "default_waf_policy" {
  name                = "waf-policy-${var.workload_name}-${var.environment.name}"
  resource_group_name = azurerm_resource_group.network_rg.name
  location            = var.common.location
  tags                = var.tags

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = true
    file_upload_limit_in_mb     = 50
    max_request_body_size_in_kb = 1024
  }

  managed_rules {

    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }
}

locals {
  frontend_ip_public_configuration_name  = "feip-config"
  frontend_ip_private_configuration_name = "feip-config-private"
  backend_address_pool_name              = "be-pool-aks"
  backend_http_settings_name             = "http-settings"
  http_listener_name                     = "http-listener"
  frontend_http_port_name                = "fp-80"
  frontend_https_port_name               = "fp-443"
}

resource "azurerm_application_gateway" "app_gateway" {
  #checkov:skip=CKV_AZURE_217:"Ensure Azure Application gateways listener that allow connection requests over HTTP" - We use cluster cert-manager for individual backends and we don't need additional access through HTTPS
  name                = "agw-${var.environment.name}"
  resource_group_name = azurerm_resource_group.network_rg.name
  location            = var.common.location

  global {
    request_buffering_enabled  = true
    response_buffering_enabled = var.vnet.app_gateway_response_buffering_enabled
  }

  sku {
    name = "WAF_v2"
    tier = "WAF_v2"
    # TODO later on we'd like to test autoscale_configuration instead of fixed capacity
    capacity = var.vnet.app_gateway_capacity_units
  }

  gateway_ip_configuration {
    name      = "agw-ip-config"
    subnet_id = azurerm_subnet.agw_subnet.id
  }

  frontend_port {
    name = local.frontend_http_port_name
    port = 80
  }

  frontend_port {
    name = local.frontend_https_port_name
    port = 443
  }

  rewrite_rule_set {
    name = "add-response-headers"
    rewrite_rule {
      name          = "add-hsts-header"
      rule_sequence = 1
      response_header_configuration {
        header_name  = "Strict-Transport-Security"
        header_value = "max-age=31536000; includeSubDomains"
      }
    }
  }

  frontend_ip_configuration {
    name                 = local.frontend_ip_public_configuration_name
    public_ip_address_id = azurerm_public_ip.aks_ingress_public_ip.id
  }

  frontend_ip_configuration {
    name                          = local.frontend_ip_private_configuration_name
    private_ip_address_allocation = "Static"
    private_ip_address            = var.vnet.app_gateway_private_ip
    subnet_id                     = azurerm_subnet.agw_subnet.id
  }

  ssl_policy {
    policy_type = "Predefined"
    policy_name = "AppGwSslPolicy20220101S"
  }

  firewall_policy_id = azurerm_web_application_firewall_policy.default_waf_policy.id

  #############################################
  ## This section is required by Terraform, but in practice it's the cluster app_gateway_config agent
  ## that will configure these settings for individual backends
  backend_address_pool {
    name = local.backend_address_pool_name
  }

  backend_http_settings {
    name                  = local.backend_http_settings_name
    cookie_based_affinity = "Disabled"
    port                  = 80
    protocol              = "Http"
    request_timeout       = 30
  }

  http_listener {
    name                           = local.http_listener_name
    frontend_ip_configuration_name = local.frontend_ip_public_configuration_name
    frontend_port_name             = local.frontend_http_port_name
    protocol                       = "Http"
  }

  request_routing_rule {
    name                       = "route-to-backend"
    rule_type                  = "Basic"
    http_listener_name         = local.http_listener_name
    backend_address_pool_name  = local.backend_address_pool_name
    backend_http_settings_name = local.backend_http_settings_name
    priority                   = 19000
    rewrite_rule_set_name      = "add-response-headers"
  }
  #############################################

  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
    # this is needed since AKS app_gateway_config pod synchronizes the cluster state on the side 
    # of app gateway and we do not want to revert it back to default at every tf apply
    ignore_changes = [
      frontend_port,
      backend_address_pool,
      backend_http_settings,
      probe,
      http_listener,
      request_routing_rule,
      ssl_certificate,
      redirect_configuration,
      tags
    ]
  }
}

resource "azurerm_monitor_diagnostic_setting" "app_gateway_all_logs" {
  name                       = "log-all-to-la"
  target_resource_id         = azurerm_application_gateway.app_gateway.id
  log_analytics_workspace_id = var.log_analytics_workspace_id


  enabled_log {
    category_group = "allLogs"
  }

  metric {
    category = "AllMetrics"
  }

  lifecycle {
    create_before_destroy = true
  }
}


resource "azurerm_subnet" "postgresql_subnet" {
  name                              = "snet-${var.workload_name}-${var.environment.name}-postgresql"
  resource_group_name               = var.vnet.rg_name
  virtual_network_name              = var.vnet.name
  address_prefixes                  = [var.vnet.postgresql_subnet_address_space]
  service_endpoints                 = ["Microsoft.Storage"]
  private_endpoint_network_policies = "Enabled"
  delegation {
    name = "fs"
    service_delegation {
      name = "Microsoft.DBforPostgreSQL/flexibleServers"
      actions = [
        "Microsoft.Network/virtualNetworks/subnets/join/action",
      ]
    }
  }
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
    ignore_changes        = [delegation.0.service_delegation["actions"]]
  }
}

resource "azurerm_subnet_network_security_group_association" "subnet_nsg_postgresql_assoc" {
  subnet_id                 = azurerm_subnet.postgresql_subnet.id
  network_security_group_id = azurerm_network_security_group.nsg.id
}


resource "azurerm_subnet_route_table_association" "subnet_postgresql_rt_assoc" {
  subnet_id      = azurerm_subnet.postgresql_subnet.id
  route_table_id = data.azurerm_route_table.default_route_table.id
}
