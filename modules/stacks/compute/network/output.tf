
output "application_gateway" {
  value = {
    id         = azurerm_application_gateway.app_gateway.id
    subnet_id  = azurerm_subnet.agw_subnet.id
    public_ip  = azurerm_public_ip.aks_ingress_public_ip.ip_address
    private_ip = [for item in azurerm_application_gateway.app_gateway.frontend_ip_configuration : item if item.name == local.frontend_ip_private_configuration_name][0].private_ip_address
  }
}

output "resource_group" {
  value = {
    name = azurerm_resource_group.network_rg.name
    id   = azurerm_resource_group.network_rg.id
  }
}

output "subnet_config" {
  value = {
    id                        = azurerm_subnet.subnet.id
    network_security_group_id = azurerm_network_security_group.nsg.id
  }
}

output "postgresql_subnet" {
  value = {
    id = azurerm_subnet.postgresql_subnet.id
  }
}
