output "kubernetes" {
  value = {
    client_certificate        = module.k8s.client_certificate
    client_key                = module.k8s.client_key
    cluster_ca_certificate    = module.k8s.cluster_ca_certificate
    host                      = module.k8s.host
    cluster_oidc_issuer_url   = module.k8s.cluster_oidc_issuer_url
    keda_identity_id          = module.k8s.keda_identity_id
    grafana_sql_user_password = module.k8s.grafana_sql_user_password
  }

  description = <<EOT
  Kubernetes cluster generated configuration
  {
    client_certificate: "Kubernetes client certificate used for authentication"
    client_key: "Kubernetes client private key used for authentication"
    cluster_ca_certificate: "Kubernetes cluster root certificate"
    host: "AKS internal host"
    cluster_oidc_issuer_url: "AKS OpenID Connect issuer URL"
    keda_identity_id: "ID of the Managed Identity assigned to KEDA"
    grafana_sql_user_password: "Password for the SQL user used by Grafana for data source authentication"
  }
  EOT
}

output "network" {
  value = {
    subnet_id            = module.network.subnet_config.id
    postgresql_subnet_id = module.network.postgresql_subnet.id
    dns_config = {
      dns_zone_name                = var.top_dns_zone.name
      dns_zone_resource_group_name = var.top_dns_zone.resource_group_name
      public_ip                    = module.network.application_gateway.public_ip
      private_ip                   = module.network.application_gateway.private_ip
    }
  }

  description = <<EOT
  Network generated configuration
  {
    subnet_id: "Subnet in which all other resources are deployed"
    postgresql_subnet_id: "Managed subnet for postgresql server"
    dns_config:
      dns_zone_name: "Top-level DNS zone name passed from env vars"
      dns_zone_resource_group_name: "Top-level DNS zone resource group passed from env vars"
      public_ip: "Public IP assigned to Application Gateway"
      private_ip: "Private IP created by Application Gateway" 
  }
  EOT
}

output "env_log_analytics_workspace_id" {
  value = azurerm_log_analytics_workspace.env_log_workspace.id
}

output "central_key_vault" {
  value = module.central_key_vault
}
