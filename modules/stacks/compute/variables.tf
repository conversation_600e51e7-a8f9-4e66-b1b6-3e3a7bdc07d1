variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "aks_configuration" {
  type = any
}

variable "infra_admins" {
  type = list(string)
}

variable "infra_readers" {
  type = list(string)
}

variable "argocd_configuration" {
  type = any
}

variable "prometheus_configuration" {
  type = any
}

variable "registry_configuration" {
  type = any
}

variable "top_dns_zone" {
  type = map(any)
}

variable "vnet" {
  type = map(string)
}

variable "tags" {
  type = map(string)
}

variable "private_dns_zone_ids" {
  type = map(string)
}
