
output "databases" {
  value = module.sql_server.databases
}

output "postgresql_servers" {
  value = {
    for server_name, server in module.postgresql_server : replace(server_name, "-", "_") => {
      databases             = server.databases
      server_name           = server.server_name
      server_fqdn           = server.server_fqdn
      server_admin          = server.server_admin
      server_resource_group = server.server_resource_group
    }
  }
}

output "raw_storage" {
  value = {
    account_id            = module.raw_storage_account.account_id
    account_name          = module.raw_storage_account.account_name
    account_blob_endpoint = module.raw_storage_account.account_blob_endpoint
    resource_group_name   = module.raw_storage_account.resource_group_name
  }
}

output "cloud_reports_storage" {
  value = {
    account_id            = module.cloud_reports_storage_account.account_id
    account_name          = module.cloud_reports_storage_account.account_name
    account_blob_endpoint = module.cloud_reports_storage_account.account_blob_endpoint
    account_web_endpoint  = module.cloud_reports_storage_account.account_web_endpoint
    resource_group_name   = module.cloud_reports_storage_account.resource_group_name
  }
}

output "user_service_key_file_storage_account" {
  value = {
    account_id            = module.user_service_key_file_storage_account.account_id
    account_name          = module.user_service_key_file_storage_account.account_name
    account_blob_endpoint = module.user_service_key_file_storage_account.account_blob_endpoint
    account_web_endpoint  = module.user_service_key_file_storage_account.account_web_endpoint
    resource_group_name   = module.user_service_key_file_storage_account.resource_group_name
  }
}

output "sql_backup_storage_account" {
  value = {
    account_id            = module.sql_backup_storage_account.account_id
    account_name          = module.sql_backup_storage_account.account_name
    account_blob_endpoint = module.sql_backup_storage_account.account_blob_endpoint
    resource_group_name   = module.sql_backup_storage_account.resource_group_name
  }
}

output "processed_storage" {
  value = {
    account_id            = module.processed_storage_account.account_id
    account_name          = module.processed_storage_account.account_name
    account_blob_endpoint = module.processed_storage_account.account_blob_endpoint
    resource_group_name   = module.processed_storage_account.resource_group_name
    account_access_key    = module.processed_storage_account.account_access_key
  }
}

output "dataset_storage" {
  value = {
    account_id            = module.dataset_storage.account_id
    account_name          = module.dataset_storage.account_name
    account_blob_endpoint = module.dataset_storage.account_blob_endpoint
    resource_group_name   = module.dataset_storage.resource_group_name
    account_access_key    = module.dataset_storage.account_access_key
  }
}

output "sql_ad_admin" {
  value = var.sql_ad_admin
}


output "partner_portal_storage" {
  value = {
    account_id   = module.partner_portal_storage_account.account_id
    account_name = module.partner_portal_storage_account.account_name
  }
}


output "client_credentials_key_vault" {
  value = {
    id  = module.client_credentials_key_vault.id
    url = module.client_credentials_key_vault.uri
  }
}
