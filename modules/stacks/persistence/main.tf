terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm, azurerm.management]
    }
  }
}

locals {
  storage_readers = [var.common.teams.dpt.azure_group.object_id]
}

module "client_credentials_key_vault" {
  source              = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault?ref=1.1.4"
  name                = "client-creds"
  environment         = var.environment.name
  location            = var.common.location
  tenant_id           = var.common.tenant_id
  readers             = []
  admins              = var.client_credentials_vault_admins
  subnet_id           = var.network_subnet_id
  private_dns_zone_id = var.private_dns_zone_ids.vault
  sku_name            = "standard"
  tags                = var.tags
}

module "sql_server" {
  source                          = "../../components/sql_server"
  tenant_id                       = var.common.tenant_id
  admin_contact_email             = var.common.admin_contact_email
  environment                     = var.environment.name
  sql_ad_admin                    = var.sql_ad_admin
  databases                       = var.configuration.databases
  name                            = "indiebi-db-server"
  key_vault_id                    = var.central_key_vault_id
  grafana_sql_user_password       = var.grafana_sql_user_password
  private_dns_zone_ids            = var.private_dns_zone_ids
  subnet_id                       = var.network_subnet_id
  server_password_expiration_date = var.common.keys_secrets_expiration_date
  tags                            = var.tags
}

module "postgresql_server" {
  for_each = {
    for index, server in var.configuration.postgresql : server.server_name => server
  }
  source       = "../../components/postgresql_server"
  tenant_id    = var.common.tenant_id
  entra_admin  = var.common.teams.cpt.runner_identity
  environment  = var.environment.name
  databases    = each.value.databases
  name         = each.value.server_name
  max_size_mb  = each.value.max_size_mb
  storage_tier = each.value.storage_tier
  sku_name     = each.value.sku_name
  key_vault_id = var.central_key_vault_id
  # grafana_sql_user_password       = var.grafana_sql_user_password
  postgresql_subnet_id            = var.postgresql_subnet_id
  postgresql_dns_zone_id          = var.private_dns_zone_ids.postgres
  server_password_expiration_date = var.common.keys_secrets_expiration_date
  tags                            = var.tags
}

module "raw_storage_account" {
  source               = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  environment          = var.environment.name
  subnet_id            = var.network_subnet_id
  private_dns_zone_ids = var.private_dns_zone_ids
  backup               = var.common.backup
  name                 = "raw-storage"
  container_names      = ["raw-reports", "raw-reports-migrated", "public-data"]
  is_public            = true
  tags                 = { stack = "persistence", team = "dpt" }
  readers              = local.storage_readers
  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

module "module_federation_storage_account" {
  source                 = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  environment            = var.environment.name
  subnet_id              = var.network_subnet_id
  private_dns_zone_ids   = var.private_dns_zone_ids
  name                   = "mfederation"
  static_website_enabled = false
  hns_enabled            = false
  tags                   = { stack = "persistence", team = "saas" }
  is_public              = true
  is_data_public         = true
  container_names        = ["dashboards-mf"]
  contributors           = [var.common.teams.saas.azure_group.object_id, var.common.teams.saas.runner_identity.object_id]
  cors_rules             = var.configuration.module_federation_storage_account.cors_rules
  skip_backup            = true
  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

module "partner_portal_storage_account" {
  source               = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  name                 = "partner-storage"
  container_names      = ["partner-portal"]
  environment          = var.environment.name
  subnet_id            = var.network_subnet_id
  private_dns_zone_ids = var.private_dns_zone_ids

  backup = var.common.backup

  use_table_storage = true
  is_public         = false
  tags              = { stack = "persistence", team = "cpt" }

  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

module "cloud_reports_storage_account" {
  source                 = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  environment            = var.environment.name
  subnet_id              = var.network_subnet_id
  private_dns_zone_ids   = var.private_dns_zone_ids
  backup                 = var.common.backup
  name                   = "cloud-store"
  static_website_enabled = true
  tags                   = { stack = "persistence", team = "dpt" }
  is_public              = true
  readers                = local.storage_readers
  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

module "user_service_key_file_storage_account" {
  source               = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  environment          = var.environment.name
  readers              = [var.common.teams.cpt.azure_group.object_id]
  subnet_id            = var.network_subnet_id
  private_dns_zone_ids = var.private_dns_zone_ids
  backup               = var.common.backup
  tags                 = var.tags
  resource_group_name  = "user-service-key-file-storage"
  name                 = "user-key-file"
  container_names      = ["files"]
  is_public            = true
  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

module "processed_storage_account" {
  source      = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  environment = var.environment.name
  readers = [
    var.common.teams.dpt.azure_group.object_id,
    var.common.teams.ppt.azure_group.object_id,
    var.common.service_principals.dbw_indiebi.object_id
  ]
  subnet_id            = var.network_subnet_id
  private_dns_zone_ids = var.private_dns_zone_ids
  backup               = var.common.backup
  resource_group_name  = "processed-storage"
  name                 = "aggregated"
  tier                 = "Premium"
  kind                 = "BlockBlobStorage"
  hns_enabled          = true
  is_public            = true
  tags                 = { stack = "persistence", team = "dpt" }
  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

module "dataset_storage" {
  source               = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  name                 = "dataset-manager"
  resource_group_name  = "dataset-manager-storage"
  environment          = var.environment.name
  readers              = [var.common.teams.dpt.azure_group.object_id]
  subnet_id            = var.network_subnet_id
  private_dns_zone_ids = var.private_dns_zone_ids
  backup               = var.common.backup
  tags                 = { stack = "persistence", team = "dpt", workload = "dataset-manager" }
  container_names      = ["models"]
  is_public            = true
  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

module "sql_backup_storage_account" {
  source               = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/storage_account?ref=1.1.5"
  environment          = var.environment.name
  backup               = var.common.backup
  subnet_id            = var.network_subnet_id
  private_dns_zone_ids = var.private_dns_zone_ids
  readers              = [var.common.teams.cpt.azure_group.object_id]
  name                 = "sql-backup-store"
  container_names      = ["backup"]
  tags                 = { stack = "persistence", team = "cpt" }
  is_public            = false
  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

resource "azurerm_storage_management_policy" "processed_storage_lifecycle_policy" {
  storage_account_id = module.processed_storage_account.account_id

  rule {
    name    = "remove-older-that-14-days"
    enabled = true
    filters {
      blob_types = ["blockBlob"]
      prefix_match = [
        "processed-data/processed-reports",
        "saas-gold/data",
        "direct_data_access_gold/data",
        "pbi-gold/data",
        "ppt-gold/data",
        "core-silver/result",
        "events-service-gold/data",
        "public-data-crawler/"
      ]
    }
    actions {
      base_blob {
        delete_after_days_since_creation_greater_than = 14
      }
      snapshot {
        delete_after_days_since_creation_greater_than = 14
      }
      version {
        delete_after_days_since_creation = 14
      }
    }
  }
}
