variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "sql_ad_admin" {
  type = map(string)
}

variable "infra_admins" {
  type = list(any)
}

variable "infra_readers" {
  type = list(any)
}

variable "client_credentials_vault_admins" {
  type = list(string)
}

variable "network_subnet_id" {
  type = string
}

variable "postgresql_subnet_id" {
  type = string
}

variable "tags" {
  type = map(string)
}

variable "configuration" {
  type = any
}

variable "grafana_sql_user_password" {
  type = string
}

variable "private_dns_zone_ids" {
  type = map(string)
}

variable "central_key_vault_id" {
  type = string
}
