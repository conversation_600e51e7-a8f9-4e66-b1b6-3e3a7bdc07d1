terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.processing_notification
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                = "dpt"
  dataset_manager_api_key_secret_name = "dataset-manager-api-key"
  user_service_v2_api_key_secret_name = "user-service-v2-api-key"
  one_signal_app_id_secret_name       = "one-signal-app-id"
  one_signal_api_key_secret_name      = "one-signal-api-key"
  sentry_dsn_secret_name              = "data-platform-team-sentry-dsn"
  elastic_secret_token_secret_name    = "elastic-secret-token"
  elastic_service_url_secret_name     = "elastic-service-url"
}

module "one_signal_app_id" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.one_signal_app_id_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers = [
    var.common.teams.dpt.azure_group.object_id
  ]
  admins = [var.common.teams.dpt.team_leader.object_id]
}

module "one_signal_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.one_signal_api_key_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "processing_notification" {
  name                    = "processing-notification"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace
  max_duration            = 7200

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = merge(
    {
      DATASET_MANAGER_URL = "http://dataset-manager-service"
      USER_SERVICE_URL    = "http://user-service-v2-service.cpt"
      SENTRY_ENVIRONMENT  = var.environment.name
    }
  )
  secret = merge(
    {
      DATASET_MANAGER_API_KEY = local.dataset_manager_api_key_secret_name
      USER_SERVICE_API_KEY    = local.user_service_v2_api_key_secret_name
      ONE_SIGNAL_APP_ID       = local.one_signal_app_id_secret_name
      ONE_SIGNAL_API_KEY      = local.one_signal_api_key_secret_name
      SENTRY_DSN              = local.sentry_dsn_secret_name
      ELASTIC_SECRET_TOKEN    = local.elastic_secret_token_secret_name
      ELASTIC_SERVICE_URL     = local.elastic_service_url_secret_name
    }
  )
  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}
