variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "team_configuration" {
  type = any
}

variable "kubernetes" {
  type = map(string)
}

variable "network" {
  type = any
}

variable "central_key_vault_id" {
  type = string
}

variable "persistence" {
  type = any
}

variable "service_bus" {
  type = map(string)
}

variable "pipeline_manager" {
  type = map(string)
}

