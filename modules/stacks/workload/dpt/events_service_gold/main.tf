terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.events_service_gold
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace             = "dpt"
  sentry_dsn_secret_name           = "data-platform-team-sentry-dsn"
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"

  config = {
    READER_CFG__TYPE           = "dls"
    READER_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    READER_CFG__CONTAINER_NAME = "core-silver"
    READER_CFG__BASE_DIR       = "result"

    WRITER_CFG__TYPE           = "dls"
    WRITER_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    WRITER_CFG__CONTAINER_NAME = "events-service-gold"
    WRITER_CFG__BASE_DIR       = "data"

    SENTRY_ENVIRONMENT = var.environment.name
    FORK_HANDLER       = true
  }

  secret = {
    SENTRY_DSN           = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN = local.elastic_secret_token_secret_name
    ELASTIC_SERVICE_URL  = local.elastic_service_url_secret_name
  }
}

module "events_service_gold" {
  name                    = "events-service-gold"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = local.config
  secret = local.secret

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_storage_data_lake_gen2_filesystem" "adls" {
  name               = "events-service-gold"
  storage_account_id = var.persistence.processed_storage.account_id
}

resource "azurerm_role_assignment" "processed_data_lake_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.events_service_gold.identity_principal_id
}

module "events_service_gold_heavy" {
  name                    = "events-service-gold-heavy"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.heavy_slot.kubernetes

  config = local.config
  secret = local.secret

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_role_assignment" "processed_data_lake_slot_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.events_service_gold_heavy.identity_principal_id
}
