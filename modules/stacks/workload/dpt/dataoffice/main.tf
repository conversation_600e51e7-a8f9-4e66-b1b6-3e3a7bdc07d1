terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.dataoffice
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                 = "dpt"
  dataoffice_url                       = "https://${local.workload_configuration.workload_name}.${var.environment.domain}"
  report_service_api_key_secret_name   = "report-service-api-key"
  dataset_manager_api_key_secret_name  = "dataset-manager-api-key"
  user_service_v2_api_key_secret_name  = "user-service-v2-api-key"
  pipeline_manager_api_key_secret_name = "pipeline-manager-api-key"
  auth_client_secret_name              = "dataoffice-auth-client-secret"
  cookie_secret_key_name               = "dataoffice-cookie-secret-key"
  scraper_service_api_key_secret_name  = "scraper-service-api-key"
  cloud_reports_storage                = var.persistence.cloud_reports_storage
}

resource "azuread_application" "dataoffice_app_registration" {
  display_name            = "dataoffice-${var.environment.name}"
  group_membership_claims = ["All"]

  lifecycle {
    create_before_destroy = true
  }

  feature_tags {
    enterprise = true
    gallery    = false
  }

  web {
    homepage_url  = local.dataoffice_url
    logout_url    = "${local.dataoffice_url}/logout"
    redirect_uris = ["${local.dataoffice_url}/oauth/callback"]
  }

  required_resource_access {
    resource_app_id = "00000003-0000-0000-c000-000000000000" # Microsoft Graph

    resource_access {
      id   = "e1fe6dd8-ba31-4d61-89e7-88639da4683d" # User.Read
      type = "Scope"
    }

    resource_access {
      id   = "b340eb25-3456-403f-be2f-af7a0d370277" # User.ReadBasic.All
      type = "Scope"
    }
  }
}

resource "azuread_application_password" "dataoffice_app_secret" {
  application_id = azuread_application.dataoffice_app_registration.id
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.resource_group.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.dpt.azure_group.object_id
}

module "auth_client_secret" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.auth_client_secret_name
  autogenerated   = false
  value           = azuread_application_password.dataoffice_app_secret.value
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "cookie_secret_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.cookie_secret_key_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "dataoffice_app" {
  source = "../../../../components/web_app"

  resource_group_name         = azurerm_resource_group.resource_group.name
  tenant_id                   = var.common.tenant_id
  location                    = var.common.location
  secret_store_name           = var.common.secret_store_name
  env_dns_extension           = var.environment.dns_extension
  environment                 = var.environment.name
  domain                      = var.environment.domain
  cluster_oidc_issuer_url     = var.kubernetes.cluster_oidc_issuer_url
  dns_config                  = var.network.dns_config
  argocd_notifications        = var.team_configuration.argocd_notifications
  name                        = local.workload_configuration.workload_name
  kubernetes_namespace        = local.kubernetes_namespace
  kubernetes                  = local.workload_configuration.kubernetes
  docker_image                = local.workload_configuration.docker_image
  priority_class_name         = "low-priority"
  healthcheck_timeout_seconds = 5
  request_timeout_seconds     = 300 # triggering everything might take some time

  tags = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART
  is_private_webapp = true # true for VPN-only, false for public access
  firewall_policy   = { mode = "Detection" }

  config = {
    AUTH_TENANT_ID           = var.common.tenant_id
    AUTH_CLIENT_ID           = azuread_application.dataoffice_app_registration.client_id
    PIPELINE_MANAGER_URL     = "http://pipeline-manager-service.cpt"
    REPORT_SERVICE_URL       = "http://report-service-service.cpt"
    DATASET_MANAGER_URL      = "http://dataset-manager-service"
    USER_SERVICE_URL         = "http://user-service-v2-service.cpt"
    SCRAPER_SERVICE_URL      = "http://scraper-service-service.dpt"
    DATAOFFICE_URL           = local.dataoffice_url
    CLOUD_REPORTS_REPORT_URL = "${local.cloud_reports_storage.account_web_endpoint}${var.environment.name}/reports/latest.html"
  }

  secret = {
    AUTH_CLIENT_SECRET   = local.auth_client_secret_name
    PIPELINE_MANAGER_KEY = local.pipeline_manager_api_key_secret_name
    REPORT_SERVICE_KEY   = local.report_service_api_key_secret_name
    DATASET_MANAGER_KEY  = local.dataset_manager_api_key_secret_name
    USER_SERVICE_KEY     = local.user_service_v2_api_key_secret_name
    COOKIE_SECRET_KEY    = local.cookie_secret_key_name
    SCRAPER_SERVICE_KEY  = local.scraper_service_api_key_secret_name
  }
}
