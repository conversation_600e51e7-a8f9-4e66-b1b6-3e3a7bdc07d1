terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm, azurerm.connectivity]
    }
  }
}

resource "kubernetes_namespace" "team_ns" {
  metadata {
    name = var.common.teams.dpt.name
  }
}

resource "helm_release" "team_project" {
  name      = "${var.common.teams.dpt.name}-project"
  chart     = "../components/kubernetes-resource"
  namespace = var.common.teams.dpt.name

  values = [
    <<-EOF
configurations:
  - ${indent(
    4,
    templatefile("../components/templates/project.yaml.tpl",
      {
        namespace  = var.common.teams.dpt.name
        team_group = var.common.teams.dpt.azure_group.object_id
      }
    )
)}
  EOF
]

depends_on = [kubernetes_namespace.team_ns]
}

locals {
  service_bus_rg         = "rg-pipeline-manager-${var.environment.name}"
  service_bus_namespace  = "sb-data-pipeline-${var.environment.name}"
  service_bus_topic_name = "data-jobs-topic"

  pipeline_manager_service_bus = {
    namespace_id          = "/subscriptions/${var.environment.subscription_id}/resourceGroups/${local.service_bus_rg}/providers/Microsoft.ServiceBus/namespaces/${local.service_bus_namespace}"
    namespace             = local.service_bus_namespace
    topic_id              = "/subscriptions/${var.environment.subscription_id}/resourceGroups/${local.service_bus_rg}/providers/Microsoft.ServiceBus/namespaces/${local.service_bus_namespace}/topics/${local.service_bus_topic_name}"
    topic_name            = local.service_bus_topic_name
    deadletter_topic_name = "data-jobs-deadletter-topic"
  }
}

module "data_platform_team_sentry_dsn" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "data-platform-team-sentry-dsn"
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "dataset_manager" {
  source               = "./dataset_manager"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  private_dns_zone_ids = var.private_dns_zone_ids
  backup_manager       = var.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

locals {
  pipeline_manager = {
    url                 = "http://pipeline-manager-service.cpt"
    api_key_secret_name = "pipeline-manager-api-key"
  }
}

module "update_shared" {
  source               = "./update_shared"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "find_shards" {
  source               = "./find_shards"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "pbi_refresh" {
  source               = "./pbi_refresh"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "core_silver" {
  source               = "./core_silver"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "processing_notification" {
  source               = "./processing_notification"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "scraper_api" {
  source               = "./scraper_api"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  private_dns_zone_ids = var.private_dns_zone_ids

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}


module "cloud_reports" {
  source                         = "./cloud_reports"
  common                         = var.common
  environment                    = var.environment
  team_configuration             = var.team_configuration
  persistence                    = var.persistence
  kubernetes                     = var.kubernetes
  network                        = var.network
  central_key_vault_id           = var.central_key_vault_id
  service_bus                    = local.pipeline_manager_service_bus
  pipeline_manager               = local.pipeline_manager
  user_service_identity_provider = var.user_service_identity_provider

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "events_service" {
  source               = "./events_service"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  backup_manager       = var.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "events_service_gold" {
  source               = "./events_service_gold"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "dataoffice" {
  source               = "./dataoffice"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "currency_exchange_rates" {
  source             = "./currency_exchange_rates"
  common             = var.common
  environment        = var.environment
  team_configuration = var.team_configuration
  persistence        = var.persistence
  kubernetes         = var.kubernetes
  service_bus        = local.pipeline_manager_service_bus
  pipeline_manager   = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "scraper_service" {
  source               = "./scraper_service"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  backup_manager       = var.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
