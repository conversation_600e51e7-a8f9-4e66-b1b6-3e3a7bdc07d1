terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.find_shards
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                = "dpt"
  report_service_api_key_secret_name  = "report-service-api-key"
  user_service_v2_api_key_secret_name = "user-service-v2-api-key"
  dataset_manager_api_key_secret_name = "dataset-manager-api-key"
  sentry_dsn_secret_name              = "data-platform-team-sentry-dsn"
  elastic_secret_token_secret_name    = "elastic-secret-token"
  elastic_service_url_secret_name     = "elastic-service-url"
}

module "find_shards" {
  name                    = "find-shards"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = merge(
    {
      USER_SERVICE_URL    = "http://user-service-v2-service.cpt"
      REPORT_SERVICE_URL  = "http://report-service-service.cpt"
      DATASET_MANAGER_URL = "http://dataset-manager-service"
      SENTRY_ENVIRONMENT  = var.environment.name
    }
  )
  secret = merge(
    {
      REPORT_SERVICE_API_KEY  = local.report_service_api_key_secret_name
      USER_SERVICE_API_KEY    = local.user_service_v2_api_key_secret_name
      DATASET_MANAGER_API_KEY = local.dataset_manager_api_key_secret_name
      SENTRY_DSN              = local.sentry_dsn_secret_name
      ELASTIC_SECRET_TOKEN    = local.elastic_secret_token_secret_name
      ELASTIC_SERVICE_URL     = local.elastic_service_url_secret_name
    }
  )
  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}
