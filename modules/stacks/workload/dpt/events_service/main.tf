terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.events_service
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace            = "dpt"
  database                        = var.persistence.databases.events_service
  access_token_secret_secret_name = "access-token-secret"
  api_key_secret_name             = "${local.workload_configuration.workload_name}-api-key"
  sentry_dsn_secret_name          = "data-platform-team-sentry-dsn"
  elastic_secret_token_name       = "elastic-secret-token"
  elastic_service_url_name        = "elastic-service-url"
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

module "api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "events_service_app" {
  source = "../../../../components/web_app"

  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  environment             = var.environment.name
  domain                  = var.environment.domain
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  argocd_notifications    = var.team_configuration.argocd_notifications
  resource_group_name     = azurerm_resource_group.resource_group.name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  env_dns_extension       = var.environment.dns_extension
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  tags                    = local.tags
  replicas                = local.workload_configuration.replicas

  # add this section if it's a web app accessible from outside of AKS
  is_private_webapp = true # true for VPN-only, false for public access
  firewall_policy   = { mode = "Detection" }

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic.ini, upgrade, head]"
  }

  config = {
    ENV                     = var.environment.name
    SQLALCHEMY_DATABASE_URL = "mssql+pyodbc:///?odbc_connect=Driver=ODBC+Driver+17+for+SQL+Server;Server=${local.database.host};Database=${local.database.name}"
    DLS_ACCOUNT_NAME        = var.persistence.processed_storage.account_name
    SENTRY_ENVIRONMENT      = var.environment.name
  }

  secret = {
    SENTRY_DSN           = local.sentry_dsn_secret_name
    API_KEY              = local.api_key_secret_name
    ELECTRON_HASH_SECRET = local.access_token_secret_secret_name
    ELASTIC_SECRET_TOKEN = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL  = local.elastic_service_url_name
  }

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

resource "azurerm_role_assignment" "processed_storage_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = module.events_service_app.identity.principal_id
}

module "database_provisioner" {
  source   = "../../../../components/database_provisioner"
  database = local.database
  server_admin = {
    username = "${var.persistence.sql_ad_admin.app_id}@${var.common.tenant_id}"
    password = var.persistence.sql_ad_admin.password
  }
  admins = [
    {
      object_id = module.events_service_app.identity.id
      name      = module.events_service_app.identity.name
    },
    var.common.teams.dpt.team_leader
  ]
  writers = [var.common.teams.dpt.azure_group]
  readers = [var.common.service_principals.dbw_indiebi, var.backup_manager]
}
