variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "team_configuration" {
  type = any
}

variable "kubernetes" {
  type = map(string)
}

variable "network" {
  type = any
}

variable "persistence" {
  type = any
}

variable "central_key_vault_id" {
  type = string
}


variable "private_dns_zone_ids" {
  type = map(any)
}

variable "user_service_identity_provider" {
  type = map(any)
}

variable "backup_manager" {
  type = any
}
