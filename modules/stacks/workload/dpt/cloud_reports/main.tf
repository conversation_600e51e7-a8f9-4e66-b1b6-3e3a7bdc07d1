terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.cloud_reports
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace             = "dpt"
  cloud_reports_storage            = var.persistence.cloud_reports_storage
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"
  user_service_api_key_secret_name = "user-service-v2-api-key"
}

module "epic_account_id" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${local.workload_configuration.workload_name}-epic-account-id"
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "epic_device_id" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${local.workload_configuration.workload_name}-epic-device-id"
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "epic_oauth_token" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${local.workload_configuration.workload_name}-epic-oauth-token"
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "epic_secret" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${local.workload_configuration.workload_name}-epic-secret"
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}


module "cloud_reports" {
  name                    = local.workload_configuration.workload_name
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace
  max_replica_count       = 1

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = {
    ENV                     = var.environment.name
    SCRAPER_API_URL         = "http://scraper-api-service.dpt"
    SCRAPER_SERVICE_URL     = "http://scraper-service-service.dpt"
    SUMMARY_VIEW_URL        = "${local.cloud_reports_storage.account_web_endpoint}${var.environment.name}/reports"
    SUMMARY_UPLOAD_URL      = "${local.cloud_reports_storage.account_blob_endpoint}$$web/${var.environment.name}/reports"
    SUMMARY_USE_SAS_TOKEN   = "False"
    USER_SERVICE_URL        = "http://user-service-v2-service.cpt"
    USER_SERVICE_APP_ID_URI = var.user_service_identity_provider.identifier_uri
  }

  secret = {
    EPIC_ACCOUNT_ID      = module.epic_account_id.secret_name
    EPIC_DEVICE_ID       = module.epic_device_id.secret_name
    EPIC_OAUTH_TOKEN     = module.epic_oauth_token.secret_name
    EPIC_SECRET          = module.epic_secret.secret_name
    ELASTIC_SECRET_TOKEN = local.elastic_secret_token_secret_name
    ELASTIC_SERVICE_URL  = local.elastic_service_url_secret_name
    USER_SERVICE_API_KEY = local.user_service_api_key_secret_name
  }

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_role_assignment" "cloud_storage_role_assignment" {
  scope                = local.cloud_reports_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.cloud_reports.identity_principal_id
}

resource "azuread_app_role_assignment" "user_impersonate_role_assignment" {
  app_role_id         = var.user_service_identity_provider.impersonate_role_id
  principal_object_id = module.cloud_reports.identity_principal_id
  resource_object_id  = var.user_service_identity_provider.object_id
}
