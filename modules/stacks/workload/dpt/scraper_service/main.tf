terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.scraper_service
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                = "dpt"
  api_key_secret_name                 = "${local.workload_configuration.workload_name}-api-key"
  database_server_fqdn                = var.persistence.postgresql_servers.indiebi_postgresql_server.server_fqdn
  database_server_name                = var.persistence.postgresql_servers.indiebi_postgresql_server.server_name
  database_name                       = var.persistence.postgresql_servers.indiebi_postgresql_server.databases.scraper_service.name
  sentry_dsn_secret_name              = "data-platform-team-sentry-dsn"
  elastic_secret_token_name           = "elastic-secret-token"
  elastic_service_url_name            = "elastic-service-url"
  user_access_token_public_key_name   = "user-access-token-public-key"
  identity_name                       = "id-${local.workload_configuration.workload_name}-${var.environment.name}"
  user_service_v2_api_key_secret_name = "user-service-v2-api-key"
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

module "api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}


module "scraper_service_app" {
  source = "../../../../components/web_app"

  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  environment             = var.environment.name
  domain                  = var.environment.domain
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  argocd_notifications    = var.team_configuration.argocd_notifications
  resource_group_name     = azurerm_resource_group.resource_group.name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  env_dns_extension       = var.environment.dns_extension
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  tags                    = local.tags
  replicas                = local.workload_configuration.replicas

  # add this section if it's a web app accessible from outside of AKS
  is_private_webapp          = false
  private_routes_allowed_ips = [var.common.firewall_public_ip_address]

  firewall_policy = {
    overrides = [
      {
        rule_group_name = "REQUEST-920-PROTOCOL-ENFORCEMENT"
        rule_ids        = ["920230", "920320"]
      },
      {
        rule_group_name = "REQUEST-921-PROTOCOL-ATTACK"
        rule_ids        = ["921130"]
      },
      {
        rule_group_name = "REQUEST-932-APPLICATION-ATTACK-RCE"
        rule_ids        = ["932105", "932115", "932130", "932140"]
      },
      {
        rule_group_name = "REQUEST-933-APPLICATION-ATTACK-PHP"
        rule_ids        = ["933210"]
      },
      {
        rule_group_name = "REQUEST-941-APPLICATION-ATTACK-XSS"
        rule_ids        = ["941100", "941120", "941130", "941140", "941150", "941160", "941180", "941190", "941250", "941260", "941270", "941320", "941330", "941340"]
      },
      {
        rule_group_name = "REQUEST-942-APPLICATION-ATTACK-SQLI"
        rule_ids        = ["942100", "942110", "942120", "942130", "942150", "942180", "942190", "942200", "942210", "942230", "942260", "942270", "942300", "942330", "942340", "942360", "942361", "942370", "942380", "942390", "942400", "942410", "942430", "942440", "942450", "942480"]
      },
    ]
  }

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic.ini, upgrade, head]"
  }

  config = {
    ENV                = var.environment.name
    SENTRY_ENVIRONMENT = var.environment.name
    DATABASE_URL       = "postgresql://${local.identity_name}:azure_identity_token@${local.database_server_fqdn}/${local.database_name}?sslmode=require"
    TEAMS_WEBHOOK_URL  = local.workload_configuration.configuration.teams_webhook_url
    USER_SERVICE_URL   = "http://user-service-v2-service.cpt"
  }

  secret = {
    SENTRY_DSN                   = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN         = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL          = local.elastic_service_url_name
    USER_ACCESS_TOKEN_PUBLIC_KEY = local.user_access_token_public_key_name
    API_KEY                      = local.api_key_secret_name
    USER_SERVICE_KEY             = local.user_service_v2_api_key_secret_name
  }

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

resource "azurerm_postgresql_flexible_server_active_directory_administrator" "managed_identity_admin" {
  server_name         = local.database_server_name
  resource_group_name = var.persistence.postgresql_servers.indiebi_postgresql_server.server_resource_group
  tenant_id           = var.common.tenant_id
  object_id           = module.scraper_service_app.identity.principal_id
  principal_name      = module.scraper_service_app.identity.name
  principal_type      = "ServicePrincipal"
  lifecycle {
    create_before_destroy = true
  }
}

module "database_provisioner" {
  source = "../../../../components/postgresql_provisioner"
  database = {
    name = local.database_name
    host = local.database_server_fqdn
  }
  server_admin = var.persistence.postgresql_servers.indiebi_postgresql_server.server_admin
  entra_admin  = var.common.teams.cpt.runner_identity
  schema       = "public"
  admins = [
    {
      object_id = module.scraper_service_app.identity.id
      name      = module.scraper_service_app.identity.name
    },
    var.common.teams.dpt.team_leader
  ]
  entra_users = [var.common.teams.dpt.azure_group, { object_id = var.backup_manager.principal_id, name = var.backup_manager.name }]
}
