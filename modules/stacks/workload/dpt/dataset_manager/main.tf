terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm, azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.dataset_manager
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                  = "dpt"
  database                              = var.persistence.databases.dataset_manager
  api_key_secret_name                   = "${local.workload_configuration.workload_name}-api-key"
  processed_adls_access_key_secret_name = "processed-adls-access-key"
  pbi_azure_client_secret_name          = "pbi-azure-client-secret"
  placeholder_secret_value              = "IM_A_PLACEHOLDER_CHANGE_ME"
  sentry_dsn_secret_name                = "data-platform-team-sentry-dsn"
  elastic_secret_token_name             = "elastic-secret-token"
  elastic_service_url_name              = "elastic-service-url"
  user_access_token_public_key_name     = "user-access-token-public-key"
  user_service_api_key_secret_name      = "user-service-v2-api-key"
}

resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.dpt.azure_group.object_id
}

module "api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id, var.common.teams.saas.runner_identity.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "processed_adls_credential" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.processed_adls_access_key_secret_name
  value           = var.persistence.processed_storage.account_access_key
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "pbi_azure_client_secret" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.pbi_azure_client_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}



module "dataset_manager_app" {
  source = "../../../../components/web_app"

  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  tags                    = local.tags
  environment             = var.environment.name
  domain                  = var.environment.domain
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  argocd_notifications    = var.team_configuration.argocd_notifications
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  resource_group_name     = azurerm_resource_group.rg.name

  priority_class_name         = "high-priority"
  healthcheck_timeout_seconds = 3

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  is_private_webapp = true
  firewall_policy   = { mode = "Detection" }

  config = {
    SQLALCHEMY_DATABASE_URL     = "mssql+pyodbc:///?odbc_connect=Driver=ODBC+Driver+17+for+SQL+Server;Server=${local.database.host};Database=${local.database.name}"
    AZURE_SUBSCRIPTION_ID       = var.environment.subscription_id
    AZURE_RESOURCE_GROUP_NAME   = local.workload_configuration.configuration.pbi_resource_group
    PBI_AZURE_TENANT_ID         = var.common.tenant_id
    PBI_AZURE_SUBSCRIPTION_ID   = local.workload_configuration.configuration.pbi_sub_id
    AZURE_SUBSCRIPTION_ID       = local.workload_configuration.configuration.pbi_sub_id
    SERVICE_PRINCIPAL_ID        = local.workload_configuration.configuration.user_profiles_service_principal_id
    PBI_AZURE_CLIENT_ID         = local.workload_configuration.configuration.azure_client_id
    DLS_ACCOUNT_NAME            = var.persistence.processed_storage.account_name
    DATASET_STORAGE_ACCOUNT_URL = var.persistence.dataset_storage.account_blob_endpoint
    SENTRY_ENVIRONMENT          = var.environment.name
    USER_SERVICE_URL            = "http://user-service-v2-service.cpt"
  }

  secret = {
    SENTRY_DSN                   = local.sentry_dsn_secret_name
    API_KEY                      = local.api_key_secret_name
    PBI_AZURE_CLIENT_SECRET      = local.pbi_azure_client_secret_name
    PROCESSED_ADLS_CREDENTIALS   = local.processed_adls_access_key_secret_name
    ELASTIC_SECRET_TOKEN         = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL          = local.elastic_service_url_name
    USER_ACCESS_TOKEN_PUBLIC_KEY = local.user_access_token_public_key_name
    USER_SERVICE_KEY             = local.user_service_api_key_secret_name
  }

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic.ini, upgrade, head]"
  }

  env_dns_extension = var.environment.dns_extension


  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

resource "azurerm_role_assignment" "dataset_storage_role_assignment" {
  scope                = var.persistence.dataset_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.dataset_manager_app.identity.principal_id
}

# TODO: this will be changed by DPT
# once they migrate to using Managed Identity
# we'll be able to remove null_resource
resource "null_resource" "service_principal_provisioner" {
  triggers = {
    # In case of problem with creating DB users (the most likely on first deployment) We should bump this value to force creating users once again
    retry         = 0
    database_name = local.database.name
    identity_id   = module.dataset_manager_app.identity.id
  }
  provisioner "local-exec" {
    command = "sqlcmd --authentication-method=ActiveDirectoryServicePrincipal -d ${local.database.name} -Q \"${
    templatefile("../components/templates/setup_db.sql.tpl", { identity = local.workload_configuration.configuration.service_principal_name })}\""
    environment = {
      SQLCMDSERVER   = local.database.host
      SQLCMDUSER     = "${var.persistence.sql_ad_admin.app_id}@${var.common.tenant_id}"
      SQLCMDPASSWORD = var.persistence.sql_ad_admin.password
    }
  }

  depends_on = [module.dataset_manager_app]
  lifecycle {
    create_before_destroy = true
  }
}

module "database_provisioner" {
  source   = "../../../../components/database_provisioner"
  database = local.database
  server_admin = {
    username = "${var.persistence.sql_ad_admin.app_id}@${var.common.tenant_id}"
    password = var.persistence.sql_ad_admin.password
  }
  admins = [
    {
      object_id = module.dataset_manager_app.identity.id
      name      = module.dataset_manager_app.identity.name
    },
    var.common.teams.dpt.team_leader
  ]
  writers = [var.common.teams.dpt.azure_group]
  readers = [var.common.service_principals.dbw_indiebi, var.common.teams.ppt.azure_group, var.common.teams.saas.azure_group, var.backup_manager]
}
