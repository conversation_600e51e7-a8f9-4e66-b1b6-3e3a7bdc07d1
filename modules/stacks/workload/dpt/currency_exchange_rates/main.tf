terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

resource "azurerm_storage_data_lake_gen2_filesystem" "adls" {
  name               = "public-data-crawlers"
  storage_account_id = var.persistence.processed_storage.account_id
}

locals {
  workload_configuration = var.team_configuration.currency_exchange_rates
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace             = "dpt"
  sentry_dsn_secret_name           = "data-platform-team-sentry-dsn"
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"
}

module "currency_exchange_rates" {
  name                    = "currency-exchange-rates"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace
  max_replica_count       = 1

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = {
    STORAGE_CFG__TYPE           = "dls"
    STORAGE_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    STORAGE_CFG__CONTAINER_NAME = azurerm_storage_data_lake_gen2_filesystem.adls.name
    STORAGE_CFG__BASE_DIR       = "currency-exchange-rates"

    SENTRY_ENVIRONMENT = var.environment.name
  }

  secret = {
    SENTRY_DSN           = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN = local.elastic_secret_token_secret_name
    ELASTIC_SERVICE_URL  = local.elastic_service_url_secret_name
  }

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_role_assignment" "processed_data_lake_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.currency_exchange_rates.identity_principal_id
}
