terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.core_silver
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                = "dpt"
  report_service_api_key_secret_name  = "report-service-api-key"
  user_service_v2_api_key_secret_name = "user-service-v2-api-key"
  sentry_dsn_secret_name              = "data-platform-team-sentry-dsn"
  elastic_secret_token_secret_name    = "elastic-secret-token"
  elastic_service_url_secret_name     = "elastic-service-url"

  config = {
    CRAWLED_PUBLIC_DATA_CFG__TYPE           = "dls"
    CRAWLED_PUBLIC_DATA_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    CRAWLED_PUBLIC_DATA_CFG__CONTAINER_NAME = "public-data-crawlers"
    CRAWLED_PUBLIC_DATA_CFG__BASE_DIR       = ""

    INPUT_CFG__TYPE           = "dls"
    INPUT_CFG__ACCOUNT_NAME   = var.persistence.raw_storage.account_name
    INPUT_CFG__CONTAINER_NAME = "raw-reports"
    INPUT_CFG__BASE_DIR       = ""

    CONVERTED_REPORTS_CFG__TYPE           = "dls"
    CONVERTED_REPORTS_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    CONVERTED_REPORTS_CFG__CONTAINER_NAME = "core-silver"
    CONVERTED_REPORTS_CFG__BASE_DIR       = "converted"

    OUTPUT_CFG__TYPE           = "dls"
    OUTPUT_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    OUTPUT_CFG__CONTAINER_NAME = "core-silver"
    OUTPUT_CFG__BASE_DIR       = "result"

    REPORT_SERVICE__TYPE = "api"
    REPORT_SERVICE__URL  = "http://report-service-service.cpt"

    USER_SERVICE__TYPE = "api"
    USER_SERVICE__URL  = "http://user-service-v2-service.cpt"

    SENTRY_ENVIRONMENT = var.environment.name
    FORK_HANDLER       = true
  }

  secret = {
    REPORT_SERVICE__API_KEY = local.report_service_api_key_secret_name
    USER_SERVICE__API_KEY   = local.user_service_v2_api_key_secret_name
    SENTRY_DSN              = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN    = local.elastic_secret_token_secret_name
    ELASTIC_SERVICE_URL     = local.elastic_service_url_secret_name
  }
}

module "core_silver" {
  name                    = "core-silver"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace
  max_duration            = 10800
  # node memory (GB) * number of nodes / memory request (GB)
  max_replica_count = 192

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = local.config
  secret = local.secret

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_storage_data_lake_gen2_filesystem" "adls" {
  name               = "core-silver"
  storage_account_id = var.persistence.processed_storage.account_id
}

resource "azurerm_role_assignment" "processed_data_lake_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.core_silver.identity_principal_id
}

resource "azurerm_role_assignment" "raw_storage_role_assignment" {
  scope                = var.persistence.raw_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.core_silver.identity_principal_id
}

module "core_silver_heavy" {
  name                    = "core-silver-heavy"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace
  max_duration            = 10800
  # node memory (GB) * number of nodes / memory request (GB)
  max_replica_count = 192

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.heavy_slot.kubernetes

  config = local.config
  secret = local.secret

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_role_assignment" "processed_data_lake_slot_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.core_silver_heavy.identity_principal_id
}

resource "azurerm_role_assignment" "raw_storage_slot_role_assignment" {
  scope                = var.persistence.raw_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.core_silver_heavy.identity_principal_id
}

