terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.scraper_api
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                = "dpt"
  redis_key_secret_name               = "redis-key"
  api_key_secret_name                 = "scraper-api-api-key"
  report_service_api_key_secret_name  = "report-service-api-key"
  user_service_v2_api_key_secret_name = "user-service-v2-api-key"
  elastic_secret_token_name           = "elastic-secret-token"
  elastic_service_url_name            = "elastic-service-url"
  user_access_token_public_key_name   = "user-access-token-public-key"
  access_token_secret_secret_name     = "access-token-secret"

  firewall_policy = {
    overrides = [
      {
        rule_group_name = "REQUEST-920-PROTOCOL-ENFORCEMENT"
        rule_ids        = ["920230", "920300", "920320"]
      },
      {
        rule_group_name = "REQUEST-921-PROTOCOL-ATTACK"
        rule_ids        = ["921130"]
      },
      {
        rule_group_name = "REQUEST-932-APPLICATION-ATTACK-RCE"
        rule_ids        = ["932105", "932115", "932130", "932140", "932150"]
      },
      {
        rule_group_name = "REQUEST-933-APPLICATION-ATTACK-PHP"
        rule_ids        = ["933210"]
      },
      {
        rule_group_name = "REQUEST-941-APPLICATION-ATTACK-XSS"
        rule_ids        = ["941100", "941120", "941130", "941140", "941150", "941160", "941180", "941190", "941250", "941260", "941270", "941320", "941330", "941340"]
      },
      {
        rule_group_name = "REQUEST-942-APPLICATION-ATTACK-SQLI"
        rule_ids        = ["942100", "942110", "942120", "942130", "942150", "942180", "942190", "942200", "942210", "942230", "942260", "942270", "942300", "942330", "942340", "942360", "942361", "942370", "942380", "942390", "942400", "942410", "942430", "942440", "942450", "942480"]
      },
    ]
  }
}

resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.dpt.azure_group.object_id
}

resource "azurerm_redis_cache" "redis" {
  name                          = "redis-${local.workload_configuration.workload_name}-${var.environment.name}"
  location                      = azurerm_resource_group.rg.location
  resource_group_name           = azurerm_resource_group.rg.name
  capacity                      = 0
  family                        = "C"
  sku_name                      = local.workload_configuration.configuration.redis_sku_name
  non_ssl_port_enabled          = false
  minimum_tls_version           = "1.2"
  public_network_access_enabled = false
  tags                          = local.tags
}

resource "azurerm_private_endpoint" "redis_private_endpoint" {
  name                = "${azurerm_redis_cache.redis.name}-endpoint"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
  subnet_id           = var.network.subnet_id
  tags                = local.tags

  private_service_connection {
    name                           = "${azurerm_redis_cache.redis.name}-endpoint"
    private_connection_resource_id = azurerm_redis_cache.redis.id
    is_manual_connection           = false
    subresource_names              = ["redisCache"]
  }

  private_dns_zone_group {
    name                 = "privatelink.redis.cache.windows.net"
    private_dns_zone_ids = [var.private_dns_zone_ids.redis]
  }
}

module "redis_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.redis_key_secret_name
  value           = azurerm_redis_cache.redis.primary_access_key
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "scraper_api_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.dpt.azure_group.object_id]
  admins          = [var.common.teams.dpt.team_leader.object_id]
}

module "scraper_api_app" {
  source = "../../../../components/web_app"

  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  environment             = var.environment.name
  domain                  = var.environment.domain
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  argocd_notifications    = var.team_configuration.argocd_notifications
  resource_group_name     = azurerm_resource_group.rg.name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  server_port             = 3000
  healthcheck_path        = "/health/ping"
  priority_class_name     = "high-priority"
  tags                    = local.tags

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = {
    VERSION                      = "1"
    REPORT_SERVICE_V2_URI        = "http://report-service-service.cpt"
    USER_SERVICE_V2_URI          = "http://user-service-v2-service.cpt"
    NODE_ENV                     = var.environment.name
    REDIS_HOST                   = "redis://${azurerm_redis_cache.redis.hostname}"
    REDIS_PORT                   = azurerm_redis_cache.redis.ssl_port
    LSE_URI                      = local.workload_configuration.configuration.lse_uri
    AUTH_EMAIL_FORWARDING_PREFIX = local.workload_configuration.configuration.auth_email_forwarding_prefix
  }

  secret = {
    REDIS_KEY                    = local.redis_key_secret_name
    API_KEY                      = local.api_key_secret_name
    REPORT_SERVICE_V2_KEY        = local.report_service_api_key_secret_name
    USER_SERVICE_V2_KEY          = local.user_service_v2_api_key_secret_name
    ACCESS_TOKEN_SECRET          = local.access_token_secret_secret_name
    ELASTIC_SECRET_TOKEN         = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL          = local.elastic_service_url_name
    USER_ACCESS_TOKEN_PUBLIC_KEY = local.user_access_token_public_key_name
  }

  is_private_webapp          = false || var.environment.is_private
  firewall_policy            = local.firewall_policy
  private_routes_allowed_ips = [var.common.firewall_public_ip_address]

  env_dns_extension = var.environment.dns_extension

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}


module "scraper_api_app_side_deployment_slot" {
  source = "../../../../components/web_app"

  count                   = local.workload_configuration.side_deployment_slot != null ? 1 : 0
  name                    = local.workload_configuration.side_deployment_slot.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  environment             = var.environment.name
  domain                  = var.environment.domain
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  argocd_notifications    = var.team_configuration.argocd_notifications
  resource_group_name     = azurerm_resource_group.rg.name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  server_port             = 3000
  healthcheck_path        = "/health/ping"
  priority_class_name     = "low-priority"
  tags                    = local.tags

  docker_image = {
    name = local.workload_configuration.docker_image.name
    tag  = local.workload_configuration.side_deployment_slot.tag
  }
  kubernetes = local.workload_configuration.kubernetes

  config = {
    VERSION                      = "1"
    REPORT_SERVICE_V2_URI        = "http://report-service-service.cpt"
    USER_SERVICE_V2_URI          = "http://user-service-v2-service.cpt"
    NODE_ENV                     = var.environment.name
    REDIS_HOST                   = "redis://${azurerm_redis_cache.redis.hostname}"
    REDIS_PORT                   = azurerm_redis_cache.redis.ssl_port
    LSE_URI                      = local.workload_configuration.configuration.lse_uri
    AUTH_EMAIL_FORWARDING_PREFIX = local.workload_configuration.configuration.auth_email_forwarding_prefix
    SERVICE_NAME                 = local.workload_configuration.side_deployment_slot.workload_name
  }

  secret = {
    REDIS_KEY                    = local.redis_key_secret_name
    API_KEY                      = local.api_key_secret_name
    REPORT_SERVICE_V2_KEY        = local.report_service_api_key_secret_name
    USER_SERVICE_V2_KEY          = local.user_service_v2_api_key_secret_name
    ACCESS_TOKEN_SECRET          = local.access_token_secret_secret_name
    ELASTIC_SECRET_TOKEN         = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL          = local.elastic_service_url_name
    USER_ACCESS_TOKEN_PUBLIC_KEY = local.user_access_token_public_key_name
  }

  is_private_webapp = true
  firewall_policy   = local.firewall_policy

  env_dns_extension = var.environment.dns_extension

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
