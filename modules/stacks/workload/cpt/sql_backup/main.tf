terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.sql_backup
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace             = "cpt"
  sentry_dsn_secret_name           = "cloud-platform-team-sentry-dsn"
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"
}

module "sql_backup" {
  name                    = local.workload_configuration.workload_name
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = merge(
    {
      SENTRY_ENVIRONMENT   = var.environment.name
      DATABASES            = "[ ${join(",", [for db in var.persistence.databases : "\\\"${db.name}\\\""])} ]"
      SQL_SERVER_NAME      = var.persistence.databases.report_service.host
      STORAGE_ACCOUNT_NAME = var.persistence.sql_backup_storage_account.account_name
      POSTGRESQL_USERNAME  = "id-${local.workload_configuration.workload_name}-${var.environment.name}" # identity is created inside, so we need to recreate the value
      POSTGRESQL_SERVERS   = replace(jsonencode([for server in var.persistence.postgresql_servers : { host = server.server_fqdn, databases = [for database in server.databases : database.name] }]), "\"", "\\\"")
    }
  )
  secret = merge(
    {
      SENTRY_DSN           = local.sentry_dsn_secret_name
      ELASTIC_SECRET_TOKEN = local.elastic_secret_token_secret_name
      ELASTIC_SERVICE_URL  = local.elastic_service_url_secret_name
    }
  )
  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_role_assignment" "raw_storage_role_assignment" {
  scope                = var.persistence.sql_backup_storage_account.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.sql_backup.identity_principal_id
}
