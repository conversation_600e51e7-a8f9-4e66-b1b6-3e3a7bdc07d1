terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.report_service
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                 = "cpt"
  raw_storage                          = var.persistence.raw_storage
  database                             = var.persistence.databases.report_service
  api_key_secret_name                  = "${local.workload_configuration.workload_name}-api-key"
  pipeline_manager_api_key_secret_name = "pipeline-manager-api-key"
  user_service_v2_api_key_secret_name  = "user-service-v2-api-key"
  sentry_dsn_secret_name               = "cloud-platform-team-sentry-dsn"
  elastic_secret_token_secret_name     = "elastic-secret-token"
  elastic_service_url_secret_name      = "elastic-service-url"
  user_access_token_public_key_name    = "user-access-token-public-key"
}

resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.cpt.azure_group.object_id
}

module "api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

module "report_service_app" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.rg.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image

  priority_class_name         = "high-priority"
  healthcheck_timeout_seconds = 3

  tags = local.tags

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic/alembic.ini, upgrade, head]"
  }

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART
  is_private_webapp = true # true for VPN-only, false for public access
  firewall_policy   = { mode = "Detection" }

  prometheus_monitor_enabled = true

  config = {
    SENTRY_ENVIRONMENT       = var.environment.name
    SQLALCHEMY_DATABASE_URL  = "mssql+pyodbc:///?odbc_connect=Server=${local.database.host};Database=${local.database.name}"
    RAW_STORAGE_ACCOUNT_NAME = local.raw_storage.account_name
  }

  secret = {
    API_KEY                      = local.api_key_secret_name
    PIPELINE_MANAGER_API_KEY     = local.pipeline_manager_api_key_secret_name
    USER_SERVICE_API_KEY         = local.user_service_v2_api_key_secret_name
    SENTRY_DSN                   = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN         = local.elastic_secret_token_secret_name
    ELASTIC_SERVER_URL           = local.elastic_service_url_secret_name
    USER_ACCESS_TOKEN_PUBLIC_KEY = local.user_access_token_public_key_name
  }
}

resource "azurerm_role_assignment" "raw_storage_role_assignment" {
  scope                = local.raw_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.report_service_app.identity.principal_id
}

module "database_provisioner" {
  source   = "../../../../components/database_provisioner"
  database = local.database
  server_admin = {
    username = "${var.persistence.sql_ad_admin.app_id}@${var.common.tenant_id}"
    password = var.persistence.sql_ad_admin.password
  }
  admins = [
    {
      object_id = module.report_service_app.identity.id
      name      = module.report_service_app.identity.name
    },
    var.common.teams.cpt.team_leader
  ]
  writers = [var.common.teams.cpt.azure_group]
  readers = [var.common.service_principals.dbw_indiebi, var.common.teams.saas.azure_group, var.backup_manager]
}
