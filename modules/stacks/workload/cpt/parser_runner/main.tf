terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.parser_runner
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace             = "cpt"
  sentry_dsn_secret_name           = "cloud-platform-team-sentry-dsn"
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"
}

module "parser_runner" {
  name                    = "parser-runner"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = merge(
    {
      CRAWLED_STORAGE_ACCOUNT_NAME   = var.persistence.raw_storage.account_name
      CRAWLED_STORAGE_CONTAINER_NAME = "public-data"
      PARSED_STORAGE_ACCOUNT_NAME    = var.persistence.processed_storage.account_name
      PARSED_STORAGE_CONTAINER_NAME  = "public-data-crawler"
      PUBLIC_DATA_CRAWLER_URL        = "http://public-data-crawler-service"
      SENTRY_ENVIRONMENT             = var.environment.name
      FORK_HANDLER                   = true
    }
  )
  secret = merge(
    {
      SENTRY_DSN           = local.sentry_dsn_secret_name
      ELASTIC_SECRET_TOKEN = local.elastic_secret_token_secret_name
      ELASTIC_SERVICE_URL  = local.elastic_service_url_secret_name
    }
  )
  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_storage_data_lake_gen2_filesystem" "adls" {
  name               = "public-data-crawler"
  storage_account_id = var.persistence.processed_storage.account_id
}

resource "azurerm_role_assignment" "processed_data_lake_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.parser_runner.identity_principal_id
}

resource "azurerm_role_assignment" "raw_storage_role_assignment" {
  scope                = var.persistence.raw_storage.account_id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = module.parser_runner.identity_principal_id
}

resource "azurerm_role_assignment" "storage_table_role_assignment" {
  scope                = var.persistence.raw_storage.account_id
  role_definition_name = "Storage Table Data Contributor"
  principal_id         = module.parser_runner.identity_principal_id
}
