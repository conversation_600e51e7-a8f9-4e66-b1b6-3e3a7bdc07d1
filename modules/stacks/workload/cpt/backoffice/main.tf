terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.backoffice
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                 = "cpt"
  raw_storage                          = var.persistence.raw_storage
  report_service_api_key_secret_name   = "report-service-api-key"
  dataset_manager_api_key_secret_name  = "dataset-manager-api-key"
  user_service_v2_api_key_secret_name  = "user-service-v2-api-key"
  pipeline_manager_api_key_secret_name = "pipeline-manager-api-key"
  electron_api_key_secret_name         = "electron-api-api-key"
  access_token_secret_secret_name      = "access-token-secret"
  elastic_secret_token_name            = "elastic-secret-token"
  elastic_service_url_name             = "elastic-service-url"
  backoffice_url                       = "https://${local.workload_configuration.workload_name}.${var.environment.domain}"
  dataoffice_url                       = "https://dataoffice.${var.environment.domain}"
  azure_ad_client_secret_name          = "backoffice-app-client-secret"
  auth_session_key_secret_name         = "backoffice-auth-session-key"
}

resource "azuread_application" "backoffice_app_registration" {
  display_name            = "backoffice-${var.environment.name}"
  group_membership_claims = ["All"]

  lifecycle {
    create_before_destroy = true
  }

  feature_tags {
    enterprise = true
    gallery    = false
  }

  single_page_application {
    redirect_uris = ["${local.backoffice_url}/login"]
  }

  web {
    redirect_uris = ["${local.backoffice_url}/oauth/callback"]
  }

  required_resource_access {
    resource_app_id = "00000003-0000-0000-c000-000000000000" # Microsoft Graph

    resource_access {
      id   = "e1fe6dd8-ba31-4d61-89e7-88639da4683d" # User.Read
      type = "Scope"
    }

    resource_access {
      id   = "b340eb25-3456-403f-be2f-af7a0d370277" # User.ReadBasic.All
      type = "Scope"
    }
  }

  required_resource_access {
    resource_app_id = var.user_service_identity_provider.app_id

    resource_access {
      id   = var.user_service_identity_provider.impersonate_scope_id
      type = "Scope"
    }
  }
}

resource "azuread_application_password" "backoffice_app_password" {
  application_id = azuread_application.backoffice_app_registration.id
}

module "backoffice_app_password" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.azure_ad_client_secret_name
  value           = azuread_application_password.backoffice_app_password.value
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = []
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

module "auth_session_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.auth_session_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.resource_group.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.cpt.azure_group.object_id
}

module "backoffice_app" {
  source = "../../../../components/web_app"

  resource_group_name         = azurerm_resource_group.resource_group.name
  tenant_id                   = var.common.tenant_id
  location                    = var.common.location
  secret_store_name           = var.common.secret_store_name
  env_dns_extension           = var.environment.dns_extension
  environment                 = var.environment.name
  domain                      = var.environment.domain
  cluster_oidc_issuer_url     = var.kubernetes.cluster_oidc_issuer_url
  dns_config                  = var.network.dns_config
  argocd_notifications        = var.team_configuration.argocd_notifications
  name                        = local.workload_configuration.workload_name
  kubernetes_namespace        = local.kubernetes_namespace
  kubernetes                  = local.workload_configuration.kubernetes
  docker_image                = local.workload_configuration.docker_image
  priority_class_name         = "low-priority"
  healthcheck_timeout_seconds = 5
  request_timeout_seconds     = 300 # triggering everything might take some time

  tags = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART
  is_private_webapp = true # true for VPN-only, false for public access
  firewall_policy   = { mode = "Detection" }

  config = {
    BACKOFICE_URL                  = local.backoffice_url
    DATAOFFICE_URL                 = local.dataoffice_url
    PIPELINE_MANAGER_URL           = "http://pipeline-manager-service"
    REPORT_SERVICE_URL             = "http://report-service-service"
    DATASET_MANAGER_URL            = "http://dataset-manager-service.dpt"
    USER_SERVICE_URL               = "http://user-service-v2-service"
    ELECTRON_API_URL               = "http://electron-api-service.saas"
    PUBLIC_DATA_CRAWLER_URL        = "http://public-data-crawler-service"
    BO_USER_VIEWER_GROUPS          = "[ ${join(",", [for s in local.workload_configuration.configuration.user_viewer_groups : "\\\"${s}\\\""])} ]"
    BO_USER_EDITOR_GROUPS          = "[ ${join(",", [for s in local.workload_configuration.configuration.user_editor_groups : "\\\"${s}\\\""])} ]"
    BO_USER_ACCESS_MANAGER_GROUPS  = "[ ${join(",", [for s in local.workload_configuration.configuration.user_access_manager_groups : "\\\"${s}\\\""])} ]"
    BO_REPORT_EDITOR_GROUPS        = "[ ${join(",", [for s in local.workload_configuration.configuration.report_editor_groups : "\\\"${s}\\\""])} ]"
    BO_REPORT_VIEWER_GROUPS        = "[ ${join(",", [for s in local.workload_configuration.configuration.report_viewer_groups : "\\\"${s}\\\""])} ]"
    BO_SOFT_REUPLOAD_EDITOR_GROUPS = "[ ${join(",", [for s in local.workload_configuration.configuration.soft_reupload_editor_groups : "\\\"${s}\\\""])} ]"
    BO_DATA_SHARING_VIEWER_GROUPS  = "[ ${join(",", [for s in local.workload_configuration.configuration.data_sharing_viewer_groups : "\\\"${s}\\\""])} ]"
    BO_DATA_SHARING_EDITOR_GROUPS  = "[ ${join(",", [for s in local.workload_configuration.configuration.data_sharing_editor_groups : "\\\"${s}\\\""])} ]"
    BO_SKU_VIEWER_GROUPS           = "[ ${join(",", [for s in local.workload_configuration.configuration.sku_viewer_groups : "\\\"${s}\\\""])} ]"
    BO_SKU_EDITOR_GROUPS           = "[ ${join(",", [for s in local.workload_configuration.configuration.sku_editor_groups : "\\\"${s}\\\""])} ]"
    BO_FEATURE_VIEWER_GROUPS       = "[ ${join(",", [for s in local.workload_configuration.configuration.feature_viewer_groups : "\\\"${s}\\\""])} ]"
    BO_FEATURE_EDITOR_GROUPS       = "[ ${join(",", [for s in local.workload_configuration.configuration.feature_editor_groups : "\\\"${s}\\\""])} ]"
    BO_PROCESSING_MANAGER_GROUPS   = "[ ${join(",", [for s in local.workload_configuration.configuration.processing_manager_groups : "\\\"${s}\\\""])} ]"
    BO_DASHBOARD_MANAGER_GROUPS    = "[ ${join(",", [for s in local.workload_configuration.configuration.dashboard_manager_groups : "\\\"${s}\\\""])} ]"
    BO_KEY_VALUE_EDITOR_GROUPS     = "[ ${join(",", [for s in local.workload_configuration.configuration.key_value_editor_groups : "\\\"${s}\\\""])} ]"
    BO_PARTNER_PORTAL_USER_GROUPS  = "[ ${join(",", [for s in local.workload_configuration.configuration.partner_portal_user_groups : "\\\"${s}\\\""])} ]"
    MSTEAMS_WEBHOOK                = local.workload_configuration.configuration.msteams_webhook
    SYNAPSE_DATABASE_URL           = local.workload_configuration.configuration.synapse_database_url
    PARTNER_PORTAL_URL             = local.workload_configuration.configuration.partner_portal_url
    AZURE_AD_TENANT_ID             = var.common.tenant_id
    AZURE_AD_CLIENT_ID             = azuread_application.backoffice_app_registration.client_id
    RAW_STORAGE_ACCOUNT_URL        = trim(local.raw_storage.account_blob_endpoint, "/")
    USER_SERVICE_ENTRA_APP_ID_URI  = var.user_service_identity_provider.identifier_uri
  }

  secret = {
    PIPELINE_MANAGER_KEY    = local.pipeline_manager_api_key_secret_name
    REPORT_SERVICE_KEY      = local.report_service_api_key_secret_name
    DATASET_MANAGER_KEY     = local.dataset_manager_api_key_secret_name
    USER_SERVICE_KEY        = local.user_service_v2_api_key_secret_name
    ELECTRON_API_KEY        = local.electron_api_key_secret_name
    ELECTRON_API_JWT_SECRET = local.access_token_secret_secret_name
    SCRAPER_API_JWT_SECRET  = local.access_token_secret_secret_name
    ELASTIC_SECRET_TOKEN    = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL     = local.elastic_service_url_name
    AZURE_AD_CLIENT_SECRET  = local.azure_ad_client_secret_name
    AUTH_SESSION_KEY        = local.auth_session_key_secret_name
  }
}

resource "azurerm_role_assignment" "raw_storage_role_assignment" {
  scope                = local.raw_storage.account_id
  role_definition_name = "Storage Blob Data Reader"
  principal_id         = module.backoffice_app.identity.principal_id
}
