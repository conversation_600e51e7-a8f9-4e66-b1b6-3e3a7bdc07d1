variable "common" {
  type = any
}

variable "environment" {
  type = map(any)
}

variable "team_configuration" {
  type = any
}

variable "kubernetes" {
  type = map(string)
}

variable "network" {
  type = any
}

variable "central_key_vault_id" {
  type = string
}

variable "persistence" {
  type = any
}

variable "user_service_identity_provider" {
  type = object({
    app_id               = string
    impersonate_scope_id = string
    identifier_uri       = string
  })
}
