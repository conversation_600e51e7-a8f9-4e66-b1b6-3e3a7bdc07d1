terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.pipeline_manager
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace              = "cpt"
  database                          = var.persistence.databases.pipeline_manager
  api_key_secret_name               = "${local.workload_configuration.workload_name}-api-key"
  sentry_dsn_secret_name            = "cloud-platform-team-sentry-dsn"
  elastic_secret_token_name         = "elastic-secret-token"
  elastic_service_url_name          = "elastic-service-url"
  user_access_token_public_key_name = "user-access-token-public-key"
}

resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.cpt.azure_group.object_id
}

# SQL Server
resource "random_id" "suffix" {
  byte_length = 2
}

locals {
  servicebus_namespace = "sb-data-pipeline-${var.environment.name}"
  # P10675199DT2H48M5.4775807S which is supposed to represnt never in ISO_8601 started causing  "integer overflow 
  # occurred in years" errors. Because of that we assumed that 100 years is good enough approximation of never
  iso_never_timespan = "P36500D"
}

# Service Bus

resource "azurerm_servicebus_namespace" "servicebus_namespace" {
  #checkov:skip=CKV_AZURE_199: "Ensure that Azure Service Bus uses double encryption" - we don't need double encryption
  #checkov:skip=CKV_AZURE_201: "Ensure that Azure Service Bus uses a customer-managed key to encrypt data" - we don't want to use customer-managed key
  #checkov:skip=CKV_AZURE_204: "Ensure 'public network access enabled' is set to 'False' for Azure Service Bus" - requires Premium
  name                          = local.servicebus_namespace
  location                      = azurerm_resource_group.rg.location
  resource_group_name           = azurerm_resource_group.rg.name
  sku                           = "Standard"
  local_auth_enabled            = false
  minimum_tls_version           = "1.2"
  public_network_access_enabled = true
  identity {
    type = "SystemAssigned"
  }

  tags = local.tags
}

resource "azurerm_role_assignment" "servicebus_owners_role_assignment" {
  scope                = azurerm_servicebus_namespace.servicebus_namespace.id
  role_definition_name = "Azure Service Bus Data Owner"
  principal_id         = var.common.teams.cpt.azure_group.object_id
}

resource "azurerm_servicebus_topic" "servicebus_data_jobs_topic" {
  name                = "data-jobs-topic"
  namespace_id        = azurerm_servicebus_namespace.servicebus_namespace.id
  auto_delete_on_idle = local.iso_never_timespan
  default_message_ttl = local.workload_configuration.configuration.service_bus_message_ttl
}

resource "azurerm_servicebus_topic" "servicebus_data_jobs_deadletter_topic" {
  name                = "data-jobs-deadletter-topic"
  namespace_id        = azurerm_servicebus_namespace.servicebus_namespace.id
  auto_delete_on_idle = local.iso_never_timespan
}

resource "azurerm_servicebus_subscription" "servicebus_data_jobs_deadletter_subscription" {
  name                = "data-jobs-deadletter-sub"
  topic_id            = azurerm_servicebus_topic.servicebus_data_jobs_deadletter_topic.id
  max_delivery_count  = 1
  auto_delete_on_idle = local.iso_never_timespan
}

module "pipeline_manager_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers = [
    var.common.teams.cpt.azure_group.object_id,
    var.common.teams.dpt.runner_identity.object_id,
    var.common.teams.cpt.runner_identity.object_id,
  ]
  admins = [var.common.teams.cpt.team_leader.object_id]
}

module "pipeline_manager_app" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.rg.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  tags                    = local.tags

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic/alembic.ini, upgrade, head]"
  }

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART
  is_private_webapp = true # true for VPN-only, false for public access
  firewall_policy   = { mode = "Detection" }

  config = {
    SENTRY_ENVIRONMENT                                 = var.environment.name
    SQLALCHEMY_DATABASE_URL                            = "mssql+pyodbc:///?odbc_connect=Server=${local.database.host};Database=${local.database.name}"
    SERVICE_BUS_NAME                                   = local.servicebus_namespace
    PIPELINE_MANAGER_URL                               = "http://${local.workload_configuration.workload_name}-service.cpt"
    BACKOFFICE_URL                                     = local.workload_configuration.configuration.backoffice_url
    ERROR_NOTIFICATION_MSTEAMS_WEBHOOKS                = base64encode(local.workload_configuration.configuration.error_notification_msteams_webhooks)
    INFRASTRUCTURE_ERROR_NOTIFICATION_MSTEAMS_WEBHOOKS = local.workload_configuration.configuration.infrastructure_error_notification_msteams_webhooks
    USER_ACCESS_TOKEN_PUBLIC_KEY                       = local.user_access_token_public_key_name
  }

  secret = {
    API_KEY              = local.api_key_secret_name
    SENTRY_DSN           = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL  = local.elastic_service_url_name
  }
}

# Role assignments
resource "azurerm_role_assignment" "servicebus_role_assignment" {
  scope                = azurerm_servicebus_namespace.servicebus_namespace.id
  role_definition_name = "Azure Service Bus Data Owner"
  principal_id         = module.pipeline_manager_app.identity.principal_id
}

resource "azurerm_role_assignment" "servicebus_keda_role_assignment" {
  scope                = azurerm_servicebus_namespace.servicebus_namespace.id
  role_definition_name = "Azure Service Bus Data Owner"
  principal_id         = var.kubernetes.keda_identity_id
}

# TODO: additional roles, how do we specify what level of access developers have?

module "database_provisioner" {
  source   = "../../../../components/database_provisioner"
  database = local.database
  server_admin = {
    username = "${var.persistence.sql_ad_admin.app_id}@${var.common.tenant_id}"
    password = var.persistence.sql_ad_admin.password
  }
  admins = [
    {
      object_id = module.pipeline_manager_app.identity.id
      name      = module.pipeline_manager_app.identity.name
    },
    var.common.teams.cpt.team_leader
  ]
  writers = [var.common.teams.cpt.azure_group]
  readers = [var.common.teams.dpt.azure_group, var.backup_manager]
}
