terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.public_data_crawler
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace             = "cpt"
  sentry_dsn_secret_name           = "cloud-platform-team-sentry-dsn"
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"
  proxy_list_url_secret_name       = "proxy-list-url"

  database_server_fqdn = var.persistence.postgresql_servers.pdc_postgresql_server.server_fqdn
  database_server_name = var.persistence.postgresql_servers.pdc_postgresql_server.server_name
  database_name        = var.persistence.postgresql_servers.pdc_postgresql_server.databases.public_data_crawler.name
  identity_name        = "id-${local.workload_configuration.workload_name}-${var.environment.name}"
}

resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

module "proxy_list_url" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.proxy_list_url_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}


module "public_data_crawler" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.rg.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  replicas                = local.workload_configuration.replicas

  healthcheck_timeout_seconds = 3

  tags = local.tags

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic/alembic.ini, upgrade, head]"
  }

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART
  is_private_webapp = true # true for VPN-only, false for public access
  firewall_policy   = { mode = "Detection" }

  config = {
    SENTRY_ENVIRONMENT   = var.environment.name
    DATABASE_URL         = "postgresql+asyncpg://${local.identity_name}:azure_identity_token@${local.database_server_fqdn}/${local.database_name}"
    STORAGE_ACCOUNT_NAME = var.persistence.raw_storage.account_name
    BLOB_CONTAINER_NAME  = "public-data"
    APM_ENABLED          = local.workload_configuration.configuration.apm_enabled
  }

  secret = {
    SENTRY_DSN           = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN = local.elastic_secret_token_secret_name
    ELASTIC_SERVER_URL   = local.elastic_service_url_secret_name
    PROXY_LIST_URL       = local.proxy_list_url_secret_name
  }
}

resource "azurerm_postgresql_flexible_server_active_directory_administrator" "managed_identity_admin" {
  server_name         = local.database_server_name
  resource_group_name = var.persistence.postgresql_servers.pdc_postgresql_server.server_resource_group
  tenant_id           = var.common.tenant_id
  object_id           = module.public_data_crawler.identity.principal_id
  principal_name      = module.public_data_crawler.identity.name
  principal_type      = "ServicePrincipal"
  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_role_assignment" "raw_storage_blob_role_assignment" {
  scope                = var.persistence.raw_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.public_data_crawler.identity.principal_id
}

resource "azurerm_role_assignment" "raw_storage_table_role_assignment" {
  scope                = var.persistence.raw_storage.account_id
  role_definition_name = "Storage Table Data Contributor"
  principal_id         = module.public_data_crawler.identity.principal_id
}

module "database_provisioner" {
  source = "../../../../components/postgresql_provisioner"
  database = {
    name = local.database_name
    host = local.database_server_fqdn
  }
  server_admin = var.persistence.postgresql_servers.pdc_postgresql_server.server_admin
  entra_admin  = var.common.teams.cpt.runner_identity
  admins = [
    {
      object_id = module.public_data_crawler.identity.id
      name      = module.public_data_crawler.identity.name
    },
    var.common.teams.cpt.team_leader
  ]
  entra_users = [var.common.teams.cpt.azure_group, { object_id = var.backup_manager.principal_id, name = var.backup_manager.name }]
}
