terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.partner_portal
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  partner_portal_url                    = "https://${local.workload_configuration.workload_name}.${var.environment.domain}"
  kubernetes_namespace                  = "cpt"
  entra_app_password_secret_name        = "partner-portal-app-client-secret"
  session_secret_name                   = "partner-portal-session-secret"
  bitwarden_client_secret_name          = "bitwarden-client-secret"
  bitwarden_master_password_secret_name = "bitwarden-master-password"
  report_service_api_key_secret_name    = "report-service-api-key"
  user_service_api_key_secret_name      = "user-service-v2-api-key"
  elastic_secret_token_name             = "elastic-secret-token"
  elastic_service_url_name              = "elastic-service-url"
  proxy_list_url_secret_name            = "proxy-list-url" # provisioned in public-data-crawler
  sentry_dsn_secret_name                = "cloud-platform-team-sentry-dsn"

  database_server_fqdn = var.persistence.postgresql_servers.dms_postgresql_server.server_fqdn
  database_server_name = var.persistence.postgresql_servers.dms_postgresql_server.server_name
  database_name        = var.persistence.postgresql_servers.dms_postgresql_server.databases.discount_management_system.name
  identity_name        = "id-${local.workload_configuration.workload_name}-${var.environment.name}"
}


resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "random_uuid" "partner_portal_fake_scope_id" {}

resource "azuread_application" "partner_portal_app_registration" {
  display_name            = "${local.workload_configuration.workload_name}-${var.environment.name}"
  identifier_uris         = ["api://${local.workload_configuration.workload_name}-${var.environment.name}"]
  group_membership_claims = ["All"]

  api {
    requested_access_token_version = 2

    oauth2_permission_scope {
      admin_consent_description  = "To use .default scope we need at least one scope to exist"
      admin_consent_display_name = "fake"
      enabled                    = true
      id                         = random_uuid.partner_portal_fake_scope_id.result
      type                       = "Admin"
      value                      = "fake"
    }
  }

  feature_tags {
    enterprise = true
  }

  single_page_application {
    redirect_uris = ["${local.partner_portal_url}/login"]
  }

  web {
    redirect_uris = ["${local.partner_portal_url}/oauth/callback"]
  }

  optional_claims {
    access_token {
      name = "email"
    }
  }

  required_resource_access {
    resource_app_id = "00000003-0000-0000-c000-000000000000" # Microsoft Graph

    resource_access {
      id   = "e1fe6dd8-ba31-4d61-89e7-88639da4683d" # User.Read
      type = "Scope"
    }

    resource_access {
      id   = "64a6cdd6-aab1-4aaf-94b8-3cc8405e90d0" # email
      type = "Scope"
    }

    resource_access {
      id   = "5c28f0bf-8a70-41f1-8ab2-9032436ddb65" # Files.ReadWrite
      type = "Scope"
    }

    resource_access {
      id   = "863451e7-0667-486c-a5d6-d135439485f0" # Files.ReadWrite.All
      type = "Scope"
    }

    resource_access {
      id   = "89fe6a52-be36-487e-b7d8-d061c450a026" # Sites.ReadWrite.All
      type = "Scope"
    }
  }

  required_resource_access {
    resource_app_id = "cfa8b339-82a2-471a-a3c9-0fc0be7a4093" # Azure Key Vault
    resource_access {
      id   = "f53da476-18e3-4152-8e01-aec403e6edc0" # user_impersonate
      type = "Scope"
    }
  }
}

# allow access from az login in Microsoft Azure CLI
resource "azuread_application_pre_authorized" "pre_authorized_azure_cli_partner_portal" {
  application_id       = azuread_application.partner_portal_app_registration.id
  authorized_client_id = "04b07795-8ddb-461a-bbee-02f9e1bf7b46"
  permission_ids       = [random_uuid.partner_portal_fake_scope_id.result]
}

# allow access from az login in Microsoft Azure PowerShell
resource "azuread_application_pre_authorized" "pre_authorized_power_shell_partner_portal" {
  application_id       = azuread_application.partner_portal_app_registration.id
  authorized_client_id = "1950a258-227b-4e31-a9cf-717495945fc2"
  permission_ids       = [random_uuid.partner_portal_fake_scope_id.result]
}

resource "azuread_application_password" "partner_portal_app_password" {
  application_id = azuread_application.partner_portal_app_registration.id
}


ephemeral "random_password" "session_secret_key" {
  length  = 64
  special = false
}

module "partner_portal_app_password" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.entra_app_password_secret_name
  value           = azuread_application_password.partner_portal_app_password.value
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = []
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

module "partner_portal_session_secret" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.session_secret_name
  value_wo        = ephemeral.random_password.session_secret_key.result
  write_only      = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = []
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

module "bitwarden_client_secret" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.bitwarden_client_secret_name
  placeholder     = true
  key_vault_id    = var.persistence.client_credentials_key_vault.id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = []
  admins          = [var.common.teams.cpt.team_leader.object_id, var.common.teams.cpt.azure_group.object_id]
}

module "bitwarden_master_password" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.bitwarden_master_password_secret_name
  placeholder     = true
  key_vault_id    = var.persistence.client_credentials_key_vault.id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = []
  admins          = [var.common.teams.cpt.team_leader.object_id, var.common.teams.cpt.azure_group.object_id]
}

module "partner_portal" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.rg.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  replicas                = local.workload_configuration.replicas

  healthcheck_timeout_seconds                 = 3
  liveness_healthcheck_initial_delay_seconds  = 60
  readiness_healthcheck_initial_delay_seconds = 60

  # TODO: remove it once we fix healthcheck issues
  liveness_healthcheck_failure_threshold  = 6
  liveness_healthcheck_period_seconds     = 20
  readiness_healthcheck_failure_threshold = 6
  readiness_healthcheck_period_seconds    = 20

  tags = local.tags

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic/alembic.ini, upgrade, head]"
  }

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  is_private_webapp = true
  firewall_policy   = { mode = "Detection" }

  # this is to allow long-running streamed responses for SSE 
  request_timeout_seconds = 3600 # 1 hr

  config = {
    DATABASE_URL        = "postgresql+asyncpg://${local.identity_name}:azure_identity_token@${local.database_server_fqdn}/${local.database_name}"
    DISCOUNT_SETTER_URL = local.partner_portal_url
    ENTRA_CLIENT_ID     = azuread_application.partner_portal_app_registration.client_id
    # after migration rename this in DS to sth more clear e.g. credentials key vault url
    VAULT_URL                               = var.persistence.client_credentials_key_vault.url
    USER_ACTION_TEAMS_NOTIFICATION_WEBHOOKS = replace(jsonencode(local.workload_configuration.configuration.user_action_teams_notification_webhooks), "\"", "\\\"")
    REPORT_SERVICE_URL                      = "http://report-service-service.cpt"
    USER_SERVICE_URL                        = "http://user-service-v2-service.cpt"
    DISCOUNT_MANAGEMENT_SYSTEM_URL          = "http://partner-portal-service.cpt/dms"
    BITWARDEN_CLIENT_ID                     = local.workload_configuration.configuration.bitwarden_client_id
    BITWARDEN_BASE_COLLECTION               = local.workload_configuration.configuration.bitwarden_base_collection
    STORAGE_ACCOUNT_NAME                    = var.persistence.partner_portal_storage.account_name
    STORAGE_CONTAINER_NAME                  = "partner-portal"
    RAW_DISCOUNTS_FILES                     = replace(jsonencode(local.workload_configuration.configuration.raw_discounts_files), "\"", "\\\"")
    ORGANIZATION_OVERRIDE                   = local.workload_configuration.configuration.organization_override
  }

  secret = {
    SENTRY_DSN             = local.sentry_dsn_secret_name
    ENTRA_CLIENT_SECRET    = local.entra_app_password_secret_name
    SESSION_SECRET_KEY     = local.session_secret_name
    REPORT_SERVICE_API_KEY = local.report_service_api_key_secret_name
    USER_SERVICE_API_KEY   = local.user_service_api_key_secret_name
    ELASTIC_SECRET_TOKEN   = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL    = local.elastic_service_url_name
    PROXY_LIST_URL         = local.proxy_list_url_secret_name
  }
}

resource "azurerm_role_assignment" "storage_account_blob_writer" {
  scope                = var.persistence.partner_portal_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.partner_portal.identity.principal_id
}

resource "azurerm_role_assignment" "storage_account_table_writer" {
  scope                = var.persistence.partner_portal_storage.account_id
  role_definition_name = "Storage Table Data Contributor"
  principal_id         = module.partner_portal.identity.principal_id
}

resource "azurerm_role_assignment" "partner_portal_creds_kv_access" {
  scope                = var.persistence.client_credentials_key_vault.id
  role_definition_name = "Key Vault Secrets Officer"
  principal_id         = module.partner_portal.identity.principal_id
}

module "database_provisioner" {
  source = "../../../../components/postgresql_provisioner"
  database = {
    name = local.database_name
    host = local.database_server_fqdn
  }
  server_admin = var.persistence.postgresql_servers.dms_postgresql_server.server_admin
  entra_admin  = var.common.teams.cpt.runner_identity
  admins = [
    {
      object_id = module.partner_portal.identity.id
      name      = module.partner_portal.identity.name
    },
    var.common.teams.cpt.team_leader
  ]
  entra_users = [var.common.teams.cpt.azure_group, { object_id = var.backup_manager.principal_id, name = var.backup_manager.name }]
}
