terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

resource "kubernetes_namespace" "team_ns" {
  metadata {
    name = var.common.teams.cpt.name
  }
}

resource "helm_release" "team_project" {
  name      = "${var.common.teams.cpt.name}-project"
  chart     = "../components/kubernetes-resource"
  namespace = var.common.teams.cpt.name

  values = [
    <<-EOF
configurations:
  - ${indent(
    4,
    templatefile("../components/templates/project.yaml.tpl",
      {
        namespace  = var.common.teams.cpt.name
        team_group = var.common.teams.cpt.azure_group.object_id
      }
    )
)}
  EOF
]

depends_on = [kubernetes_namespace.team_ns]
}

module "cloud_platform_team_sentry_dsn" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "cloud-platform-team-sentry-dsn"
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}


locals {
  pipeline_manager = {
    url                 = "http://pipeline-manager-service"
    api_key_secret_name = "pipeline-manager-api-key"
  }
}

module "sql_backup" {
  source               = "./sql_backup"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  kubernetes           = var.kubernetes
  central_key_vault_id = var.central_key_vault_id
  persistence          = var.persistence

  service_bus = module.pipeline_manager.service_bus

  pipeline_manager = local.pipeline_manager

  depends_on = [kubernetes_namespace.team_ns]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "pipeline_manager" {
  source               = "./pipeline_manager"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  backup_manager       = module.sql_backup.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "report_service" {
  source               = "./report_service"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  backup_manager       = module.sql_backup.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}


module "backoffice" {
  source                         = "./backoffice"
  common                         = var.common
  environment                    = var.environment
  team_configuration             = var.team_configuration
  persistence                    = var.persistence
  kubernetes                     = var.kubernetes
  network                        = var.network
  central_key_vault_id           = var.central_key_vault_id
  user_service_identity_provider = module.user_service_v2.user_service_identity_provider

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "user_service_v2" {
  source               = "./user_service_v2"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  backup_manager       = module.sql_backup.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "reprocessing_trigger" {
  source               = "./reprocessing_trigger"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  kubernetes           = var.kubernetes
  central_key_vault_id = var.central_key_vault_id

  service_bus = module.pipeline_manager.service_bus

  pipeline_manager = local.pipeline_manager

  depends_on = [kubernetes_namespace.team_ns]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "parser_runner" {
  source               = "./parser_runner"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  kubernetes           = var.kubernetes
  central_key_vault_id = var.central_key_vault_id
  persistence          = var.persistence

  service_bus = module.pipeline_manager.service_bus

  pipeline_manager = local.pipeline_manager

  depends_on = [kubernetes_namespace.team_ns]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "public_data_crawler" {
  source               = "./public_data_crawler"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  backup_manager       = module.sql_backup.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}


module "partner_portal" {
  source               = "./partner_portal"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  persistence          = var.persistence
  backup_manager       = module.sql_backup.backup_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "partner_tasks_executor" {
  source               = "./partner_tasks_executor"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  kubernetes           = var.kubernetes
  central_key_vault_id = var.central_key_vault_id
  persistence          = var.persistence

  service_bus = module.pipeline_manager.service_bus

  pipeline_manager = local.pipeline_manager

  depends_on = [kubernetes_namespace.team_ns]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
