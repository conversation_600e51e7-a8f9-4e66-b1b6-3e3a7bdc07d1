terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
    tls = {
      source  = "hashicorp/tls"
      version = "4.1.0"
    }
  }
}

locals {
  workload_configuration = var.team_configuration.user_service_v2
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                           = "cpt"
  api_key_secret_name                            = "${local.workload_configuration.workload_name}-api-key"
  database                                       = var.persistence.databases.user_service_v2
  pipeline_manager_api_key_secret_name           = "pipeline-manager-api-key"
  report_service_api_key_secret_name             = "report-service-api-key"
  mailchimp_api_key_secret_name                  = "mailchimp-transactional-api-key"
  mailchimp_marketing_api_key_secret_name        = "mailchimp-marketing-api-key"
  mailchimp_registered_users_list_id_secret_name = "mailchimp-registered-users-list-id"
  user_access_token_private_key_name             = "user-access-token-private-key"
  user_access_token_public_key_name              = "user-access-token-public-key"
  user_refresh_token_key_name                    = "user-refresh-token-key"
  elastic_secret_token_name                      = "elastic-secret-token"
  elastic_service_url_name                       = "elastic-service-url"
  sentry_dsn_secret_name                         = "cloud-platform-team-sentry-dsn"
  entra_app_id_uri                               = "api://user-service-${var.environment.name}"
}

resource "random_uuid" "user_impersonate_scope_id" {}
resource "random_uuid" "user_impersonate_role_id" {}

resource "azuread_application" "user_service_app_registration" {
  display_name    = "user-service-${var.environment.name}"
  identifier_uris = [local.entra_app_id_uri]
  owners          = [var.common.teams.cpt.team_leader.object_id]

  api {
    requested_access_token_version = 2

    oauth2_permission_scope {
      admin_consent_description  = "This scope allows for impersonation of any user"
      admin_consent_display_name = "User.Impersonate"
      enabled                    = true
      id                         = random_uuid.user_impersonate_scope_id.result
      type                       = "Admin"
      value                      = "User.Impersonate"
    }
  }

  app_role {
    allowed_member_types = ["User", "Application"]
    description          = "This role allows for impersonation of any user"
    display_name         = "USER_SERVICE_IMPERSONATE"
    enabled              = true
    id                   = random_uuid.user_impersonate_role_id.result
    value                = "USER_SERVICE_IMPERSONATE"
  }

  feature_tags {
    enterprise = true
  }

  # for OpenAPI authenticaiton in /docs
  single_page_application {
    redirect_uris = ["https://${local.workload_configuration.workload_name}.${var.environment.domain}/oauth2-redirect"]
  }

  optional_claims {
    access_token {
      name = "email"
    }
  }

  required_resource_access {
    resource_app_id = "00000003-0000-0000-c000-000000000000" # Microsoft Graph

    resource_access {
      id   = "e1fe6dd8-ba31-4d61-89e7-88639da4683d" # User.Read
      type = "Scope"
    }

    resource_access {
      id   = "64a6cdd6-aab1-4aaf-94b8-3cc8405e90d0" # email
      type = "Scope"
    }
  }
}

# allow access from az login in Microsoft Azure CLI
# more: https://learn.microsoft.com/en-us/troubleshoot/azure/entra/entra-id/governance/verify-first-party-apps-sign-in
resource "azuread_application_pre_authorized" "pre_authorized_azure_cli" {
  application_id       = azuread_application.user_service_app_registration.id
  authorized_client_id = "04b07795-8ddb-461a-bbee-02f9e1bf7b46"
  permission_ids       = [random_uuid.user_impersonate_scope_id.result]
}

# allow access from az login in Microsoft Azure PowerShell
# more: https://learn.microsoft.com/en-us/troubleshoot/azure/entra/entra-id/governance/verify-first-party-apps-sign-in
resource "azuread_application_pre_authorized" "pre_authorized_power_shell" {
  application_id       = azuread_application.user_service_app_registration.id
  authorized_client_id = "1950a258-227b-4e31-a9cf-717495945fc2"
  permission_ids       = [random_uuid.user_impersonate_scope_id.result]
}

resource "azuread_service_principal" "user_service_enterprise_app" {
  client_id                    = azuread_application.user_service_app_registration.client_id
  app_role_assignment_required = true
}

# assign impersonate role to CPT team 
resource "azuread_app_role_assignment" "user_impersonate_role_assignment" {
  app_role_id         = random_uuid.user_impersonate_role_id.result
  principal_object_id = var.common.teams.cpt.azure_group.object_id
  resource_object_id  = azuread_service_principal.user_service_enterprise_app.object_id
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

module "api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

ephemeral "tls_private_key" "user_jwt_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

module "user_jwt_private_key" {
  source           = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name             = local.user_access_token_private_key_name
  value_wo         = ephemeral.tls_private_key.user_jwt_key.private_key_pem
  value_wo_version = 2
  write_only       = true
  key_vault_id     = var.central_key_vault_id
  expiration_date  = var.common.keys_secrets_expiration_date
  readers          = []
  admins           = [var.common.teams.cpt.team_leader.object_id]
}

module "user_jwt_public_key" {
  source           = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name             = local.user_access_token_public_key_name
  value_wo         = ephemeral.tls_private_key.user_jwt_key.public_key_pem
  value_wo_version = 2
  write_only       = true
  key_vault_id     = var.central_key_vault_id
  expiration_date  = var.common.keys_secrets_expiration_date
  readers          = [var.common.teams.cpt.azure_group.object_id]
  admins           = [var.common.teams.cpt.team_leader.object_id]
}

module "refresh_token_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.user_refresh_token_key_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

module "mailchimp_registered_users_list_id" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.mailchimp_registered_users_list_id_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

module "user_service_v2_app" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.resource_group.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  priority_class_name     = "high-priority"
  tags                    = local.tags

  before_deployment = {
    command = "alembic"
    args    = "[--config, alembic/alembic.ini, upgrade, head]"
  }

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART
  is_private_webapp = true # true for VPN-only, false for public access
  firewall_policy   = { mode = "Detection" }

  config = {
    DATABASE_URL                                   = "mssql+pyodbc:///?odbc_connect=Server=${local.database.host};Database=${local.database.name}"
    STORAGE_ACCOUNT_NAME                           = var.persistence.user_service_key_file_storage_account.account_name
    ENTRA_APPLICATION_ID                           = azuread_application.user_service_app_registration.client_id
    ENTRA_APPLICATION_ID_URI                       = local.entra_app_id_uri
    ALLOW_IMPERSONATE_ACCESS_TOKEN_EXPIRY_OVERRIDE = title(tostring(local.workload_configuration.configuration.allow_impersonate_access_token_expiry_override))
  }
  secret = {
    API_KEY                            = local.api_key_secret_name
    PIPELINE_MANAGER_API_KEY           = local.pipeline_manager_api_key_secret_name
    REPORT_SERVICE_API_KEY             = local.report_service_api_key_secret_name
    MAILCHIMP_API_KEY                  = local.mailchimp_api_key_secret_name
    MAILCHIMP_MARKETING_API_KEY        = local.mailchimp_marketing_api_key_secret_name
    MAILCHIMP_REGISTERED_USERS_LIST_ID = local.mailchimp_registered_users_list_id_secret_name
    ELASTIC_SECRET_TOKEN               = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL                = local.elastic_service_url_name
    USER_ACCESS_TOKEN_PRIVATE_KEY      = local.user_access_token_private_key_name
    USER_REFRESH_TOKEN_KEY             = local.user_refresh_token_key_name
    SENTRY_DSN                         = local.sentry_dsn_secret_name
  }
}

resource "azurerm_role_assignment" "raw_storage_role_assignment" {
  scope                = var.persistence.user_service_key_file_storage_account.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.user_service_v2_app.identity.principal_id
}

module "database_provisioner" {
  source   = "../../../../components/database_provisioner"
  database = local.database
  server_admin = {
    username = "${var.persistence.sql_ad_admin.app_id}@${var.common.tenant_id}"
    password = var.persistence.sql_ad_admin.password
  }
  admins = [
    {
      object_id = module.user_service_v2_app.identity.id
      name      = module.user_service_v2_app.identity.name
    },
    var.common.teams.cpt.team_leader
  ]
  writers = [var.common.teams.cpt.azure_group]
  readers = [var.common.service_principals.dbw_indiebi, var.common.teams.dpt.team_leader, var.backup_manager]
}

resource "terraform_data" "masked_column_admin" {
  triggers_replace = {
    database_name = local.database.name
    identity_id   = module.user_service_v2_app.identity.id
  }
  provisioner "local-exec" {
    command = <<EOT
sqlcmd --authentication-method=ActiveDirectoryServicePrincipal -d ${local.database.name} \
-Q "GRANT ALTER ANY MASK TO [${module.user_service_v2_app.identity.name}];GRANT UNMASK TO [${module.user_service_v2_app.identity.name}];"
EOT
    environment = {
      SQLCMDSERVER   = local.database.host
      SQLCMDUSER     = "${var.persistence.sql_ad_admin.app_id}@${var.common.tenant_id}"
      SQLCMDPASSWORD = var.persistence.sql_ad_admin.password
    }
  }
}
