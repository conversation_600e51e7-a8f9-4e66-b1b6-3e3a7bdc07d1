output "user_service_identity_provider" {
  value = {
    app_id               = azuread_application.user_service_app_registration.client_id
    object_id            = azuread_service_principal.user_service_enterprise_app.object_id
    impersonate_scope_id = random_uuid.user_impersonate_scope_id.result
    impersonate_role_id  = random_uuid.user_impersonate_role_id.result
    identifier_uri       = local.entra_app_id_uri
  }
}
