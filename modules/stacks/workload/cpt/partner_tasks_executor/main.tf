terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.partner_tasks_executor
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace               = "cpt"
  sentry_dsn_secret_name             = "cloud-platform-team-sentry-dsn"
  report_service_api_key_secret_name = "report-service-api-key"
  user_service_api_key_secret_name   = "user-service-v2-api-key"
  elastic_secret_token_name          = "elastic-secret-token"
  elastic_service_url_name           = "elastic-service-url"
  proxy_list_url_secret_name         = "proxy-list-url" # provisioned in public data crawler
}

module "partner_tasks_executor" {
  name                    = "partner-tasks-executor"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  max_duration = 10800
  evictable    = false

  config = {
    FORK_HANDLER = true
    # TODO: after migration rename this in DS to sth more clear e.g. credentials key vault url
    VAULT_URL                                      = var.persistence.client_credentials_key_vault.url
    PLAYSTATION_EVENTS_TEAMS_NOTIFICATION_WEBHOOKS = replace(jsonencode(local.workload_configuration.configuration.playstation_events_teams_notification_webhooks), "\"", "\\\"")
    UNASSIGNED_SKUS_TEAMS_NOTIFICATION_WEBHOOKS    = replace(jsonencode(local.workload_configuration.configuration.unassigned_skus_teams_notification_webhooks), "\"", "\\\"")
    ERRORS_WEBHOOKS                                = replace(jsonencode(local.workload_configuration.configuration.errors_webhooks), "\"", "\\\"")
    REPORT_SERVICE_URL                             = "http://report-service-service.cpt"
    PARTNER_PORTAL_URL                             = "http://partner-portal-service.cpt"
    USER_SERVICE_URL                               = "http://user-service-v2-service.cpt"
    DISCOUNT_MANAGEMENT_SYSTEM_URL                 = "http://partner-portal-service.cpt/dms"
    STORAGE_ACCOUNT_NAME                           = var.persistence.partner_portal_storage.account_name
    STORAGE_CONTAINER_NAME                         = "partner-portal"
    BACKOFFICE_URL                                 = local.workload_configuration.configuration.backoffice_url
    BITWARDEN_CLIENT_ID                            = local.workload_configuration.configuration.bitwarden_client_id
    BITWARDEN_BASE_COLLECTION                      = local.workload_configuration.configuration.bitwarden_base_collection
    ORGANIZATION_OVERRIDE                          = local.workload_configuration.configuration.organization_override
  }

  secret = {
    REPORT_SERVICE_API_KEY     = local.report_service_api_key_secret_name
    USER_SERVICE_API_KEY       = local.user_service_api_key_secret_name
    ELASTIC_SECRET_TOKEN       = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL        = local.elastic_service_url_name
    SENTRY_DSN                 = local.sentry_dsn_secret_name
    proxy_list_url_secret_name = "proxy-list-url"
  }
  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_role_assignment" "storage_account_blob_writer" {
  scope                = var.persistence.partner_portal_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.partner_tasks_executor.identity_principal_id
}

resource "azurerm_role_assignment" "storage_account_table_writer" {
  scope                = var.persistence.partner_portal_storage.account_id
  role_definition_name = "Storage Table Data Contributor"
  principal_id         = module.partner_tasks_executor.identity_principal_id
}

resource "azurerm_role_assignment" "partner_portal_creds_kv_access" {
  scope                = var.persistence.client_credentials_key_vault.id
  role_definition_name = "Key Vault Secrets Officer"
  principal_id         = module.partner_tasks_executor.identity_principal_id
}
