terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

resource "kubernetes_namespace" "team_ns" {
  metadata {
    name = var.common.teams.saas.name
  }
}

resource "helm_release" "team_project" {
  name      = "${var.common.teams.saas.name}-project"
  chart     = "../components/kubernetes-resource"
  namespace = var.common.teams.saas.name

  values = [
    <<-EOF
configurations:
  - ${indent(
    4,
    templatefile("../components/templates/project.yaml.tpl",
      {
        namespace  = var.common.teams.saas.name
        team_group = var.common.teams.saas.azure_group.object_id
      }
    )
)}
  EOF
]

depends_on = [kubernetes_namespace.team_ns]
}

module "saas_team_sentry_dsn" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "saas-team-sentry-dsn"
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}



module "electron_api" {
  source               = "./electron_api"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "discounts_service" {
  source               = "./discounts_service"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "landing" {
  source               = "./landing"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "saas_monitoring" {
  count                = var.team_configuration.saas_monitoring != null ? 1 : 0
  source               = "./saas_monitoring"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
module "events_planner" {
  source               = "./events_planner"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "desktop_app_web" {
  source               = "./desktop_app_web"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "mobile" {
  count                = var.team_configuration.mobile != null ? 1 : 0
  source               = "./mobile"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  network              = var.network
  central_key_vault_id = var.central_key_vault_id

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

locals {
  service_bus_rg         = "rg-pipeline-manager-${var.environment.name}"
  service_bus_namespace  = "sb-data-pipeline-${var.environment.name}"
  service_bus_topic_name = "data-jobs-topic"

  pipeline_manager = {
    url                 = "http://pipeline-manager-service.cpt"
    api_key_secret_name = "pipeline-manager-api-key"
  }
  pipeline_manager_service_bus = {
    namespace_id          = "/subscriptions/${var.environment.subscription_id}/resourceGroups/${local.service_bus_rg}/providers/Microsoft.ServiceBus/namespaces/${local.service_bus_namespace}"
    namespace             = local.service_bus_namespace
    topic_id              = "/subscriptions/${var.environment.subscription_id}/resourceGroups/${local.service_bus_rg}/providers/Microsoft.ServiceBus/namespaces/${local.service_bus_namespace}/topics/${local.service_bus_topic_name}"
    topic_name            = local.service_bus_topic_name
    deadletter_topic_name = "data-jobs-deadletter-topic"
  }
}


module "saas_gold" {
  source               = "./saas_gold"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "direct_data_access_gold" {
  source               = "./direct_data_access_gold"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.team_configuration
  persistence          = var.persistence
  kubernetes           = var.kubernetes
  network              = var.network
  central_key_vault_id = var.central_key_vault_id
  service_bus          = local.pipeline_manager_service_bus
  pipeline_manager     = local.pipeline_manager

  depends_on = [
    kubernetes_namespace.team_ns
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
