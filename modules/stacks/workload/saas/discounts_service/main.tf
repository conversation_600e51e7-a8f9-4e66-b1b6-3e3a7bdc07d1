terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.discounts_service
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                         = "saas"
  report_service_api_key_secret_name           = "report-service-api-key"
  user_service_v2_api_key_secret_name          = "user-service-v2-api-key"
  dataset_manager_api_key_secret_name          = "dataset-manager-api-key"
  pipeline_manager_api_key_secret_name         = "pipeline-manager-api-key"
  electron_api_key_secret_name                 = "electron-api-api-key"
  api_key_secret_name                          = "discounts-service-api-key"
  refresh_token_secret_secret_name             = "refresh-token-secret"
  access_token_secret_secret_name              = "access-token-secret"
  mailchimp_transactional_api_key_secret_name  = "mailchimp-transactional-api-key"
  pbi_defs_connection_string_secret_name       = "pbi-defs-connection-string"
  electron_store_connection_string_secret_name = "electron-store-connection-string"
  intercom_verification_secret_name            = "intercom-verification-key"
  events_service_api_key_secret_name           = "events-service-api-key"
  chargebee_api_key_secret_name                = "chargebee-api-key"
  elastic_secret_token_name                    = "elastic-secret-token"
  elastic_service_url_name                     = "elastic-service-url"
  user_access_token_public_key_name            = "user-access-token-public-key"
  sentry_dsn_secret_name                       = "discounts-service-sentry-dsn"
  sentry_auth_token_secret_name                = "discounts-service-sentry-auth-token"
}

resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.saas.azure_group.object_id
}

module "discounts_service_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.api_key_secret_name
  autogenerated   = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "discounts_service_sentry_dsn" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.sentry_dsn_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "discounts_service_sentry_auth_token" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.sentry_auth_token_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id, var.common.teams.saas.runner_identity.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "discounts_service_app" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.rg.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  priority_class_name     = "normal-priority"
  tags                    = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART

  server_port       = 3000 //this is mapped to PORT accessible in pod
  healthcheck_path  = "/health/ping"
  replicas          = 1
  is_private_webapp = true
  firewall_policy   = { mode = "Detection" }

  config = {
    ENV                                      = local.workload_configuration.configuration.env
    DATASET_MANAGER_URI                      = "http://dataset-manager-service.dpt"
    EVENTS_SERVICE_URI                       = "http://events-service-service.dpt"
    USER_SERVICE_V2_URI                      = "http://user-service-v2-service.cpt"
    REPORT_SERVICE_V2_URI                    = "http://report-service-service.cpt"
    PIPELINE_MANAGER_URI                     = "http://pipeline-manager-service.cpt"
    PBI_DEFS_CONTAINER_NAME                  = "indiebi-desktop-definitions"
    ELECTRON_CONFIG_CONTAINER_NAME           = "electron-app-config"
    ELECTRON_APP_DISTRIBUTION_CONTAINER_NAME = "electron-app-distribution"
    DISCOUNTS_MASTER_FILE_PATH               = local.workload_configuration.configuration.discounts_master_file_path
    # To be deleted after migration to auth in US. Any string -> True; empty string -> False
    NEW_JWT_ENABLED = "True"
  }

  secret = {
    REPORT_SERVICE_V2_KEY            = local.report_service_api_key_secret_name
    EVENTS_SERVICE_KEY               = local.events_service_api_key_secret_name
    USER_SERVICE_V2_KEY              = local.user_service_v2_api_key_secret_name
    DATASET_MANAGER_KEY              = local.dataset_manager_api_key_secret_name
    PIPELINE_MANAGER_KEY             = local.pipeline_manager_api_key_secret_name
    ELECTRON_API_KEY                 = local.electron_api_key_secret_name
    API_KEY                          = local.api_key_secret_name
    REFRESH_TOKEN_SECRET             = local.refresh_token_secret_secret_name
    ACCESS_TOKEN_SECRET              = local.access_token_secret_secret_name
    MAILCHIMP_TRANSACTIONAL_API_KEY  = local.mailchimp_transactional_api_key_secret_name
    PBI_DEFS_CONNECTION_STRING       = local.pbi_defs_connection_string_secret_name
    ELECTRON_STORE_CONNECTION_STRING = local.electron_store_connection_string_secret_name
    INTERCOM_VERIFICATION_SECRET_KEY = local.intercom_verification_secret_name
    CHARGEBEE_API_KEY                = local.chargebee_api_key_secret_name
    ELASTIC_SECRET_TOKEN             = local.elastic_secret_token_name
    ELASTIC_SERVICE_URL              = local.elastic_service_url_name
    USER_ACCESS_TOKEN_PUBLIC_KEY     = local.user_access_token_public_key_name
    SENTRY_DSN                       = local.sentry_dsn_secret_name
    SENTRY_AUTH_TOKEN                = local.sentry_auth_token_secret_name
  }
}
