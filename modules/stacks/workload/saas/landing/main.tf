terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.landing
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace                         = "saas"
  mailchimp_marketing_api_key_secret_name      = "mailchimp-marketing-api-key"
  mailchimp_transactional_api_key_secret_name  = "mailchimp-transactional-api-key"
  mailchimp_list_id_secret_name                = "mailchimp-list-id"
  mailchimp_region_secret_name                 = "mailchimp-region"
  landing_google_tag_manager_id_name           = "landing-google-tag-manager-id"
  landing_google_tag_manager_query_params_name = "landing-google-tag-manager-query-params"
  intercom_electron_app_id_secret_name         = "intercom-electron-app-id"
}

resource "azurerm_resource_group" "rg" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "azurerm_role_assignment" "data_contributor_role" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "[INDIEBI] Data Contributor"
  principal_id         = var.common.teams.saas.azure_group.object_id
}

locals {
  placeholder_secret_value = "IM_A_PLACEHOLDER_CHANGE_ME"
}

# TODO: move api keys to user service, since it's using them now
module "mailchimp_transactional_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.mailchimp_transactional_api_key_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "mailchimp_marketing_api_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.mailchimp_marketing_api_key_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "mailchimp_list_id" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.mailchimp_list_id_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "mailchimp_region" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.mailchimp_region_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}


module "intercom_electron_app_id" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.intercom_electron_app_id_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}


module "landing_google_tag_manager_id" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.landing_google_tag_manager_id_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "landing_google_tag_manager_query_params" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.landing_google_tag_manager_query_params_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "landing_app" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.rg.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  custom_dns_entries      = local.workload_configuration.network.custom_dns_entries
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  priority_class_name     = "high-priority"
  tags                    = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART

  server_port                = 8080
  healthcheck_path           = "/health/ping"
  is_private_webapp          = false || var.environment.is_private
  private_routes_allowed_ips = [var.common.firewall_public_ip_address]

  config = {
    ENV                        = local.workload_configuration.configuration.env
    NUXT_ELECTRON_API_URL      = "http://electron-api-service"
    NUXT_PUBLIC_USER_GUIDE_URL = local.workload_configuration.configuration.external_urls.user_guide
    NUXT_PUBLIC_DISCORD_URL    = local.workload_configuration.configuration.external_urls.discord
    NUXT_PUBLIC_SITE_ENV       = local.workload_configuration.configuration.nuxt_public_site_env
  }

  secret = {
    NUXT_PUBLIC_GTM_ID                   = local.landing_google_tag_manager_id_name
    NUXT_PUBLIC_GTM_QUERY_PARAMS         = local.landing_google_tag_manager_query_params_name
    NUXT_PUBLIC_INTERCOM_ELECTRON_APP_ID = local.intercom_electron_app_id_secret_name
  }
}
