terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.events_planner
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace          = "saas"
  sentry_dsn_secret_name        = "discount-advisor-sentry-dsn"
  sentry_auth_token_secret_name = "discount-advisor-sentry-auth-token"
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

module "discount_advisor_sentry_dsn" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.sentry_dsn_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "discount_advisor_sentry_auth_token" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.sentry_auth_token_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id, var.common.teams.saas.runner_identity.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "event_planner" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.resource_group.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  priority_class_name     = "low-priority"
  tags                    = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  # MOST LIKELY YOU WILL ONLY WANT TO MODIFY THIS PART

  healthcheck_path = "/"
  server_port      = 8080

  # add this section if it's a web app accessible from outside of AKS
  is_private_webapp          = false || var.environment.is_private # true for VPN-only, false for public access
  private_routes_allowed_ips = [var.common.firewall_public_ip_address]
  firewall_policy = {
    overrides = [
      {
        rule_group_name = "REQUEST-920-PROTOCOL-ENFORCEMENT"
        rule_ids        = ["920320"]
      },
      {
        rule_group_name = "REQUEST-942-APPLICATION-ATTACK-SQLI"
        rule_ids        = ["942440"]
      },
    ]
  }
  config = {
    ENV = local.workload_configuration.configuration.env
  }

  secret = {
    SENTRY_DSN        = local.sentry_dsn_secret_name
    SENTRY_AUTH_TOKEN = local.sentry_auth_token_secret_name
  }
}
