terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.mobile
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

resource "random_password" "password" {
  length      = 32
  min_special = 1
  min_upper   = 1
  min_lower   = 1
  min_numeric = 1

  keepers = {
    expiration_date = var.common.keys_secrets_expiration_date
  }
}

module "basic_auth_password" {
  count           = (var.environment.is_private && local.workload_configuration.configuration.sku == "Standard") ? 1 : 0
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${local.workload_configuration.workload_name}-basic-auth-password"
  value           = random_password.password.result
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers = [
    var.common.teams.saas.azure_group.object_id,
    var.common.teams.saas.runner_identity.object_id
  ]
  admins = [var.common.teams.saas.team_leader.object_id]
}

module "electron_api_url" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${local.workload_configuration.workload_name}-electron-api-url"
  value           = "https://electron-api${var.environment.dns_extension != null ? var.environment.dns_extension : ""}.${var.environment.domain}"
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers = [
    var.common.teams.saas.azure_group.object_id,
    var.common.teams.saas.runner_identity.object_id
  ]
  admins = [var.common.teams.saas.team_leader.object_id]
}

resource "azurerm_static_web_app" "mobile_app" {
  name                = "stapp-mobile-${var.environment.name}"
  resource_group_name = azurerm_resource_group.resource_group.name
  location            = azurerm_resource_group.resource_group.location
  sku_tier            = local.workload_configuration.configuration.sku
  sku_size            = local.workload_configuration.configuration.sku
  tags                = local.tags

  dynamic "basic_auth" {
    for_each = (var.environment.is_private && local.workload_configuration.configuration.sku == "Standard") ? [1] : []
    content {
      password     = module.basic_auth_password[0].secret_value
      environments = "AllEnvironments"
    }
  }
}

module "mobile_app_deployment_token" {
  source = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"

  name         = "${local.workload_configuration.workload_name}-deployment-token"
  key_vault_id = var.central_key_vault_id
  value        = azurerm_static_web_app.mobile_app.api_key
  admins       = [var.common.teams.saas.team_leader.object_id]
  readers = [
    var.common.teams.saas.azure_group.object_id,
    var.common.teams.saas.runner_identity.object_id,
    var.common.teams.cpt.azure_group.object_id
  ]
  expiration_date = var.common.keys_secrets_expiration_date
}

resource "azurerm_role_assignment" "runner_identity_role_assignment" {
  scope                = azurerm_static_web_app.mobile_app.id
  role_definition_name = "Contributor"
  principal_id         = var.common.teams.saas.runner_identity.object_id

}

resource "azurerm_role_assignment" "saas_role_assignment" {
  scope                = azurerm_static_web_app.mobile_app.id
  role_definition_name = "Contributor"
  principal_id         = var.common.teams.saas.azure_group.object_id
}

resource "azurerm_dns_cname_record" "dns_cname_record" {
  name                = "${local.workload_configuration.workload_name}${var.environment.dns_extension != null ? var.environment.dns_extension : ""}"
  zone_name           = var.network.dns_config.dns_zone_name
  resource_group_name = var.network.dns_config.dns_zone_resource_group_name
  ttl                 = 300
  record              = azurerm_static_web_app.mobile_app.default_host_name
  tags                = local.tags

  provider = azurerm.connectivity
}

resource "azurerm_private_dns_cname_record" "private_dns_cname_record" {
  name                = "${local.workload_configuration.workload_name}${var.environment.dns_extension != null ? var.environment.dns_extension : ""}"
  zone_name           = var.network.dns_config.dns_zone_name
  resource_group_name = var.network.dns_config.dns_zone_resource_group_name
  ttl                 = 300
  record              = azurerm_static_web_app.mobile_app.default_host_name
  tags                = local.tags

  provider = azurerm.connectivity
}

resource "azurerm_static_web_app_custom_domain" "custom_domain" {
  static_web_app_id = azurerm_static_web_app.mobile_app.id
  domain_name       = "${azurerm_dns_cname_record.dns_cname_record.name}.${azurerm_dns_cname_record.dns_cname_record.zone_name}"
  validation_type   = "cname-delegation"
}
