terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.saas_gold
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace             = "saas"
  sentry_dsn_secret_name           = "saas-team-sentry-dsn"
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"

  config = {
    # READER_CFG, WRITER_CFG, SILVER and GOLD are old configs to be removed after migration to core silver
    READER_CFG__SOURCE_TYPE    = "dls"
    READER_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    READER_CFG__CONTAINER_NAME = "processed-data"
    READER_CFG__BASE_DIR       = "processed-reports"

    WRITER_CFG__SOURCE_TYPE    = "dls"
    WRITER_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    WRITER_CFG__CONTAINER_NAME = "saas-gold"
    WRITER_CFG__BASE_DIR       = ""

    SILVER__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    SILVER__CONTAINER_NAME = "processed-data"
    SILVER__BASE_DIR       = "processed-reports"

    GOLD__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    GOLD__CONTAINER_NAME = "saas-gold"
    GOLD__BASE_DIR       = ""

    DIRECT_DATA_ACCESS_CFG__TYPE           = "dls"
    DIRECT_DATA_ACCESS_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    DIRECT_DATA_ACCESS_CFG__CONTAINER_NAME = "saas-gold-direct-data-access"
    DIRECT_DATA_ACCESS_CFG__BASE_DIR       = "data"
    DIRECT_DATA_ACCESS_CFG__FILE_EXTENSION = "csv"

    INPUT_CFG__TYPE           = "dls"
    INPUT_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    INPUT_CFG__CONTAINER_NAME = "core-silver"
    INPUT_CFG__BASE_DIR       = "result"

    PBI_OUTPUT_CFG__TYPE           = "dls"
    PBI_OUTPUT_CFG__ACCOUNT_NAME   = var.persistence.processed_storage.account_name
    PBI_OUTPUT_CFG__CONTAINER_NAME = "saas-gold"
    PBI_OUTPUT_CFG__BASE_DIR       = "data"

    SENTRY_ENVIRONMENT = var.environment.name
    FORK_HANDLER       = true
  }
  secret = {
    SENTRY_DSN           = local.sentry_dsn_secret_name
    ELASTIC_SECRET_TOKEN = local.elastic_secret_token_secret_name
    ELASTIC_SERVICE_URL  = local.elastic_service_url_secret_name
  }
}

module "saas_gold" {
  name                    = "saas-gold"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace
  max_duration            = 7200

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.kubernetes

  config = local.config
  secret = local.secret

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_storage_data_lake_gen2_filesystem" "adls" {
  name               = "saas-gold"
  storage_account_id = var.persistence.processed_storage.account_id
}

resource "azurerm_storage_data_lake_gen2_filesystem" "dda_dls" {
  name               = "saas-gold-direct-data-access"
  storage_account_id = var.persistence.processed_storage.account_id
}

resource "azurerm_role_assignment" "processed_data_lake_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.saas_gold.identity_principal_id
}

module "saas_gold_heavy" {
  name                    = "saas-gold-heavy"
  source                  = "../../../../components/data_job"
  environment             = var.environment.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  argocd_notifications    = var.team_configuration.argocd_notifications
  kubernetes_namespace    = local.kubernetes_namespace
  max_duration            = 7200

  docker_image = local.workload_configuration.docker_image
  kubernetes   = local.workload_configuration.heavy_slot.kubernetes

  config = local.config
  secret = local.secret

  service_bus      = var.service_bus
  pipeline_manager = var.pipeline_manager
  tags             = local.tags
}

resource "azurerm_role_assignment" "processed_data_lake_heavy_role_assignment" {
  scope                = var.persistence.processed_storage.account_id
  role_definition_name = "Storage Blob Data Contributor"
  principal_id         = module.saas_gold_heavy.identity_principal_id
}

locals {
  tables_path = "${path.module}/../_shared_tables"
  tables      = setsubtract(fileset(local.tables_path, "*.parquet"), ["dim_date.parquet"])
  csv_tables  = setsubtract(fileset(local.tables_path, "*.csv"), ["dim_date.csv"])
}

resource "azurerm_storage_blob" "shared_table" {
  for_each               = local.tables
  name                   = "shared-tables/${each.value}"
  storage_account_name   = var.persistence.processed_storage.account_name
  storage_container_name = azurerm_storage_data_lake_gen2_filesystem.adls.name
  type                   = "Block"
  source                 = "${local.tables_path}/${each.value}"
  content_md5            = filemd5("${local.tables_path}/${each.value}")
}

resource "azurerm_storage_blob" "shared_table_dda" {
  for_each               = local.csv_tables
  name                   = "shared-tables/${each.value}"
  storage_account_name   = var.persistence.processed_storage.account_name
  storage_container_name = azurerm_storage_data_lake_gen2_filesystem.dda_dls.name
  type                   = "Block"
  source                 = "${local.tables_path}/${each.value}"
  content_md5            = filemd5("${local.tables_path}/${each.value}")
}
