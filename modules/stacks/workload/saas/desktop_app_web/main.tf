terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

locals {
  workload_configuration = var.team_configuration.desktop_app_web
  tags = merge(
    var.team_configuration.tags,
    {
      workload = local.workload_configuration.workload_name
    }
  )
  kubernetes_namespace          = "saas"
  sentry_dsn_secret_name        = "electron-app-web-sentry-dsn"
  sentry_auth_token_secret_name = "electron-app-web-sentry-auth-token"
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${local.workload_configuration.workload_name}-${var.environment.name}"
  location = var.common.location
  tags     = local.tags
}

module "desktop_app_web_sentry_dsn" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.sentry_dsn_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}

module "desktop_app_web_sentry_auth_token" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.sentry_auth_token_secret_name
  placeholder     = true
  key_vault_id    = var.central_key_vault_id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.saas.azure_group.object_id, var.common.teams.saas.runner_identity.object_id]
  admins          = [var.common.teams.saas.team_leader.object_id]
}


module "desktop_app_web_app" {
  source = "../../../../components/web_app"

  resource_group_name     = azurerm_resource_group.resource_group.name
  tenant_id               = var.common.tenant_id
  location                = var.common.location
  secret_store_name       = var.common.secret_store_name
  env_dns_extension       = var.environment.dns_extension
  environment             = var.environment.name
  domain                  = var.environment.domain
  cluster_oidc_issuer_url = var.kubernetes.cluster_oidc_issuer_url
  dns_config              = var.network.dns_config
  argocd_notifications    = var.team_configuration.argocd_notifications
  name                    = local.workload_configuration.workload_name
  custom_dns_entries      = local.workload_configuration.network.custom_dns_entries
  kubernetes_namespace    = local.kubernetes_namespace
  kubernetes              = local.workload_configuration.kubernetes
  docker_image            = local.workload_configuration.docker_image
  tags                    = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }

  healthcheck_path = "/health/info"
  server_port      = 8080

  is_private_webapp          = var.environment.is_private # true for VPN-only, false for public access
  private_routes_allowed_ips = [var.common.firewall_public_ip_address]

  config = {
    ENV = local.workload_configuration.configuration.env
  }

  secret = {
    SENTRY_DSN        = local.sentry_dsn_secret_name
    SENTRY_AUTH_TOKEN = local.sentry_auth_token_secret_name
  }
}
