terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }

    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }

    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.0.3"
    }
  }
}

locals {
  registry_configuration = {
    gitlab = {
      username = var.gitlab_username
      password = var.gitlab_password
    }
  }
  tags = merge(
    { stack = "compute" },
    var.cloud_platform_team_configuration.tags
  )
}

module "compute" {
  source                   = "./compute"
  common                   = var.common
  environment              = var.environment
  aks_configuration        = var.aks_configuration
  infra_admins             = var.infra_admins
  infra_readers            = var.infra_readers
  argocd_configuration     = var.argocd_configuration
  prometheus_configuration = var.prometheus_configuration
  top_dns_zone             = var.top_dns_zone
  vnet                     = var.vnet
  private_dns_zone_ids     = var.private_dns_zone_ids

  registry_configuration = local.registry_configuration
  tags                   = local.tags

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}


moved {
  from = module.indiebi_eso
  to   = module.compute.module.k8s.module.indiebi_eso
}

locals {
  sql_ad_admin_with_password = merge(
    { password = var.sql_ad_admin_password },
    var.sql_ad_admin
  )
}

module "persistence" {
  source                          = "./persistence"
  common                          = var.common
  environment                     = var.environment
  infra_admins                    = var.infra_admins
  infra_readers                   = var.infra_readers
  configuration                   = var.persistence_configuration
  network_subnet_id               = module.compute.network.subnet_id
  postgresql_subnet_id            = module.compute.network.postgresql_subnet_id
  grafana_sql_user_password       = module.compute.kubernetes.grafana_sql_user_password
  private_dns_zone_ids            = var.private_dns_zone_ids
  central_key_vault_id            = module.compute.central_key_vault.id
  client_credentials_vault_admins = var.client_credentials_vault_admins

  sql_ad_admin = local.sql_ad_admin_with_password

  tags = merge(
    { stack = "persistence" },
    var.cloud_platform_team_configuration.tags
  )
  depends_on = [module.compute]

  providers = {
    azurerm            = azurerm
    azurerm.management = azurerm.management
  }
}

locals {
  elastic_secret_token_secret_name = "elastic-secret-token"
  elastic_service_url_secret_name  = "elastic-service-url"
}

module "elastic_secret_token_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.elastic_secret_token_secret_name
  placeholder     = true
  key_vault_id    = module.compute.central_key_vault.id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

module "elastic_service_url_key" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = local.elastic_service_url_secret_name
  placeholder     = true
  key_vault_id    = module.compute.central_key_vault.id
  expiration_date = var.common.keys_secrets_expiration_date
  readers         = [var.common.teams.cpt.azure_group.object_id]
  admins          = [var.common.teams.cpt.team_leader.object_id]
}

module "cloud_platform_team" {
  source               = "./workload/cpt"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.cloud_platform_team_configuration
  persistence          = module.persistence
  kubernetes           = module.compute.kubernetes
  network              = module.compute.network
  central_key_vault_id = module.compute.central_key_vault.id

  depends_on = [
    module.compute,
    module.persistence
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "data_platform_team" {
  source                         = "./workload/dpt"
  common                         = var.common
  environment                    = var.environment
  team_configuration             = var.data_platform_team_configuration
  persistence                    = module.persistence
  kubernetes                     = module.compute.kubernetes
  network                        = module.compute.network
  central_key_vault_id           = module.compute.central_key_vault.id
  private_dns_zone_ids           = var.private_dns_zone_ids
  user_service_identity_provider = module.cloud_platform_team.user_service_identity_provider
  backup_manager                 = module.cloud_platform_team.backup_manager

  depends_on = [
    module.compute,
    module.persistence,
    module.cloud_platform_team
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "saas_team" {
  source               = "./workload/saas"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.saas_team_configuration
  persistence          = module.persistence
  kubernetes           = module.compute.kubernetes
  network              = module.compute.network
  central_key_vault_id = module.compute.central_key_vault.id

  depends_on = [
    module.compute,
    module.persistence,
    module.cloud_platform_team,
    module.data_platform_team
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}

module "ppt_team" {
  source               = "./workload/ppt"
  common               = var.common
  environment          = var.environment
  team_configuration   = var.partner_program_team_configuration
  persistence          = module.persistence
  kubernetes           = module.compute.kubernetes
  network              = module.compute.network
  central_key_vault_id = module.compute.central_key_vault.id

  depends_on = [
    module.compute,
    module.persistence,
    module.cloud_platform_team,
    module.data_platform_team
  ]

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm.connectivity
  }
}
