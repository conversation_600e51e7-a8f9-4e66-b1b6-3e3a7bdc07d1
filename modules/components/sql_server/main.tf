terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
  }
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${var.name}-${var.environment}"
  location = var.location
  tags     = var.tags
}

module "sql_admin_password" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${var.name}-sql-admin-password"
  autogenerated   = true
  key_vault_id    = var.key_vault_id
  expiration_date = var.server_password_expiration_date
}

resource "azurerm_mssql_server" "sqlserver" {
  #checkov:skip=CKV_AZURE_24: "Ensure that 'Auditing' Retention is 'greater than 90 days' for SQL servers" - to lower the costs, we are OK with shorter period for now
  #checkov:skip=CKV_AZURE_113: "Ensure that SQL server disables public network access" - temporarily needed by databricks
  name                         = "sql-${var.name}-${var.environment}"
  resource_group_name          = azurerm_resource_group.resource_group.name
  location                     = azurerm_resource_group.resource_group.location
  version                      = "12.0"
  administrator_login          = "indiebi_admin"
  administrator_login_password = module.sql_admin_password.secret_value
  minimum_tls_version          = "1.2"
  tags                         = var.tags

  azuread_administrator {
    login_username = var.sql_ad_admin.login_name
    object_id      = var.sql_ad_admin.app_id
  }

  primary_user_assigned_identity_id = var.sql_ad_admin.server_identity_id
  identity {
    type         = "UserAssigned"
    identity_ids = [var.sql_ad_admin.server_identity_id]
  }

  lifecycle {
    prevent_destroy = true
  }

  # TODO: use default until we figure out why databricks doesn't work with this
  # connection_policy = "Redirect"
  # TODO: temporarily set to true because databricks cannot access it, set to false once fixed
  public_network_access_enabled = true
}

resource "random_id" "suffix" {
  byte_length = 2
}

resource "azurerm_storage_account" "vulnerability_assessment_scans" {
  #checkov:skip=CKV_AZURE_33: "Ensure Storage logging is enabled for Queue service for read, write and delete requests" - we don't use Queue service
  #checkov:skip=CKV_AZURE_59: "Ensure that Storage accounts disallow public access" - requires GitLab runner in Azure
  #checkov:skip=CKV_AZURE_206: "Ensure that Storage Accounts use replication" - we don't need replication on this account
  #checkov:skip=CKV2_AZURE_1: "Ensure storage for critical data are encrypted with Customer Managed Key" - we don't want use our own key for now
  #checkov:skip=CKV2_AZURE_38: "Ensure soft-delete is enabled on Azure storage account" - we don't need soft-delete on this account
  #checkov:skip=CKV2_AZURE_40: "Ensure storage account is not configured with Shared Key authorization" - terraform needs SAS for provisioning
  # To allow GitLab Runner access, add Storage Data Blob Contributor role to appropriate Service Principal
  name                            = replace("st${substr(var.name, 0, 8)}va${random_id.suffix.hex}${substr(var.environment, 0, 8)}", "-", "")
  resource_group_name             = azurerm_resource_group.resource_group.name
  location                        = azurerm_resource_group.resource_group.location
  account_tier                    = "Standard"
  account_replication_type        = "LRS"
  min_tls_version                 = "TLS1_2"
  shared_access_key_enabled       = true
  allow_nested_items_to_be_public = false
  local_user_enabled              = false
  tags                            = merge(var.tags, { "skip-backup" : "true" })

  sas_policy {
    expiration_period = "07.00:00:00"
  }
}

resource "azurerm_storage_container" "va_scans_container" {
  #checkov:skip=CKV2_AZURE_21: "Ensure Storage logging is enabled for Blob service for read requests" - we don't need storage logging for this container
  name                  = "va-scans-sql-server"
  storage_account_name  = azurerm_storage_account.vulnerability_assessment_scans.name
  container_access_type = "private"
}

resource "azurerm_mssql_server_extended_auditing_policy" "auditing_policy" {
  server_id                               = azurerm_mssql_server.sqlserver.id
  storage_endpoint                        = azurerm_storage_account.vulnerability_assessment_scans.primary_blob_endpoint
  storage_account_access_key              = azurerm_storage_account.vulnerability_assessment_scans.primary_access_key
  storage_account_access_key_is_secondary = false
  retention_in_days                       = 30
}

resource "azurerm_mssql_server_security_alert_policy" "security_alert_policy" {
  resource_group_name  = azurerm_resource_group.resource_group.name
  server_name          = azurerm_mssql_server.sqlserver.name
  state                = "Enabled"
  email_addresses      = [var.admin_contact_email]
  email_account_admins = true
}

resource "azurerm_mssql_server_vulnerability_assessment" "server_vulnerability_assessment" {
  server_security_alert_policy_id = azurerm_mssql_server_security_alert_policy.security_alert_policy.id
  storage_container_path          = "${azurerm_storage_account.vulnerability_assessment_scans.primary_blob_endpoint}${azurerm_storage_container.va_scans_container.name}/"
  storage_account_access_key      = azurerm_storage_account.vulnerability_assessment_scans.primary_access_key

  recurring_scans {
    enabled                   = true
    email_subscription_admins = true
    emails                    = [var.admin_contact_email]
  }
}

resource "azurerm_mssql_firewall_rule" "allow_azure_services" {
  #checkov:skip=CKV2_AZURE_34: "	Ensure Azure SQL server firewall is not overly permissive - TODO"
  name             = "AllowAzureServices"
  server_id        = azurerm_mssql_server.sqlserver.id
  start_ip_address = "0.0.0.0"
  end_ip_address   = "0.0.0.0"
}

locals {
  databases = {
    for setting in var.databases : setting.workload_name => {
      sku_name    = setting.sku_name,
      max_size_gb = setting.max_size_gb
    }
  }
}

resource "azurerm_mssql_database" "databases" {
  #checkov:skip=CKV_AZURE_224: "Ensure that the Ledger feature is enabled on database that requires cryptographic proof and nonrepudiation of data integrity"
  #                            we don't want extra cost for now
  #checkov:skip=CKV_AZURE_229: "Ensure the Azure SQL Database Namespace is zone redundant" - only available for Premium SKUs
  for_each  = local.databases
  name      = "db-${replace(each.key, "_", "-")}"
  server_id = azurerm_mssql_server.sqlserver.id
  # to find the list of available tiers run `az sql db list-editions -l <location> -o table`
  sku_name    = each.value.sku_name
  max_size_gb = each.value.max_size_gb
  tags        = var.tags

  storage_account_type = "Geo"

  lifecycle {
    prevent_destroy = true
  }
}

resource "null_resource" "grafana_user_provisioner" {
  for_each = toset([for database in azurerm_mssql_database.databases : database.name])
  triggers = {
    grafana_user_password = var.grafana_sql_user_password
  }
  provisioner "local-exec" {
    command = "sqlcmd -d ${each.value} -Q \"${file("${path.module}/setup_grafana_user.sql")}\""
    environment = {
      SQLCMDSERVER         = azurerm_mssql_server.sqlserver.fully_qualified_domain_name
      SQLCMDUSER           = azurerm_mssql_server.sqlserver.administrator_login
      SQLCMDPASSWORD       = azurerm_mssql_server.sqlserver.administrator_login_password
      GRAFANA_SQL_PASSWORD = var.grafana_sql_user_password
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

# VA2065: Server-level firewall rules should be tracked and maintained at a strict minimum
resource "azurerm_mssql_database_vulnerability_assessment_rule_baseline" "firewall_baseline" {
  for_each                           = azurerm_mssql_database.databases
  server_vulnerability_assessment_id = azurerm_mssql_server_vulnerability_assessment.server_vulnerability_assessment.id
  database_name                      = each.value.name
  baseline_name                      = "master"
  rule_id                            = "VA2065"

  baseline_result {
    result = [
      "AllowAzureServices",
      "0.0.0.0",
      "0.0.0.0"
    ]
  }
}

resource "azurerm_private_endpoint" "sql_server_private_endpoint" {
  name                = "pep-${var.name}"
  resource_group_name = azurerm_resource_group.resource_group.name
  location            = var.location
  subnet_id           = var.subnet_id
  tags                = var.tags

  private_service_connection {
    name                           = "pep-${var.name}"
    private_connection_resource_id = azurerm_mssql_server.sqlserver.id
    is_manual_connection           = false
    subresource_names              = ["SqlServer"]
  }

  private_dns_zone_group {
    name                 = "privatelink.database.windows.net"
    private_dns_zone_ids = [var.private_dns_zone_ids.sql_server]
  }
}

resource "azurerm_private_endpoint" "storage_account_private_endpoint" {
  name                = "pep-${azurerm_storage_account.vulnerability_assessment_scans.name}"
  location            = azurerm_resource_group.resource_group.location
  resource_group_name = azurerm_resource_group.resource_group.name
  subnet_id           = var.subnet_id
  tags                = var.tags

  private_service_connection {
    name                           = "psc-${azurerm_storage_account.vulnerability_assessment_scans.name}"
    private_connection_resource_id = azurerm_storage_account.vulnerability_assessment_scans.id
    is_manual_connection           = false
    subresource_names              = ["blob"]
  }

  private_dns_zone_group {
    name                 = "privatelink.blob.core.windows.net"
    private_dns_zone_ids = [var.private_dns_zone_ids.blob]
  }
}
