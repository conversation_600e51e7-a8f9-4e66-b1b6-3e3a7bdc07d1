output "databases" {
  value = {
    for workload_name, database in azurerm_mssql_database.databases : workload_name => {
      name                               = database.name
      host                               = azurerm_mssql_server.sqlserver.fully_qualified_domain_name
      server_vulnerability_assessment_id = azurerm_mssql_server_vulnerability_assessment.server_vulnerability_assessment.id
      admin_password                     = module.sql_admin_password.secret_value
    }
  }
  sensitive = true
}
