variable "environment" {
  description = "Name of environment. It will be used by all cluster related resources by postfixing their names e.g. `-dev`"
  type        = string
  default     = "dev"
}


variable "location" {
  description = "Location of cluster and all related resources"
  type        = string
  default     = "West Europe"
}

variable "name" {
  description = "Name of the server that will be created."
  type        = string
}

variable "key_vault_id" {
  description = "ID of the Key Vault used for storing secret values"
  type        = string
}


variable "sql_ad_admin" {
  description = <<EOT
  Credential for the SQL Entra ID principal that will be configured as a server admin

  {
    login_name: "Principal name"
    app_id: "Application ID assigned to the user"
    password: "Principal client secret"
    server_identity_id: "Managed Identity ID that will be assigned to the server"
  }
  EOT
  type = object({
    login_name         = string
    app_id             = string
    password           = string
    server_identity_id = string
  })
}

variable "tenant_id" {
  description = "Azure Tenant ID"
  type        = string
}

variable "admin_contact_email" {
  description = "E-mail address used for vulnerability scans notifications and alerts"
  type        = string
}

variable "tags" {
  type = map(string)
}

variable "databases" {
  description = <<EOT
  List of databases that will be provisioned on the server

  {
    workload_name: "Name of the workload that will use the database"
    sku_name: "SKU of the Azure SQL Database. To find the list of available tiers run `az sql db list-editions -l <location> -o table`"
    max_size_gb: "Maximum size of the database"
  }
  EOT

  type = list(object({
    workload_name = string
    sku_name      = string
    max_size_gb   = number
  }))
}


variable "grafana_sql_user_password" {
  description = "Password that will be used for provisioned Grafana SQL user"
  type        = string
}

variable "subnet_id" {
  description = "ID of the subnet where private endpoint will be configured"
  type        = string
}

variable "private_dns_zone_ids" {
  description = "Ids of private DNS zones for SQL Server and Storage Account"
  type = object({
    sql_server = string
    blob       = string
  })
}

variable "server_password_expiration_date" {
  description = "Date when Key Vault secret containing SQL admin server password expires"
  type        = string
}