terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
  }
}

resource "null_resource" "schema_identity_provisioner" {
  triggers = {
    # In case of problem with creating DB users (the most likely on first deployment) We should bump this value to force creating users once again
    retry         = 0
    database_name = var.database.name
  }
  provisioner "local-exec" {
    command = "sqlcmd --authentication-method=ActiveDirectoryServicePrincipal -d ${var.database.name} -Q \"${
    file("../components/database_provisioner/create_schema.sql.tpl")}\""
    environment = {
      SQLCMDSERVER   = var.database.host
      SQLCMDUSER     = var.server_admin.username
      SQLCMDPASSWORD = var.server_admin.password
    }
  }
}

resource "null_resource" "reader_identity_provisioner" {
  for_each = { for identity in var.readers : identity.object_id => identity }
  triggers = {
    # In case of problem with creating DB users (the most likely on first deployment) We should bump this value to force creating users once again
    retry         = 0
    database_name = var.database.name
    identity_id   = each.value.object_id
  }
  provisioner "local-exec" {
    command = "sqlcmd --authentication-method=ActiveDirectoryServicePrincipal -d ${var.database.name} -Q \"${
    templatefile("../components/database_provisioner/add_reader_user.sql.tpl", { identity = each.value.name })}\""
    environment = {
      SQLCMDSERVER   = var.database.host
      SQLCMDUSER     = var.server_admin.username
      SQLCMDPASSWORD = var.server_admin.password
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "null_resource" "writer_identity_provisioner" {
  for_each = { for identity in var.writers : identity.object_id => identity }
  triggers = {
    # In case of problem with creating DB users (the most likely on first deployment) We should bump this value to force creating users once again
    retry         = 0
    database_name = var.database.name
    identity_id   = each.value.object_id
  }
  provisioner "local-exec" {
    command = "sqlcmd --authentication-method=ActiveDirectoryServicePrincipal -d ${var.database.name} -Q \"${
    templatefile("../components/database_provisioner/add_writer_user.sql.tpl", { identity = each.value.name })}\""
    environment = {
      SQLCMDSERVER   = var.database.host
      SQLCMDUSER     = var.server_admin.username
      SQLCMDPASSWORD = var.server_admin.password
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "null_resource" "admin_identity_provisioner" {
  for_each = { for identity in var.admins : identity.object_id => identity }
  triggers = {
    # In case of problem with creating DB users (the most likely on first deployment) We should bump this value to force creating users once again
    retry         = 0
    database_name = var.database.name
    identity_id   = each.value.object_id
  }
  provisioner "local-exec" {
    command = "sqlcmd --authentication-method=ActiveDirectoryServicePrincipal -d ${var.database.name} -Q \"${
    templatefile("../components/database_provisioner/add_admin_user.sql.tpl", { identity = each.value.name })}\""
    environment = {
      SQLCMDSERVER   = var.database.host
      SQLCMDUSER     = var.server_admin.username
      SQLCMDPASSWORD = var.server_admin.password
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

# VA2108: Minimal set of principals should be members of fixed high impact database roles
resource "azurerm_mssql_database_vulnerability_assessment_rule_baseline" "db_admin_baseline" {
  server_vulnerability_assessment_id = var.database.server_vulnerability_assessment_id
  database_name                      = var.database.name
  rule_id                            = "VA2108"
  dynamic "baseline_result" {
    for_each = { for identity in var.admins : identity.object_id => identity }
    content {
      result = [
        baseline_result.value.name,
        "db_ddladmin",
        "EXTERNAL_USER",
        "EXTERNAL"
      ]
    }
  }
}

# VA2109: Minimal set of principals should be members of fixed low impact database roles
resource "azurerm_mssql_database_vulnerability_assessment_rule_baseline" "db_user_baseline" {
  server_vulnerability_assessment_id = var.database.server_vulnerability_assessment_id
  database_name                      = var.database.name
  rule_id                            = "VA2109"
  baseline_result {
    result = [
      "grafana",
      "db_datareader",
      "SQL_USER",
      "DATABASE"
    ]
  }
  dynamic "baseline_result" {
    for_each = { for identity in concat(var.admins, var.writers) : identity.object_id => identity }
    content {
      result = [
        baseline_result.value.name,
        "db_datawriter",
        "EXTERNAL_USER",
        "EXTERNAL"
      ]
    }
  }
  dynamic "baseline_result" {
    for_each = { for identity in concat(var.admins, var.writers, var.readers) : identity.object_id => identity }
    content {
      result = [
        baseline_result.value.name,
        "db_datareader",
        "EXTERNAL_USER",
        "EXTERNAL"
      ]
    }
  }
}
