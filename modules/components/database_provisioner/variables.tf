variable "database" {
  description = <<EOT
  Database information
  
  {
    name: "Name of the database for which baseline will be configured"
    host: "Database host"
    server_vulnerability_assessment_id: "ID of the SQL Server Vulnerability assigned to the server that contains the database"
  }
  EOT
  type = object({
    name                               = string
    host                               = string
    server_vulnerability_assessment_id = string
  })
}

variable "server_admin" {
  description = <<EOT
  Credentials of the database server admin
  
  {
    username: "Admin username"
    password: "Admin password"
  }
  EOT
  type = object({
    username = string
    password = string
  })
  sensitive = true

  validation {
    condition     = can(regex("^[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}@[[:xdigit:]]{8}-[[:xdigit:]]{4}-4[[:xdigit:]]{3}-[89ABab][[:xdigit:]]{3}-[[:xdigit:]]{12}$", var.server_admin.username))
    error_message = "Admin username needs to be in the form: 'principal_app_id@tenant_id'"
  }
}

variable "admins" {
  description = <<EOT
  Identities or Service principals for which admin user will be created

  list({
    id: "Identity/Service principal ID"
    name: "Name of the identity or service principal
  })
  EOT
  type = list(object({
    object_id = string
    name      = string
  }))
  default = []
}

variable "writers" {
  description = <<EOT
  Identities or Service principals for which writer user will be created

  list({
    id: "Identity/Service principal ID"
    name: "Name of the identity or service principal
  })
  EOT
  type = list(object({
    object_id = string
    name      = string
  }))
  default = []
}

variable "readers" {
  description = <<EOT
  Identities or Service principals for which reader user will be created

  list({
    id: "Identity/Service principal ID"
    name: "Name of the identity or service principal
  })
  EOT
  type = list(object({
    object_id = string
    name      = string
  }))
  default = []
}
