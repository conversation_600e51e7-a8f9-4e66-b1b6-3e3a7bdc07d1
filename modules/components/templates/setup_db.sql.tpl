IF EXISTS (SELECT 1 FROM sys.database_principals WHERE name = '${identity}')
BEGIN
    DROP USER [${identity}];
END
CREATE USER [${identity}] FROM EXTERNAL PROVIDER;
EXEC sp_addrolemember 'db_ddladmin', '${identity}';
EXEC sp_addrolemember 'db_datawriter', '${identity}';
EXEC sp_addrolemember 'db_datareader', '${identity}';

IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = N'WebApp')
BEGIN
    EXEC('CREATE SCHEMA [WebApp] AUTHORIZATION [dbo]')
END