apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: ${namespace}
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: Project for ${namespace} team
  sourceRepos:
    - '*'
  destinations:
    - namespace: ${namespace}
      server: https://kubernetes.default.svc
  # Deny all cluster-scoped resources from being created, except for Namespace
  clusterResourceWhitelist:
    - group: ''
      kind: Namespace
  roles: []
