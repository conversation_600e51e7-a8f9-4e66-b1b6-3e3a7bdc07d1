apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${name}
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
  annotations:
    # this points to a mutable tag in the container registry that will tracked by updater
    argocd-image-updater.argoproj.io/image-list: ${name}=${image_name}:${image_tag}
    argocd-image-updater.argoproj.io/${name}.update-strategy: digest
    argocd-image-updater.argoproj.io/${name}.force-update: "true"
    %{~ for notification_type, notification_channel in argocd_notifications ~}
    notifications.argoproj.io/subscribe.on-${notification_type}.teams: ${notification_channel}
    %{~ endfor ~}
spec:
  # projects map to teams which map to namespaces
  project: ${namespace}
  # Multiple sources bug: https://github.com/argoproj-labs/argocd-image-updater/pull/548
  source:
    chart: ${chart_name}
    repoURL: https://gitlab.com/api/v4/projects/44953895/packages/helm/stable
    targetRevision: ${chart_revision}
    helm:
      parameters:
        %{~ for param_name, param_value in parameters ~}
        %{~ if param_value != "" && param_value != null ~}
        - name: "${param_name}"
          value: "${param_value}"
        %{~ endif ~}
        %{~ endfor ~}
        %{~ for array_param_name, array_param_value in array_parameters ~}
        %{~ for id, param_value in split(" ", array_param_value)}
        - name: "${array_param_name}[${id}]"
          value: "${param_value}"
        %{~ endfor ~}
        %{~ endfor ~}
  destination:
    server: "https://kubernetes.default.svc"
    namespace: ${namespace}
  syncPolicy:
    %{~ if auto_sync_enabled ~}
    automated:
      selfHeal: true
      prune: true
    %{~ endif ~}
    syncOptions:
      - PruneLast=true