terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }

    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
  }
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${var.name}-${var.environment}"
  location = var.location
  tags     = var.tags

  # this avoids cycles when removing module instances
  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_user_assigned_identity" "identity" {
  resource_group_name = azurerm_resource_group.resource_group.name
  name                = "id-${var.name}-${var.environment}"
  location            = var.location
  tags                = var.tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_role_assignment" "servicebus_receiver_role_assignment" {
  scope                = var.service_bus["namespace_id"]
  role_definition_name = "Azure Service Bus Data Receiver"
  principal_id         = azurerm_user_assigned_identity.identity.principal_id

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_role_assignment" "servicebus_sender_role_assignment" {
  scope                = var.service_bus["namespace_id"]
  role_definition_name = "Azure Service Bus Data Sender"
  principal_id         = azurerm_user_assigned_identity.identity.principal_id

  lifecycle {
    create_before_destroy = true
  }
}

locals {
  # P10675199DT2H48M5.4775807S which is supposed to represnt never in ISO_8601 started causing  "integer overflow 
  # occurred in years" errors. Because of that we assumed that 100 years is good enough approximation of never
  iso_never_period        = "P36500D"
  iso_two_weeks_period    = "P14D"
  iso_five_minutes_period = "PT5M"
}

resource "azurerm_servicebus_subscription" "servicebus_data_jobs_subscription" {
  name                                 = "${var.name}-sub"
  topic_id                             = var.service_bus["topic_id"]
  max_delivery_count                   = 1
  auto_delete_on_idle                  = local.iso_never_period
  default_message_ttl                  = local.iso_two_weeks_period
  dead_lettering_on_message_expiration = true
  forward_dead_lettered_messages_to    = var.service_bus["deadletter_topic_name"]
  lock_duration                        = local.iso_five_minutes_period

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_servicebus_subscription_rule" "servicebus_data_jobs_sub_filter" {
  name            = "${var.name}-filter"
  subscription_id = azurerm_servicebus_subscription.servicebus_data_jobs_subscription.id
  filter_type     = "SqlFilter"
  sql_filter      = "user.data_job_type = '${replace(var.name, "-", "_")}'"

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_federated_identity_credential" "federated_identity_credential" {
  name                = "fic-${var.name}-${var.environment}"
  resource_group_name = azurerm_resource_group.resource_group.name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.identity.id
  subject             = "system:serviceaccount:${var.kubernetes_namespace}:${var.name}-service-account"

  lifecycle {
    create_before_destroy = true
  }
}

resource "helm_release" "argocd-app" {
  name        = "${var.name}-argocd-app"
  chart       = "../components/kubernetes-resource"
  namespace   = var.kubernetes_namespace
  max_history = 5

  lifecycle {
    create_before_destroy = true
  }
  values = [
    <<-EOF
configurations:
  - ${indent(4, templatefile("../components/templates/app.yaml.tpl",
    {
      environment          = var.environment,
      name                 = var.name
      namespace            = var.kubernetes_namespace
      chart_name           = "data-job"
      chart_revision       = var.chart_revision
      image_name           = var.docker_image["name"]
      image_tag            = var.docker_image["tag"]
      argocd_notifications = var.argocd_notifications
      auto_sync_enabled    = var.auto_sync_enabled

      # parameters passed to the Helm chart
      parameters = merge(
        {
          "env"                      = var.environment,
          "image.name"               = var.docker_image["name"]
          "image.tag"                = var.docker_image["tag"]
          "workloadIdentityClientId" = azurerm_user_assigned_identity.identity.client_id
          "secretStoreName"          = var.secret_store_name
          "command"                  = var.command
          "maxDuration"              = var.max_duration
          "maxReplicaCount"          = var.max_replica_count

          "serviceBus.namespace"        = var.service_bus["namespace"]
          "serviceBus.subscriptionName" = azurerm_servicebus_subscription.servicebus_data_jobs_subscription.name
          "serviceBus.topicName"        = var.service_bus["topic_name"]

          "pipelineManager.url"              = var.pipeline_manager["url"]
          "pipelineManager.apiKeySecretName" = var.pipeline_manager["api_key_secret_name"]

          "safeToEvict" = var.evictable
        },
        { for request_key, request_value in var.kubernetes.requests : "requests.${request_key}" => request_value },
        { for limit_key, limit_value in var.kubernetes.limits : "limits.${limit_key}" => limit_value },
        { for config_key, config_value in var.config : "config.${config_key}" => config_value },
        { for secret_name, secret_value in var.secret : "secret.${secret_name}" => secret_value }
      ),
      array_parameters = {}
    }
  )
)}
    EOF
]
}
