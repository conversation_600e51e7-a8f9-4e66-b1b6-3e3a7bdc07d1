# data job specific configurable options
variable "name" {
  description = "Data job's name"
  type        = string
  nullable    = false
  validation {
    condition     = can(regex("^[a-z0-9-]{1,63}$", var.name))
    error_message = "Name cannot be longer that 63 characters and can only contain lowercase alphanumeric characters and hyphen(s)."
  }
}

variable "kubernetes_namespace" {
  description = "Kubernetes namespace to which data job should be deployed"
  type        = string
  nullable    = false

}

variable "docker_image" {
  description = "Data job docker image configuration"
  type = object({
    name = string
    tag  = string
  })
}

variable "max_duration" {
  description = "Maximum duration of the job in seconds"
  type        = number
  default     = 3600
}

variable "config" {
  description = "Configuration parameters that will be passed to the Helm chart"
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "secret" {
  description = "Secret names that will be passed to the Helm chart"
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "command" {
  type        = string
  description = "Command that should be used when running image instead of default one"
  default     = null
}

variable "kubernetes" {
  description = <<EOT
  Kubernetes deployment configuration
  
  Resources documentation:
  https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
  {
    requests:
      cpu: "CPU requests"
      memory: "Memory requests"
    limits:
      cpu: "CPU limits"
      memory: "Memory limits"
  }
  EOT
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  nullable = false
}

variable "tags" {
  type = map(string)
}

# parameters passed from dependencies outputs
variable "service_bus" {
  description = "Configuration for the Service Bus managed by Pipeline Manager"
  type = object({
    namespace_id          = string
    namespace             = string
    topic_id              = string
    topic_name            = string
    deadletter_topic_name = string
  })
  nullable = false

  validation {
    # simple validation that checks number of parts in the ID, 1st first part is an empty string hence 9 instead of 8
    condition     = length(split("/", var.service_bus["namespace_id"])) == 9
    error_message = "Service Bus namespace ID must be in the form: /subscriptions/<subscription_id>/resourceGroups/<resource-group>/providers/Microsoft.ServiceBus/namespaces/<sb-namespace>"
  }
}

variable "pipeline_manager" {
  description = "Connection information for pipeline manager"
  type = object({
    url                 = string
    api_key_secret_name = string
  })
  nullable = false
}

# Argo CD specific variables
variable "chart_revision" {
  description = "Helm chart revision to use, default should point to latest stable"
  type        = string
  default     = "0.3.1"
}

variable "argocd_notifications" {
  type        = map(string)
  description = "Map of notification types and Teams channel names where notifications from Argo CD should be sent to"
}

# tenant/subscription specific global values
variable "tenant_id" {
  description = "Azure Tenant ID"
  type        = string
  nullable    = false
}

variable "location" {
  description = "Location of cluster and all related resources"
  type        = string
  default     = "West Europe"
  nullable    = false
}

variable "environment" {
  description = "Name of environment. It will be used by all cluster related resources by postfixing their names e.g. `-dev`"
  type        = string
  nullable    = false
}

variable "secret_store_name" {
  description = "Name of ESO Secret Store connected to the Key Vault"
  type        = string
  nullable    = false
}

variable "cluster_oidc_issuer_url" {
  description = "URL of Open ID provider URL needed to establish federated identity credential for Azure Workload Identity"
  type        = string
  nullable    = false
}

variable "max_replica_count" {
  description = "Maximum number of replicas of the data job"
  type        = number
  default     = 100
}

variable "auto_sync_enabled" {
  description = "Should automatic synchronization be enabled"
  type        = bool
  default     = true
}

variable "evictable" {
  description = "Can this job be evicted during cluster autoscaling"
  type        = bool
  default     = true
}
