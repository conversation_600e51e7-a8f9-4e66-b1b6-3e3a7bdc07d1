# Data job component

This component can be used to provision resources required by all
data jobs. Each data job is deployed in its own resource group
and gets access to Pipeline Manager queue which enables it to handle messages.
The component creates a Helm release of `data-job` chart (see bluebrick/indiebi/infra/helm-charts> repository),
with the following configuration/secrets passed through by default:

- `ENV` - environment name,
- `PIPELINE_MANAGER_URL` - URL to Pipeline Manager,
- `PIPELINE_MANAGER_KEY` - Pipeline Manager API key (passed through secret),
- `SERVICE_BUS_NAMESPACE` - Service Bus Namespace used by data jobs,
- `SERVICE_BUS_TOPIC_NAME` - Service Bus Topic used by data jobs.

Each data job also has Azure Identity configured that can be used
to assign additional permissions to the pods. The ID of the principal
for the provisioned identity can accessed through `identity_principal_id`
output.

Any additional configuration can be passed through `config`, those value are passed verbatim.
Similarly secrets can be passed by **name** through `secret` variable.

All data jobs require Kubernetes requests and limits configured, this can be done
through `kubernetes` parameter.

See `variables.tf` for list and descriptions of all available configuration options.
