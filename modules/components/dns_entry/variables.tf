
variable "name" {
  description = "Name of the DNS record. Has to be full name including subdomains e.g. backoffice.dev"
  type        = string
}

variable "dns_zone_name" {
  description = "Name of the DNS zone(s) to add the record to"
  type        = string
}

variable "dns_zone_resource_group_name" {
  description = "DNS zone resource group (in connectivity subscription)"
  type        = string
}

variable "ip" {
  description = "IP value of the entry"
  type        = string
}

variable "is_private" {
  description = "If true, add to private DNS zone only (named as dns_zone_name), if false, add to private and public"
  type        = bool
  default     = true
}

