
terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }
  }
}

resource "azurerm_private_dns_a_record" "private_a_record" {
  name                = var.name
  zone_name           = var.dns_zone_name
  resource_group_name = var.dns_zone_resource_group_name
  ttl                 = 300
  records             = [var.ip]

  provider = azurerm.connectivity
}


resource "azurerm_dns_a_record" "public_a_record" {
  count               = var.is_private ? 0 : 1
  name                = var.name
  zone_name           = var.dns_zone_name
  resource_group_name = var.dns_zone_resource_group_name
  ttl                 = 300
  records             = [var.ip]

  provider = azurerm.connectivity
}
