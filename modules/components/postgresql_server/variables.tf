variable "environment" {
  description = "Name of environment. It will be used by all cluster related resources by postfixing their names e.g. `-dev`"
  type        = string
  default     = "dev"
}

variable "location" {
  description = "Location of cluster and all related resources"
  type        = string
  default     = "West Europe"
}

variable "name" {
  description = "Name of the server that will be created."
  type        = string
}

variable "max_size_mb" {
  description = "Max storage allowed for a server."
  type        = number
}

variable "storage_tier" {
  description = "The name of storage performance tier for IOPS of the PostgreSQL Flexible Server."
  type        = string
}

variable "sku_name" {
  description = "Specifies the SKU Name for this PostgreSQL Server. The name of the SKU, follows the tier + family + cores pattern (e.g. B_Gen4_1, GP_Gen5_8)."
  type        = string
}

variable "key_vault_id" {
  description = "ID of the Key Vault used for storing secret values"
  type        = string
}

variable "tenant_id" {
  description = "Azure Tenant ID"
  type        = string
}

variable "tags" {
  type = map(string)
}

variable "postgresql_subnet_id" {
  description = "ID of the dedicated postgresql subnet"
  type        = string
}

variable "postgresql_dns_zone_id" {
  description = "Id of postgresql private DNS zones"
  type        = string
}

variable "server_password_expiration_date" {
  description = "Date when Key Vault secret containing SQL admin server password expires"
  type        = string
}

variable "databases" {
  description = <<EOT
  List of postgresql databases that will be provisioned on the server

  {
    workload_name: "Name of the workload that will use the database"
  }
  EOT

  type = list(object({
    workload_name = string
  }))
}

variable "entra_admin" {
  description = <<EOT
  Credential for the Entra ID principal that will be configured as a server admin

  {
    name: "Principal name"
    object_id: "Principal's Object ID"
  }
  EOT
  type = object({
    name      = string
    object_id = string
  })
}
