terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
  }
}

resource "azurerm_resource_group" "resource_group" {
  name     = "rg-${var.name}-${var.environment}"
  location = var.location
  tags     = var.tags
}

module "postgresql_admin_password" {
  source          = "git::ssh://**************/bluebrick/indiebi/infra/terraform-lib.git//terraform/key_vault_secret?ref=1.1.3"
  name            = "${var.name}-postgresql-admin-password"
  autogenerated   = true
  key_vault_id    = var.key_vault_id
  expiration_date = var.server_password_expiration_date
}

resource "azurerm_postgresql_flexible_server" "postgresql_server" {
  #checkov:skip=CKV_AZURE_136: "Ensure that PostgreSQL Flexible server enables geo-redundant backups - TODO"
  #checkov:skip=CKV2_AZURE_57: "Ensure that PostgreSQL Flexible is configured with private endpoint - TODO"
  name                          = "psql-${var.name}-${var.environment}"
  resource_group_name           = azurerm_resource_group.resource_group.name
  location                      = azurerm_resource_group.resource_group.location
  version                       = "16"
  delegated_subnet_id           = var.postgresql_subnet_id
  private_dns_zone_id           = var.postgresql_dns_zone_id
  public_network_access_enabled = false
  administrator_login           = "psqladmin"
  administrator_password        = module.postgresql_admin_password.secret_value
  storage_mb                    = var.max_size_mb
  storage_tier                  = var.storage_tier
  sku_name                      = var.sku_name
  authentication {
    active_directory_auth_enabled = true
  }
  tags = var.tags
  # https://github.com/hashicorp/terraform-provider-azurerm/issues/16888
  lifecycle {
    ignore_changes = [
      zone,
      high_availability.0.standby_availability_zone,
      authentication.0.tenant_id
    ]
    # prevent_destroy       = true
    create_before_destroy = true
  }
}

resource "azurerm_postgresql_flexible_server_active_directory_administrator" "admin_1" {
  server_name         = azurerm_postgresql_flexible_server.postgresql_server.name
  resource_group_name = azurerm_resource_group.resource_group.name
  tenant_id           = var.tenant_id
  object_id           = "bfaa9ba4-f9f4-4062-bfbd-01fc2a14278d" # TODO: from config
  principal_name      = "<EMAIL>"                  # TODO: from config
  principal_type      = "User"
  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_postgresql_flexible_server_active_directory_administrator" "admin_2" {
  server_name         = azurerm_postgresql_flexible_server.postgresql_server.name
  resource_group_name = azurerm_resource_group.resource_group.name
  tenant_id           = var.tenant_id
  object_id           = "138569dc-1ed7-4645-a47d-4c9a942bf18d" # TODO: from config
  principal_name      = "<EMAIL>"        # TODO: from config
  principal_type      = "User"
  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_postgresql_flexible_server_active_directory_administrator" "admin_3" {
  server_name         = azurerm_postgresql_flexible_server.postgresql_server.name
  resource_group_name = azurerm_resource_group.resource_group.name
  tenant_id           = var.tenant_id
  object_id           = var.entra_admin.object_id
  principal_name      = var.entra_admin.name
  principal_type      = "ServicePrincipal"
  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_postgresql_flexible_server_database" "databases" {
  for_each  = toset(var.databases[*].workload_name)
  name      = "psql-${replace(each.value, "_", "-")}"
  server_id = azurerm_postgresql_flexible_server.postgresql_server.id
  charset   = "UTF8"
  collation = "en_US.utf8"

  # prevent the possibility of accidental data loss
  # lifecycle {
  #   prevent_destroy = true
  # }
}
