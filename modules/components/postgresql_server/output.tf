output "databases" {
  value = {
    for workload_name, database in azurerm_postgresql_flexible_server_database.databases : workload_name => {
      name = database.name
    }
  }
}

output "server_name" {
  value = azurerm_postgresql_flexible_server.postgresql_server.name
}
output "server_fqdn" {
  value = azurerm_postgresql_flexible_server.postgresql_server.fqdn
}

output "server_resource_group" {
  value = azurerm_resource_group.resource_group.name
}

output "server_admin" {
  value = {
    username = azurerm_postgresql_flexible_server.postgresql_server.administrator_login
    password = azurerm_postgresql_flexible_server.postgresql_server.administrator_password
  }
  sensitive = true
}
