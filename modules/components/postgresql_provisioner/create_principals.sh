#!/usr/bin/env bash
set -o errexit
set -o pipefail

az login --identity
access_token=`az account get-access-token --resource-type oss-rdbms --output tsv --query accessToken`

IFS=';' read -ra users <<< "$PRINCIPALS"

for principal in "${users[@]}"; do
    principal_object_id=`echo $principal | cut -d: -f1`
    principal_name=`echo $principal | cut -d: -f2`

    if [[ "$principal_name" == *"@"* ]]; then
        principal_type="user"
    elif [[ "$principal_name" == "id-"* || "$principal_name" == "sp-"* ]]; then
        principal_type="service"
    else
        principal_type="group"
    fi

    echo "DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = '${principal_name}') THEN
            PERFORM pg_catalog.pgaadauth_create_principal_with_oid('${principal_name}', '${principal_object_id}', '${principal_type}', false, false);
        END IF;
    END \$\$;" | PGPASSWORD=$access_token psql
done
