variable "database" {
  description = <<EOT
  Database information
  
  {
    name: "Name of the database"
    host: "Database host"
  }
  EOT
  type = object({
    name = string
    host = string
  })
}

variable "server_admin" {
  description = <<EOT
  Credentials of the database server traditional admin
  
  {
    username: "Admin username"
    password: "Admin password"
  }
  EOT
  type = object({
    username = string
    password = string
  })
  sensitive = true
}

variable "entra_admin" {
  description = "Entra ID PosgreSQL server admin account"
  type = object({
    object_id = string
    name      = string
  })
}

variable "admins" {
  description = <<EOT
  Identities or Service principals for which admin user will be created

  list({
    id: "Identity/Service principal ID"
    name: "Name of the identity or service principal
  })
  EOT
  type = list(object({
    object_id = string
    name      = string
  }))
  default = []
}

variable "entra_users" {
  description = <<EOT
  Identities or Service principals for additional users that will be added to the database
  from Entra ID, these users do not get any grants, they need to be added by the app

  list({
    id: "Identity/Service principal ID"
    name: "Name of the identity or service principal
  })
  EOT
  type = list(object({
    object_id = string
    name      = string
  }))
  default = []
}

variable "schema" {
  description = "Name of the schema that will be created in the database"
  type        = string
  default     = "web_app"
}
