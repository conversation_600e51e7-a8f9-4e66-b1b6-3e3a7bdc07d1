resource "terraform_data" "create_grant_entra_admin_provisioner" {
  triggers_replace = [
    var.database,
    var.entra_admin.name
  ]

  provisioner "local-exec" {
    command = "psql -c 'GRANT CREATE ON DATABASE \"${var.database.name}\" TO \"${var.entra_admin.name}\"'"
    environment = {
      PGUSER     = var.server_admin.username
      PGPASSWORD = var.server_admin.password
      PGDATABASE = "postgres"
      PGHOST     = var.database.host
    }
  }
}

resource "terraform_data" "create_principals_provisioner" {
  triggers_replace = [
    var.database,
    var.admins,
    var.entra_admin,
    var.entra_users
  ]

  provisioner "local-exec" {
    command = "${path.module}/create_principals.sh"
    environment = {
      PGUSER     = var.entra_admin.name
      PGDATABASE = "postgres"
      PGHOST     = var.database.host
      PRINCIPALS = join(";", toset([for identity in concat(var.admins, [var.entra_admin], var.entra_users) : "${identity.object_id}:${identity.name}"]))
    }
  }

  depends_on = [
    terraform_data.create_grant_entra_admin_provisioner
  ]
}

resource "terraform_data" "create_schema_provisioner" {
  triggers_replace = [
    var.database,
    var.schema,
    var.entra_admin,
    var.admins
  ]

  provisioner "local-exec" {
    command = "${path.module}/create_schema.sh"
    environment = {
      PGUSER     = var.entra_admin.name
      PGDATABASE = var.database.name
      PGHOST     = var.database.host
      SCHEMA     = var.schema
      ADMINS     = join(";", toset([for identity in concat(var.admins, [var.entra_admin]) : identity.name]))
    }
  }

  depends_on = [
    terraform_data.create_principals_provisioner
  ]
}
