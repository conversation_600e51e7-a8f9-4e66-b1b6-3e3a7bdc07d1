#!/usr/bin/env bash
set -o errexit
set -o pipefail

az login --identity
access_token=`az account get-access-token --resource-type oss-rdbms --output tsv --query accessToken`

indiebi_parent_role="indiebi_parent_role"

echo "DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = '${indiebi_parent_role}') THEN
            CREATE ROLE ${indiebi_parent_role};
        END IF;
    END \$\$;" | PGPASSWORD=$access_token psql

IFS=';' read -ra admin_names <<< "$ADMINS"

for user_name in "${admin_names[@]}"; do
    PGPASSWORD=$access_token psql -c "GRANT ${indiebi_parent_role} TO \"${user_name}\""
done

echo "CREATE SCHEMA IF NOT EXISTS ${SCHEMA};
    ALTER SCHEMA ${SCHEMA} OWNER TO \"${indiebi_parent_role}\";
    GRANT USAGE, CREATE ON SCHEMA ${SCHEMA} TO \"${indiebi_parent_role}\";" \
| PGPASSWORD=$access_token psql
