resource "azurerm_consumption_budget_resource_group" "resource_group_budget" {
  name              = "${var.resource_group_name}-budget"
  resource_group_id = var.resource_group_id

  amount     = 5
  time_grain = "Monthly"

  time_period {
    start_date = formatdate("YYYY-MM-01'T'00:00:00Z", timestamp())
  }

  notification {
    enabled        = true
    threshold      = 100.0
    operator       = "GreaterThan"
    threshold_type = "Forecasted"

    contact_emails = [
      "${var.budget_alerts_contact}"
    ]
  }

  # if budget already exists we don't need to update it's start_date
  lifecycle {
    ignore_changes = [
      time_period
    ]
  }

  count = var.create_cost_alert ? 1 : 0
}
