variable "environment" {
  description = "Name of environment. It will be used by all cluster related resources by postfixing their names e.g. `-dev`"
  type        = string
  default     = "dev"
}

variable "location" {
  description = "Location of cluster and all related resources"
  type        = string
  default     = "West Europe"
}

variable "workload_name" {
  description = "Name of the workload that will be created."
  type        = string
}

variable "budget_alerts_contact" {
  description = "Contact e-mail that will receive budget alerts"
  type        = string
  default     = "<EMAIL>"
}

variable "create_cost_alert" {
  description = "If true, budget cost alert is created for the resource group"
  type        = bool
  default     = false
}

variable "resource_group_name" {
  description = "Resource group name for Azure resources required by KEDA"
  type        = string
}

variable "resource_group_id" {
  description = "ID of resource group for Azure resources required by KEDA"
  type        = string
}
