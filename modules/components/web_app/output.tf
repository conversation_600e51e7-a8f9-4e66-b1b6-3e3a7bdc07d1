output "identity" {
  value = {
    name         = azurerm_user_assigned_identity.identity.name
    id           = azurerm_user_assigned_identity.identity.id
    principal_id = azurerm_user_assigned_identity.identity.principal_id
  }
}

output "name" {
  value = var.name
}

// TODO delete once SaaS migrates to new electron api url
output "firewall_policy_id" {
  value = length(azurerm_web_application_firewall_policy.web_app_waf_policy) > 0 ? azurerm_web_application_firewall_policy.web_app_waf_policy[0].id : null
}
