variable "name" {
  description = "Web app's name"
  type        = string
  nullable    = false
  validation {
    condition     = can(regex("^[a-z0-9-]{1,63}$", var.name))
    error_message = "Name cannot be longer that 63 characters and can only contain lowercase alphanumeric characters and hyphen(s)."
  }
}

variable "kubernetes_namespace" {
  description = "Kubernetes namespace to which web app should be deployed"
  type        = string
  nullable    = false

}

variable "environment" {
  description = "Name of environment. It will be used by all cluster related resources by postfixing their names e.g. `-dev`"
  type        = string
  default     = "dev"
  nullable    = false
}

variable "tenant_id" {
  description = "Azure Tenant ID"
  type        = string
  nullable    = false
}

variable "location" {
  description = "Location of cluster and all related resources"
  type        = string
  nullable    = false
}

variable "resource_group_name" {
  description = "Name of the resource group where web app's resources will be provisioned"
  type        = string
  nullable    = false
}

variable "docker_image" {
  description = "Data job docker image configuration"
  type = object({
    name = string
    tag  = string
  })
}

variable "secret_store_name" {
  description = "Name of ESO Secret Store connected to the Key Vault"
  type        = string
  nullable    = false
}

variable "cluster_oidc_issuer_url" {
  description = "URL of Open ID provider URL needed to establish federated identity credential for Azure Workload Identity"
  type        = string
  nullable    = false
}

variable "kubernetes" {
  description = <<EOT
  Kubernetes deployment configuration
  
  Resources documentation:
  https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
  {
    requests:
      cpu: "CPU requests"
      memory: "Memory requests"
    limits:
      cpu: "CPU limits"
      memory: "Memory limits"
  }
  EOT
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  nullable = false
}

variable "config" {
  description = "Configuration parameters that will be passed to the Helm chart"
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "secret" {
  description = "Secret names that will be passed to the Helm chart"
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "before_deployment" {
  description = "Command for the Argo CD pre-sync hoook"
  type = object({
    command = string
    args    = string
  })
  default = null
}

variable "domain" {
  type        = string
  description = "Environment domain name to configure web app's address"
  nullable    = false
}

variable "chart_revision" {
  description = "Helm chart revision to use, default should point to latest stable"
  type        = string
  default     = "0.15.6"
}

variable "argocd_notifications" {
  type        = map(string)
  description = "Map of notification triggers and Teams channel names where notifications from Argo CD should be sent to"
}

variable "server_port" {
  type        = string
  description = "Server on which the web app will run"
  default     = 8000
}

variable "healthcheck_path" {
  type        = string
  description = "Route to ping the service with for healthcheck"
  default     = "/health"
}

variable "healthcheck_timeout_seconds" {
  type        = number
  description = "Timeout, in seconds, for web app's probes"
  default     = 1
}

variable "liveness_healthcheck_initial_delay_seconds" {
  type        = number
  description = "Initial delay, in seconds, for web app's liveness probes"
  default     = 15
}

variable "liveness_healthcheck_period_seconds" {
  type        = number
  description = "Period, in seconds, for web app's liveness probes"
  default     = 10
}

variable "liveness_healthcheck_failure_threshold" {
  type        = number
  description = "Number of times liveness probe can fail before web app restart"
  default     = 3
}

variable "readiness_healthcheck_initial_delay_seconds" {
  type        = number
  description = "Initial delay, in seconds, for web app's readiness probes"
  default     = 5
}

variable "readiness_healthcheck_period_seconds" {
  type        = number
  description = "Period, in seconds, for web app's readiness probes"
  default     = 5
}

variable "readiness_healthcheck_failure_threshold" {
  type        = number
  description = "Number of times readiness probe can fail before web app stops accepting new http requests"
  default     = 3
}

variable "termination_grace_period_seconds" {
  type        = number
  description = "Grace period during pod termination. Does not apply to Forced termination"
  default     = 60
}

variable "pre_stop_sleep_seconds" {
  type        = number
  description = "Extra time for finishing current requests and giving application gateway time to remove pod from backend pools"
  # Microsoft recommends this value to be 90sec, but from our experiences 30sec should be enough and we do not
  # want to needlessly increase the time it takes to terminate old pod after deployment
  # https://azure.github.io/application-gateway-kubernetes-ingress/how-tos/minimize-downtime-during-deployments/
  default = 30
}

variable "command" {
  type        = string
  description = "Command that should be used when running image instead of default one"
  default     = null
}

variable "is_private_webapp" {
  type    = bool
  default = true
}

variable "dns_config" {
  description = "Configuration of DNS entry for web app"
  type = object({
    dns_zone_name                = string
    dns_zone_resource_group_name = string
    public_ip                    = string
    private_ip                   = string
  })
  nullable = false
}

variable "env_dns_extension" {
  type        = string
  description = "Custom postfix used for DNS names to distinguish envs different than dev/prod"
}

variable "replicas" {
  description = "Number of Pod replicas that should be created on the cluster"
  type        = number
  default     = 1
}

variable "priority_class_name" {
  description = "Name of the pod priority class used for the deployment"
  type        = string
  default     = "normal-priority"
}

variable "firewall_policy" {
  description = <<EOT
  Configuration of firewall policy. Allows to set firewall policy mode and 
  list of policy and rules IDs that should be overriden with Log action
  instead of prevention. Some web apps cause false positives in firewall
  detection which blocks the traffic, this setting allows adding
  exceptions. Setting it will create a dedicated policy for the web app.

  List of rule groups:
  https://learn.microsoft.com/en-us/azure/web-application-firewall/ag/application-gateway-crs-rulegroups-rules?tabs=drs21#owasp-crs-32
  {
    mode: "Firewall operation mode, either `Detection` or `Prevention`"
    [
      {
        rule_group_name: "Name of the WAF rule group that should be overriden, 
                          e.g.: `REQUEST-942-APPLICATION-ATTACK-SQLI`"
        rule_ids: "IDs of the rules that should be overriden, e.g. `942200`"
      }
    ]
  }
  EOT
  type = object({
    mode = optional(string, "Prevention")
    overrides = optional(list(object({
      rule_group_name = string
      rule_ids        = list(string)
    })), [])
  })
  default = null

  validation {
    condition     = var.firewall_policy == null ? true : (var.firewall_policy.mode == "Prevention" || var.firewall_policy.mode == "Detection")
    error_message = "Firewall policy mode can be either `Prevention` or `Detection`"
  }
}

variable "prometheus_monitor_enabled" {
  description = "Flag to enable exporting metrics to prometheus"
  type        = bool
  default     = false
}

variable "custom_dns_entries" {
  description = "DNS names to be applied in DNS Zones and hostnames to be used in Ingress instead of the default workload_name.domain combination"
  type = list(object({
    dns_name = string
    hostname = string
  }))
  default = null
}

variable "tags" {
  type = map(string)
}

variable "automatic_configuration_reload_enabled" {
  description = "Automatically reload web app configuration on change"
  type        = bool
  default     = true
}

variable "request_timeout_seconds" {
  description = "Timeout for the HTTP request"
  type        = number
  default     = 30
}

variable "auto_sync_enabled" {
  description = "Should automatic synchronization be enabled"
  type        = bool
  default     = true
}

variable "hpa" {
  description = <<EOT
  Horizontal Pod Autoscaler configuration, takes precedence over `replicas` setting.
  Metric's target specifies percentage threshold of the resource request that will trigger scaling.
  EOT

  type = object({
    min_replicas = number
    max_replicas = number
    metrics = list(object({
      resource = string
      target   = number
    }))
  })
  default = null

  validation {
    condition     = var.hpa == null ? true : var.hpa.min_replicas > 0
    error_message = "Minimum number of replicas must be greater that 0"
  }

  validation {
    condition     = var.hpa == null ? true : var.hpa.max_replicas > 0 && var.hpa.max_replicas > var.hpa.min_replicas
    error_message = "Maximum number of replicas must be greater that 0 and `min_replicas`"
  }

  validation {
    condition     = var.hpa == null ? true : alltrue([for index, metric in var.hpa.metrics : contains(["cpu", "memory"], metric.resource)])
    error_message = "Only `cpu` and `memory` resources are supported"
  }

  validation {
    condition     = var.hpa == null ? true : alltrue([for index, metric in var.hpa.metrics : metric.target > 0 && metric.target <= 100])
    error_message = "Metric target must be between (0, 100]"
  }
}

variable "private_routes" {
  description = <<EOT
  List of routes that should only be accessible from private network when the application is public,
  `/docs`, `/openapi.json` and `/health` endpoints are always blocked
  EOT

  type    = list(string)
  default = []
}

variable "private_routes_allowed_ips" {
  description = "IP addresses that are allowed to access private endpoints, supports CIDR - *********/16"
  type        = list(string)
  default     = null

  validation {
    condition = (
      var.is_private_webapp ||
      (!var.is_private_webapp && var.private_routes_allowed_ips != null)
    )
    error_message = <<EOT
    Public apps need `private_routes_allowed_ips` to be provided
    EOT
  }
}

variable "default_private_routes" {
  description = <<EOT
  Private routes blocked by default, defined as variable for convenience, use `private_routes` instead
  EOT

  type    = list(string)
  default = ["/docs", "/openapi.json", "^/health$"]
}
