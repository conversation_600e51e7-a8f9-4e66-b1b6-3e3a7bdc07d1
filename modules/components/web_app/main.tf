terraform {
  required_providers {
    azurerm = {
      source                = "hashicorp/azurerm"
      version               = "~> 4.0"
      configuration_aliases = [azurerm.connectivity]
    }

    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
  }
}

resource "azurerm_user_assigned_identity" "identity" {
  resource_group_name = var.resource_group_name
  location            = var.location
  name                = "id-${var.name}-${var.environment}"
  tags                = var.tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_federated_identity_credential" "federated_identity_credential" {
  name                = "fic-${var.name}-${var.environment}"
  resource_group_name = var.resource_group_name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.identity.id
  subject             = "system:serviceaccount:${var.kubernetes_namespace}:${var.name}-service-account"
}

resource "azurerm_federated_identity_credential" "federated_identity_credential_presync" {
  count               = var.before_deployment != null ? 1 : 0
  name                = "fic-${var.name}-presync-${var.environment}"
  resource_group_name = var.resource_group_name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.identity.id
  subject             = "system:serviceaccount:${var.kubernetes_namespace}:${var.name}-presync-service-account"
}

resource "azurerm_web_application_firewall_policy" "web_app_waf_policy" {
  count               = var.firewall_policy != null ? 1 : 0
  name                = "waf-policy-${var.name}-${var.environment}"
  resource_group_name = var.resource_group_name
  location            = var.location
  tags                = var.tags

  lifecycle {
    create_before_destroy = true
  }

  policy_settings {
    enabled                     = true
    mode                        = var.firewall_policy.mode
    request_body_check          = true
    file_upload_limit_in_mb     = 50
    max_request_body_size_in_kb = 1024
  }
  dynamic "custom_rules" {
    for_each = var.is_private_webapp ? [] : [1]
    content {
      name      = "BlockPublicAccessToPrivateEndpoints"
      priority  = 1
      rule_type = "MatchRule"
      action    = "Block"

      match_conditions {
        match_variables {
          variable_name = "RequestUri"
        }
        operator           = "Regex"
        negation_condition = false
        match_values       = tolist(toset(concat(var.private_routes, var.default_private_routes)))
      }

      match_conditions {
        match_variables {
          variable_name = "RemoteAddr"
        }
        operator           = "IPMatch"
        negation_condition = true
        match_values       = var.private_routes_allowed_ips
      }
    }
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"

      dynamic "rule_group_override" {
        for_each = var.firewall_policy.overrides
        content {
          rule_group_name = rule_group_override.value.rule_group_name

          dynamic "rule" {
            for_each = rule_group_override.value.rule_ids
            content {
              id      = rule.value
              action  = "Log"
              enabled = true
            }
          }
        }
      }
    }
  }
}


locals {
  dns_names = var.custom_dns_entries != null ? [for dns_entry in var.custom_dns_entries : dns_entry.dns_name] : ["${var.name}${var.env_dns_extension != null ? var.env_dns_extension : ""}"]
  hostnames = var.custom_dns_entries != null ? [for dns_entry in var.custom_dns_entries : dns_entry.hostname] : ["${var.name}.${var.domain}"]
}

resource "helm_release" "argocd-app" {
  name        = "${var.name}-argocd-app"
  chart       = "../components/kubernetes-resource"
  namespace   = var.kubernetes_namespace
  max_history = 5

  lifecycle {
    create_before_destroy = true
  }

  values = [
    <<-EOF
    configurations:
      - ${indent(4, templatefile("../components/templates/app.yaml.tpl",
    {
      environment          = var.environment,
      name                 = var.name
      namespace            = var.kubernetes_namespace
      chart_name           = "web-app"
      chart_revision       = var.chart_revision
      image_name           = var.docker_image.name
      image_tag            = var.docker_image.tag
      argocd_notifications = var.argocd_notifications
      auto_sync_enabled    = var.auto_sync_enabled

      # parameters passed to the Helm chart
      parameters = merge(
        {
          "env"                                       = var.environment,
          "image.name"                                = var.docker_image.name
          "image.tag"                                 = var.docker_image.tag
          "workloadIdentityClientId"                  = azurerm_user_assigned_identity.identity.client_id
          "secretStoreName"                           = var.secret_store_name
          "serverPort"                                = var.server_port
          "healthCheck.path"                          = var.healthcheck_path
          "healthCheck.timeoutSeconds"                = var.healthcheck_timeout_seconds
          "healthCheck.liveness.initialDelaySeconds"  = var.liveness_healthcheck_initial_delay_seconds
          "healthCheck.liveness.periodSeconds"        = var.liveness_healthcheck_period_seconds
          "healthCheck.liveness.failureThreshold"     = var.liveness_healthcheck_failure_threshold
          "healthCheck.readiness.initialDelaySeconds" = var.readiness_healthcheck_initial_delay_seconds
          "healthCheck.readiness.periodSeconds"       = var.readiness_healthcheck_period_seconds
          "healthCheck.readiness.failureThreshold"    = var.readiness_healthcheck_failure_threshold
          "terminationGracePeriodSeconds"             = var.termination_grace_period_seconds
          "preStopSleepSeconds"                       = var.pre_stop_sleep_seconds
          "command"                                   = var.command
          "deploymentStrategy.replicas"               = var.replicas
          "ingressConfig.isPrivate"                   = var.is_private_webapp
          "ingressConfig.wafPolicyId"                 = var.firewall_policy != null ? azurerm_web_application_firewall_policy.web_app_waf_policy[0].id : null
          "ingressConfig.requestTimeout"              = var.request_timeout_seconds
          "priorityClassName"                         = var.priority_class_name
          "prometheusMonitorEnabled"                  = var.prometheus_monitor_enabled
          "automaticConfigurationReloadEnabled"       = var.automatic_configuration_reload_enabled
        },
        { for request_key, request_value in var.kubernetes.requests : "requests.${request_key}" => request_value },
        { for limit_key, limit_value in var.kubernetes.limits : "limits.${limit_key}" => limit_value },
        { for config_key, config_value in var.config : "config.${config_key}" => config_value },
        { for secret_name, secret_value in var.secret : "secret.${secret_name}" => secret_value },
        var.before_deployment != null ? {
          "runBeforeDeployment.command" = var.before_deployment.command
          "runBeforeDeployment.args"    = var.before_deployment.args
        } : {},
        var.hpa != null ? merge(
          {
            "hpa.minReplicas" = var.hpa.min_replicas
            "hpa.maxReplicas" = var.hpa.max_replicas
          },
          { for index, metric in var.hpa.metrics : "hpa.metrics[${index}].resource" => metric.resource },
          { for index, metric in var.hpa.metrics : "hpa.metrics[${index}].target" => metric.target }
        ) : {}
      )
      array_parameters = {
        "hostnames" = join(" ", local.hostnames),
      }
  })
)}
    EOF
]
}


module "dns_entry" {
  for_each                     = toset(local.dns_names)
  source                       = "../dns_entry"
  name                         = each.value
  dns_zone_resource_group_name = var.dns_config.dns_zone_resource_group_name
  dns_zone_name                = var.dns_config.dns_zone_name
  ip                           = var.is_private_webapp ? var.dns_config.private_ip : var.dns_config.public_ip
  is_private                   = var.is_private_webapp

  providers = {
    azurerm.connectivity = azurerm.connectivity
  }
}

