resource "azurerm_user_assigned_identity" "eso_identity" {
  name                = "id-eso-${var.environment}"
  resource_group_name = var.key_vault.resource_group_name
  location            = var.location
  tags                = var.tags
}

resource "azurerm_role_assignment" "eso_role_assignment" {
  scope                = var.key_vault.id
  role_definition_name = "Key Vault Secrets User"
  principal_id         = azurerm_user_assigned_identity.eso_identity.principal_id
}

locals {
  service_account_name = "service-account-eso-${var.environment}"
}

resource "kubernetes_service_account" "service_account" {
  metadata {
    name      = local.service_account_name
    namespace = var.kubernetes_namespace
    annotations = {
      "azure.workload.identity/client-id" = azurerm_user_assigned_identity.eso_identity.client_id,
      "azure.workload.identity/tenant-id" = var.tenant_id
    }
    labels = {
      "azure.workload.identity/use" = "true"
    }
  }
}

resource "azurerm_federated_identity_credential" "federated_identity_credential" {
  name                = "fic-eso-${var.environment}"
  resource_group_name = var.key_vault.resource_group_name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.cluster_oidc_issuer_url
  parent_id           = azurerm_user_assigned_identity.eso_identity.id
  subject             = "system:serviceaccount:${var.kubernetes_namespace}:${local.service_account_name}"
}


resource "helm_release" "secret_store" {
  name      = var.secret_store_name
  chart     = "../components/kubernetes-resource"
  namespace = var.kubernetes_namespace

  values = [
    <<-EOF
    configurations:
      - ${indent(4, templatefile("../components/external_secrets/secret-store.yaml.tpl", { vault_url = var.key_vault.uri, service_account = local.service_account_name, secret_store_name = var.secret_store_name }))}
    EOF
  ]
}
