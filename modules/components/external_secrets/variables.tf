variable "tenant_id" {
  description = "Azure tenant ID"
  type        = string
}

variable "environment" {
  description = "Name of environment. It will be used by all cluster related resources by postfixing their names e.g. `-dev`"
  type        = string
  default     = "dev"
}

variable "location" {
  description = "Location of cluster and all related resources"
  type        = string
  default     = "West Europe"
}

variable "cluster_oidc_issuer_url" {
  description = "URL of Open ID provider URL needed to establish federated identity credential for Azure Workload Identity"
  type        = string
}

variable "key_vault" {
  description = "Configuration for key vault used by ESO"
  type = object({
    id                  = string
    uri                 = string
    resource_group_name = string
  })
}

variable "kubernetes_namespace" {
  description = "Kubernetes namespace where External Secrets Operator is installed"
  type        = string
}

variable "secret_store_name" {
  description = "Name of the helm release and ClusterSecretStore object on the cluster"
  type        = string
}

variable "tags" {
  description = "Tags for the created resources"
  type        = map(string)
  default     = {}
}
