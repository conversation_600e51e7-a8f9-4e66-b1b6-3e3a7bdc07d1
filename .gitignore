# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

*tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore output from <PERSON>ov ran locally
checkov-result.txt
# Ignore local test env vars
vars-local/*.tfvars
# Ignore Terragrunt autogenerated configs
**/backend.tf
**/provider.tf

**/.terragrunt-cache
**/terragrunt_rendered.json

**/__pycache__/

# IDE files
.idea/
